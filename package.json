{"version": "1.0.0", "scripts": {"postinstall": "patch-package", "sync-applications": "node scripts/sync-node-bff-applications.mjs"}, "dependencies": {"@bufbuild/protobuf": "^2.6.0", "@protobuf-ts/grpcweb-transport": "^2.9.5", "@protobuf-ts/plugin": "^2.9.5", "@protobuf-ts/runtime": "^2.9.5"}, "devDependencies": {"@bufbuild/protoc-gen-es": "^2.7.0", "glob": "^11.0.1", "js-yaml": "^4.1.0", "patch-package": "^8.0.0", "protobufjs": "^7.4.0", "ts-morph": "^25.0.1", "ts-proto": "^2.7.7", "typescript": "^5.8.2"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}