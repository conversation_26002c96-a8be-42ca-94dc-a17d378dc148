spring:
  config:
    import:
      - "aws-secretsmanager:moego/production/datasource?prefix=secret.datasource."
  datasource:
    url: jdbc:mysql//${secret.datasource.mysql.url.master}:${secret.datasource.mysql.port}/moe_business?useUnicode=true&characterEncoding=utf8&allowMultiQueries=true&useSSL=false
    username: ${secret.datasource.mysql.username}
    password: ${secret.datasource.mysql.password}
moego:
  data-sources:
    - name: reader
      url: jdbc:mysql://${secret.datasource.mysql.url.reader}:${secret.datasource.mysql.port}/moe_business?useUnicode=true&characterEncoding=utf8&allowMultiQueries=true&useSSL=false
      username: ${secret.datasource.mysql.username}
      password: ${secret.datasource.mysql.password}

springdoc:
  api-docs:
    enabled: false
  swagger-ui:
    enabled: false
