server:
  port: ${SERVER_PORT:9203}

spring:
  application:
    name: moego-server-business
  profiles:
    active: ${SPRING_PROFILES_ACTIVE:local}
  servlet:
    multipart:
      max-file-size: 32MB
      max-request-size: 32MB
  freemarker:
    prefer-file-system-access: false
  data:
    redis:
      host: ${REDIS_HOST:cache-main.t2.moego.pet}
      password: ${REDIS_PASSWORD:hnW6da5EfB2eVArf8JMKIste0CTJx7lRkW2YoM0Kb0w=}
      port: ${REDIS_PORT:40179}
      timeout: ${REDIS_TIMEOUT:60000}
      key:
        delimiter: ${REDIS_KEY_DELIMITER::}
        prefix: ${REDIS_KEY_PREFIX:local}
      ssl:
        enabled: ${REDIS_SSL_ENABLED:false}

business:
  register:
    inform:
      email: ${REGISTER_INFORM_EMAIL:<EMAIL>}

log:
  path: ${LOG_PATH:./logs}

mybatis:
  mapper-locations: classpath*:/com/moego/server/business/mapperxml/*.xml
  type-aliases-package: com.moego.server.business.mapperbean

pagehelper:
  helperDialect: mysql
  page-size-zero: true
  params: count=countSql
  reasonable: true
  supportMethodsArguments: true

s3:
  asset:
    private:
      bucket: ${S3_PRIVATE_BUCKET:moego-private-assets-test}
    signature:
      prefix: ${S3_SIG_PREFIX:signature/}
  domain: ${S3_DOMAIN:https://moegonew.s3-us-west-2.amazonaws.com/}
  image:
    public:
      bucket: ${S3_PUBLIC_BUCKET:moegonew}
      key:
        prefix: ${S3_PUBLIC_KEY_PREFIX:Public/Uploads/}
  #新配置项
  public:
    domain: ${S3_DOMAIN:https://moegonew.s3-us-west-2.amazonaws.com/}
    bucket: ${S3_PUBLIC_BUCKET:moegonew}
    prefix:
      exportFile: ${S3_PUBLIC_EXPORT_FILE:export/}
  key: ${S3_KEY:********************}
  region: us-west-2
  secret: ${S3_SECRET:C68h5Eb7ZaP/TqD11+OGc2NsTTpMXYoq+7PZiAiR}

sentry:
  dsn: ${SENTRY_DSN:https://<EMAIL>/5}
  environment: ${spring.profiles.active}

staff:
  init:
    first:
      name: ${STAFF_INIT_FIRSTNAME:Demo}
    last:
      name: ${STAFF_INIT_LASTNAME:Staff}

referral:
  referee:
    invoicePaidCount: ${REFERRAL_REFEREE_INVOICE_PAID_COUNT:3}
    rewardAmountOff: ${REFERRAL_REFEREE_REWARD_AMOUNT_OFF:0}
    rewardPercentOff: ${REFERRAL_REFEREE_REWARD_PERCENT_OFF:30.00}
    slackBonusClaimUrl: ${SLACK_BONUS_CLAIM_URL:/T011CF3CMJN/A03MYSG6RCY/414051232164888380/KZsMI0foD4IOfA3OtFX5hxoN}
    slackWorkflowsBaseUrl: ${SLACK_WORKFLOWS_BASE_URL:https://hooks.slack.com/workflows}
    successMinThreshold: ${REFERRAL_REFEREE_SUCCESS_MIN_THRESHOLD:10080}

moego:
  server:
    url:
      customer: ${CUSTOMER_SERVER_URL:http://moego-service-customer:9201}
      grooming: ${GROOMING_SERVER_URL:http://moego-service-grooming:9206}
      message: ${MESSAGE_SERVER_URL:http://moego-service-message:9205}
      payment: ${PAYMENT_SERVER_URL:http://moego-service-payment:9204}
      retail: ${RETAIL_SERVER_URL:http://moego-service-retail:9207}
  grpc:
    client:
      stubs:
        - service: moego.service.sms.**
          authority: moego-svc-sms:9090
        - service: moego.service.metadata.**
          authority: moego-svc-metadata:9090
        - service: moego.service.account.**
          authority: moego-svc-account:9090
        - service: moego.service.agreement.**
          authority: moego-svc-agreement:9090
        - service: moego.service.organization.**
          authority: moego-svc-organization:9090
        - service: moego.service.business_customer.**
          authority: moego-svc-business-customer:9090
        - service: moego.service.permission.**
          authority: moego-svc-permission:9090
        - service: moego.service.organization.**
          authority: moego-svc-organization:9090
        - service: moego.service.enterprise.**
          authority: moego-svc-enterprise:9090
        - service: moego.service.order.**
          authority: moego-svc-order:9090
        - service: moego.service.smart_scheduler.**
          authority: moego-svc-smart-scheduler:9090
        - service: moego.service.appointment.**
          authority: moego-svc-appointment:9090
        - service: moego.service.online_booking.**
          authority: moego-svc-online-booking:9090
  session:
    moego-pay-max-age: 86400
    moego-pay-mobile-max-age: 2592000
    sources:
      - name: customer
        cookie-name: MGSID-C-T2
        legacy-cookie-names:
          - MGSID-T2
        domains:
          - pattern: '^([^.]+-grey-)?my\.t2\.moego\.pet$'
            cookie-target-domain-level: 3
        max-age: 2592000
      - name: ob
        cookie-name: MGSID-OB-T2
        domains:
          - pattern: '^([^.]+-grey-)?(form|booking)\.t2\.moego\.pet$'
            cookie-target-domain-level: 3
            # xxx-grey-xxx.t2.moego.online
          - pattern: '^[^.]+\.t2\.moego\.online$'
            cookie-target-domain-level: 4
        max-age: 2592000
      - name: mis
        cookie-name: MGSID-MIS-T2
        domains:
          - pattern: '^([^.]+-grey-)?mis\.t2\.moego\.pet$'
            cookie-target-domain-level: 3
        max-age: 2592000
      - name: business
        cookie-name: MGSID-B-T2
        domains:
          - pattern: '^.*$'
            cookie-target-domain-level: 3
        max-age: 2592000
  posthog:
    api-key: ${POSTHOG_API_KEY:phc_hH0zrnA2tp7s5Vyv1ktl4Ht9exe6UQfPbUvv1ORgv80}
    api-host: ${POSTHOG_API_HOST:https://app.posthog.com}
  invite-staff:
    sign-in: ${INVITE_STAFF_SIGN_IN_URL:https://go.t2.moego.pet/sign_in?inviteCode={0}}
    sign-up: ${INVITE_STAFF_SIGN_UP_URL:https://go.t2.moego.pet/sign_up?inviteCode={0}}

springdoc:
  packages-to-scan:
    - com.moego.server.business.web

hubspot:
  authToken: pat-na1-************************************
