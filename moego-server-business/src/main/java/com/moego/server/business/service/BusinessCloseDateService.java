package com.moego.server.business.service;

import static com.moego.server.business.common.consts.DataSourceConst.READER;

import com.google.gson.Gson;
import com.moego.common.enums.CloseDateConst;
import com.moego.common.enums.DeleteStatusEnum;
import com.moego.common.utils.CommonUtil;
import com.moego.common.utils.DateUtil;
import com.moego.server.business.dto.BusinessClosedDateDTO;
import com.moego.server.business.dto.ParsedCloseDate;
import com.moego.server.business.mapper.MoeBusinessCloseDateMapper;
import com.moego.server.business.mapper.MoeBusinessMapper;
import com.moego.server.business.mapperbean.MoeBusiness;
import com.moego.server.business.mapperbean.MoeBusinessCloseDate;
import com.moego.server.business.web.vo.CloseDateDeleteVo;
import com.moego.server.business.web.vo.CloseDateSaveVo;
import com.moego.server.business.web.vo.CloseDateUpdateVo;
import com.moego.server.business.web.vo.HolidayUpdateBatchVo;
import com.moego.server.business.web.vo.HolidayUpdateVo;
import java.text.ParseException;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class BusinessCloseDateService {

    @Autowired
    private MoeBusinessCloseDateMapper businessCloseDateMapper;

    @Autowired
    private MoeBusinessMapper businessMapper;

    public MoeBusinessCloseDate getBusinessClosedDate(Integer businessId, Integer id) {
        MoeBusinessCloseDate closeDate = businessCloseDateMapper.selectByPrimaryKey(id);
        return closeDate == null || !closeDate.getBusinessId().equals(businessId) ? null : closeDate;
    }

    /**
     * 只包含closeDate
     *
     * @param businessId
     * @return
     */
    public List<MoeBusinessCloseDate> getBusinessClosedDateList(Integer businessId) {
        return businessCloseDateMapper.selectCloseDateByBusinessId(businessId);
    }

    /**
     * 根据date列表，计算是否包含在closeDates内
     *
     * @param allDate
     * @param closeDates
     * @return
     */
    public List<LocalDate> getNoAvailableDate(List<LocalDate> allDate, List<MoeBusinessCloseDate> closeDates) {
        return allDate.stream()
                .filter(localDate -> closeDates.stream().anyMatch(closeDate -> {
                    LocalDate startDate = LocalDate.parse(closeDate.getStartDate());
                    LocalDate endDate = LocalDate.parse(closeDate.getEndDate());
                    boolean startDateIsBeforeLocalDate = startDate.isBefore(localDate) || startDate.equals(localDate);
                    boolean endDateIsAfterLocalDate = endDate.isAfter(localDate) || endDate.equals(localDate);
                    return startDateIsBeforeLocalDate && endDateIsAfterLocalDate;
                }))
                .collect(Collectors.toList());
    }

    /**
     * 根据date列表，计算是否包含在closeDates内
     *
     * @param allDate
     * @param closeDates
     * @return
     */
    public List<String> getNoAvailableDateStr(List<Date> allDate, List<MoeBusinessCloseDate> closeDates) {
        List<String> noAvailableDate = new ArrayList<>();
        for (Date date : allDate) {
            boolean isFindInClosedDate = false;
            for (MoeBusinessCloseDate closeDate : closeDates) {
                try {
                    if (DateUtil.convertStringToDate(closeDate.getStartDate()).getTime() <= date.getTime()
                            && date.getTime()
                                    <= DateUtil.convertStringToDate(closeDate.getEndDate())
                                            .getTime()) {
                        isFindInClosedDate = true;
                        break;
                    }
                } catch (ParseException e) {
                    log.info("DateUtil.convertStringToDate().getTime() error json" + new Gson().toJson(closeDate));
                }
            }
            if (isFindInClosedDate) {
                noAvailableDate.add(DateUtil.getFormatStrForDate(date, "yyyy-MM-dd"));
            }
        }
        return noAvailableDate;
    }

    public List<MoeBusinessCloseDate> getCloseDateByStartDateEndDate(
            Integer businessId, String startDate, String endDate) {
        return businessCloseDateMapper.selectCloseDateByStartDateEndDate(businessId, startDate, endDate);
    }

    /**
     * 查询close date和holiday所有的时间
     * 只查询前天往后的时间，
     *
     * @param businessId
     * @return
     */
    public List<ParsedCloseDate> getAllCloseDate(Integer businessId) {
        List<MoeBusinessCloseDate> closeDates =
                businessCloseDateMapper.useDataSource(READER).selectAllCloseDateByBusinessId(businessId);
        return parseCloseDate(closeDates);
    }

    private List<ParsedCloseDate> parseCloseDate(List<MoeBusinessCloseDate> dates) {
        List<ParsedCloseDate> list = new ArrayList<>();
        dates.forEach(date -> {
            try {
                ParsedCloseDate dto = new ParsedCloseDate();
                dto.setStart(LocalDate.parse(date.getStartDate()));
                dto.setEnd(LocalDate.parse(date.getEndDate()));
                list.add(dto);
            } catch (DateTimeParseException e) {
                log.warn("parse closed date error {} ", date, e);
            }
        });
        return list;
    }

    public Integer addCloseDate(Long companyId, Integer businessId, CloseDateSaveVo saveVo) {
        MoeBusinessCloseDate closeDate = new MoeBusinessCloseDate();
        closeDate.setCompanyId(companyId);
        closeDate.setBusinessId(businessId);
        closeDate.setCreateTime(CommonUtil.get10Timestamp());
        closeDate.setUpdateTime(CommonUtil.get10Timestamp());
        closeDate.setDescription(saveVo.getDescription());
        closeDate.setStartDate(saveVo.getStartDate());
        closeDate.setEndDate(saveVo.getEndDate());
        businessCloseDateMapper.insertSelective(closeDate);
        return closeDate.getId();
    }

    public Boolean updateCloseDate(Integer businessId, CloseDateUpdateVo updateVo) {
        if (getBusinessClosedDate(businessId, updateVo.getId()) == null) {
            return false;
        }
        MoeBusinessCloseDate closeDate = new MoeBusinessCloseDate();
        closeDate.setUpdateTime(CommonUtil.get10Timestamp());
        closeDate.setDescription(updateVo.getDescription());
        closeDate.setStartDate(updateVo.getStartDate());
        closeDate.setEndDate(updateVo.getEndDate());
        closeDate.setId(updateVo.getId());
        return businessCloseDateMapper.updateByPrimaryKeySelective(closeDate) > 0;
    }

    public Boolean deleteCloseDate(Integer businessId, CloseDateDeleteVo deleteVo) {
        if (getBusinessClosedDate(businessId, deleteVo.getId()) == null) {
            return false;
        }
        MoeBusinessCloseDate closeDate = new MoeBusinessCloseDate();
        closeDate.setUpdateTime(CommonUtil.get10Timestamp());
        closeDate.setId(deleteVo.getId());
        closeDate.setStatus(DeleteStatusEnum.STATUS_DELETE);
        return businessCloseDateMapper.updateByPrimaryKeySelective(closeDate) > 0;
    }

    public List<MoeBusinessCloseDate> getClosedHoliday(Integer businessId, String year) {
        return businessCloseDateMapper.queryHolidayByBusinessIdYear(businessId, year);
    }

    public int closeHoliday(Long companyId, Integer businessId, HolidayUpdateVo updateVo) {
        List<MoeBusinessCloseDate> queryResultList = businessCloseDateMapper.queryHolidayByStartEndDate(
                businessId, updateVo.getStartDate(), updateVo.getEndDate());
        if (!CollectionUtils.isEmpty(queryResultList)) {
            return 0;
        }
        MoeBusinessCloseDate closeDate = new MoeBusinessCloseDate();
        closeDate.setCompanyId(companyId);
        closeDate.setBusinessId(businessId);
        closeDate.setCreateTime(CommonUtil.get10Timestamp());
        closeDate.setUpdateTime(CommonUtil.get10Timestamp());
        closeDate.setDescription(updateVo.getDescription());
        closeDate.setStartDate(updateVo.getStartDate());
        closeDate.setEndDate(updateVo.getEndDate());
        closeDate.setType(CloseDateConst.TYPE_HOLIDAY);
        return businessCloseDateMapper.insertSelective(closeDate);
    }

    public int openHoliday(Integer businessId, HolidayUpdateVo updateVo) {
        return businessCloseDateMapper.openHolidayByStartEndDate(
                businessId, updateVo.getStartDate(), updateVo.getEndDate(), DateUtil.get10Timestamp());
    }

    public int closeHolidayBatch(
            Long companyId, Integer businessId, List<HolidayUpdateBatchVo> holidayUpdateBatchVoList) {
        holidayUpdateBatchVoList.stream()
                .map(HolidayUpdateBatchVo::getYear)
                .distinct()
                .forEach(year ->
                        businessCloseDateMapper.openHolidayByYear(businessId, year, CommonUtil.get10Timestamp()));

        return holidayUpdateBatchVoList.stream()
                .flatMap(holidayUpdateBatchVo -> holidayUpdateBatchVo.getUpdateVoList().stream())
                .mapToInt(holidayUpdateVo -> closeHoliday(companyId, businessId, holidayUpdateVo))
                .sum();
    }

    /**
     * 只包含 holiday
     *
     * @param businessId
     * @return
     */
    public List<MoeBusinessCloseDate> getHolidayList(Integer businessId) {
        return businessCloseDateMapper.selectHolidayByBusinessId(businessId);
    }

    public List<BusinessClosedDateDTO> getThisWeekClosedDate(Integer businessId) {
        MoeBusiness business = businessMapper.selectByPrimaryKey(businessId);
        LocalDate nowDate = LocalDate.now(ZoneId.of(business.getTimezoneName()));
        LocalDate weekStart = nowDate.minusDays(nowDate.getDayOfWeek().getValue());
        LocalDate weekEnd = weekStart.plusWeeks(1);
        List<MoeBusinessCloseDate> closedDateList = businessCloseDateMapper.selectCloseDateByStartDateEndDate(
                businessId, weekStart.toString(), weekEnd.toString());
        return closedDateList.stream()
                .map(closedDate -> new BusinessClosedDateDTO()
                        .setStartDate(closedDate.getStartDate())
                        .setEndDate(closedDate.getEndDate())
                        .setDescription(closedDate.getDescription())
                        .setType(closedDate.getType()))
                .toList();
    }
}
