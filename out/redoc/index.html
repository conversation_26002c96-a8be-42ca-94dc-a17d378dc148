<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <title>MoeGo API Docs</title>
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <link href="https://fonts.googleapis.com/css?family=Montserrat:300,400,700|Roboto:300,400,700" rel="stylesheet" />
    <style>
      body {
        margin: 0;
        padding: 0;
      }

      html {
        font-size: 12px;
      }

      #menus {
        position: sticky;
        top: 0;
        background: white;
        z-index: 9999;
        padding: 12px 20px;
        text-align: center;
      }

      /* CSS */
      .button-55 {
        align-self: center;
        background-color: #fff;
        background-image: none;
        background-position: 0 90%;
        background-repeat: repeat no-repeat;
        background-size: 4px 3px;
        border-radius: 15px 225px 255px 15px 15px 255px 225px 15px;
        border-style: solid;
        border-width: 2px;
        box-shadow: rgba(0, 0, 0, 0.2) 15px 28px 25px -18px;
        box-sizing: border-box;
        color: #41403e;
        cursor: pointer;
        display: inline-block;
        font-family: Neucha, sans-serif;
        font-size: 1rem;
        line-height: 23px;
        outline: none;
        padding: 0.75rem;
        text-decoration: none;
        transition: all 235ms ease-in-out;
        border-bottom-left-radius: 15px 255px;
        border-bottom-right-radius: 225px 15px;
        border-top-left-radius: 255px 15px;
        border-top-right-radius: 15px 225px;
        user-select: none;
        -webkit-user-select: none;
        touch-action: manipulation;
      }

      .button-55:hover {
        box-shadow: rgba(0, 0, 0, 0.3) 2px 8px 8px -5px;
        transform: translate3d(0, 2px, 0);
      }

      .button-55:focus {
        box-shadow: rgba(0, 0, 0, 0.3) 2px 8px 4px -6px;
      }
    </style>
  </head>
  <body>
    <nav id="menus">
      <button class="button-55">gRPC-CLIENT-API</button>
      <button class="button-55">gRPC-ENTERPRISE-API</button>
      <button class="button-55">gRPC-API</button>
      <button class="button-55">gRPC-SVC</button>
      <button class="button-55">gRPC-ADMIN</button>
      <button class="button-55">REST-BUSINESS</button>
      <button class="button-55">REST-CUSTOMER</button>
      <button class="button-55">REST-MESSAGE</button>
      <button class="button-55">REST-PAYMENT</button>
      <button class="button-55">REST-RETAIL</button>
      <button class="button-55">REST-GROOMING</button>
      <button class="button-55">REST-SERVICE</button>
    </nav>
    <div id="redoc"></div>
    <script src="https://cdn.redoc.ly/redoc/latest/bundles/redoc.standalone.js"></script>
    <script>
      /**
       * @param {string} text
       */
      function initRedoc(text) {
        const type = text.split('-')[0];
        const module = text.substring(type.length + 1).toLowerCase();
        const url = type === 'gRPC' ? module + '.json' : 'rest/v3/' + module + '.json';
        Redoc.init(
          url,
          {
            scrollYOffset: '#menus',
            ctrlFHijack: true,
          },
          document.querySelector('#redoc'),
        );
        const href = new URL(location.href);
        href.searchParams.set('module', text);
        history.pushState(void 0, document.title, href);
      }

      initRedoc(new URL(location.href).searchParams.get('module') || 'gRPC-API');

      document.querySelector('#menus').addEventListener('click', (e) => {
        if (e.target instanceof HTMLButtonElement) {
          const text = e.target.textContent.trim();
          initRedoc(text);
        }
      });
    </script>
  </body>
</html>
