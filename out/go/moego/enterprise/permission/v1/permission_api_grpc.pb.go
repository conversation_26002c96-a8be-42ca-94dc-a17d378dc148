// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/enterprise/permission/v1/permission_api.proto

package permissionapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// PermissionServiceClient is the client API for PermissionService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PermissionServiceClient interface {
	// get role list
	ListRoles(ctx context.Context, in *ListRolesParams, opts ...grpc.CallOption) (*ListRolesResult, error)
	// get role detail
	GetRole(ctx context.Context, in *GetRoleParams, opts ...grpc.CallOption) (*GetRoleResult, error)
	// create role
	CreateRole(ctx context.Context, in *CreateRoleParams, opts ...grpc.CallOption) (*CreateRoleResult, error)
	// update role
	UpdateRole(ctx context.Context, in *UpdateRoleParams, opts ...grpc.CallOption) (*UpdateRoleResult, error)
	// delete role
	DeleteRole(ctx context.Context, in *DeleteRoleParams, opts ...grpc.CallOption) (*DeleteRoleResult, error)
	// duplicate role
	DuplicateRole(ctx context.Context, in *DuplicateRoleParams, opts ...grpc.CallOption) (*DuplicateRoleResult, error)
	// edit permissions
	EditPermissions(ctx context.Context, in *EditPermissionsParams, opts ...grpc.CallOption) (*EditPermissionsResult, error)
}

type permissionServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewPermissionServiceClient(cc grpc.ClientConnInterface) PermissionServiceClient {
	return &permissionServiceClient{cc}
}

func (c *permissionServiceClient) ListRoles(ctx context.Context, in *ListRolesParams, opts ...grpc.CallOption) (*ListRolesResult, error) {
	out := new(ListRolesResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.permission.v1.PermissionService/ListRoles", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *permissionServiceClient) GetRole(ctx context.Context, in *GetRoleParams, opts ...grpc.CallOption) (*GetRoleResult, error) {
	out := new(GetRoleResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.permission.v1.PermissionService/GetRole", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *permissionServiceClient) CreateRole(ctx context.Context, in *CreateRoleParams, opts ...grpc.CallOption) (*CreateRoleResult, error) {
	out := new(CreateRoleResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.permission.v1.PermissionService/CreateRole", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *permissionServiceClient) UpdateRole(ctx context.Context, in *UpdateRoleParams, opts ...grpc.CallOption) (*UpdateRoleResult, error) {
	out := new(UpdateRoleResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.permission.v1.PermissionService/UpdateRole", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *permissionServiceClient) DeleteRole(ctx context.Context, in *DeleteRoleParams, opts ...grpc.CallOption) (*DeleteRoleResult, error) {
	out := new(DeleteRoleResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.permission.v1.PermissionService/DeleteRole", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *permissionServiceClient) DuplicateRole(ctx context.Context, in *DuplicateRoleParams, opts ...grpc.CallOption) (*DuplicateRoleResult, error) {
	out := new(DuplicateRoleResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.permission.v1.PermissionService/DuplicateRole", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *permissionServiceClient) EditPermissions(ctx context.Context, in *EditPermissionsParams, opts ...grpc.CallOption) (*EditPermissionsResult, error) {
	out := new(EditPermissionsResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.permission.v1.PermissionService/EditPermissions", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PermissionServiceServer is the server API for PermissionService service.
// All implementations must embed UnimplementedPermissionServiceServer
// for forward compatibility
type PermissionServiceServer interface {
	// get role list
	ListRoles(context.Context, *ListRolesParams) (*ListRolesResult, error)
	// get role detail
	GetRole(context.Context, *GetRoleParams) (*GetRoleResult, error)
	// create role
	CreateRole(context.Context, *CreateRoleParams) (*CreateRoleResult, error)
	// update role
	UpdateRole(context.Context, *UpdateRoleParams) (*UpdateRoleResult, error)
	// delete role
	DeleteRole(context.Context, *DeleteRoleParams) (*DeleteRoleResult, error)
	// duplicate role
	DuplicateRole(context.Context, *DuplicateRoleParams) (*DuplicateRoleResult, error)
	// edit permissions
	EditPermissions(context.Context, *EditPermissionsParams) (*EditPermissionsResult, error)
	mustEmbedUnimplementedPermissionServiceServer()
}

// UnimplementedPermissionServiceServer must be embedded to have forward compatible implementations.
type UnimplementedPermissionServiceServer struct {
}

func (UnimplementedPermissionServiceServer) ListRoles(context.Context, *ListRolesParams) (*ListRolesResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListRoles not implemented")
}
func (UnimplementedPermissionServiceServer) GetRole(context.Context, *GetRoleParams) (*GetRoleResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRole not implemented")
}
func (UnimplementedPermissionServiceServer) CreateRole(context.Context, *CreateRoleParams) (*CreateRoleResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateRole not implemented")
}
func (UnimplementedPermissionServiceServer) UpdateRole(context.Context, *UpdateRoleParams) (*UpdateRoleResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateRole not implemented")
}
func (UnimplementedPermissionServiceServer) DeleteRole(context.Context, *DeleteRoleParams) (*DeleteRoleResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteRole not implemented")
}
func (UnimplementedPermissionServiceServer) DuplicateRole(context.Context, *DuplicateRoleParams) (*DuplicateRoleResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DuplicateRole not implemented")
}
func (UnimplementedPermissionServiceServer) EditPermissions(context.Context, *EditPermissionsParams) (*EditPermissionsResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method EditPermissions not implemented")
}
func (UnimplementedPermissionServiceServer) mustEmbedUnimplementedPermissionServiceServer() {}

// UnsafePermissionServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PermissionServiceServer will
// result in compilation errors.
type UnsafePermissionServiceServer interface {
	mustEmbedUnimplementedPermissionServiceServer()
}

func RegisterPermissionServiceServer(s grpc.ServiceRegistrar, srv PermissionServiceServer) {
	s.RegisterService(&PermissionService_ServiceDesc, srv)
}

func _PermissionService_ListRoles_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListRolesParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PermissionServiceServer).ListRoles(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.permission.v1.PermissionService/ListRoles",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PermissionServiceServer).ListRoles(ctx, req.(*ListRolesParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _PermissionService_GetRole_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRoleParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PermissionServiceServer).GetRole(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.permission.v1.PermissionService/GetRole",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PermissionServiceServer).GetRole(ctx, req.(*GetRoleParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _PermissionService_CreateRole_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateRoleParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PermissionServiceServer).CreateRole(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.permission.v1.PermissionService/CreateRole",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PermissionServiceServer).CreateRole(ctx, req.(*CreateRoleParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _PermissionService_UpdateRole_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateRoleParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PermissionServiceServer).UpdateRole(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.permission.v1.PermissionService/UpdateRole",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PermissionServiceServer).UpdateRole(ctx, req.(*UpdateRoleParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _PermissionService_DeleteRole_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteRoleParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PermissionServiceServer).DeleteRole(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.permission.v1.PermissionService/DeleteRole",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PermissionServiceServer).DeleteRole(ctx, req.(*DeleteRoleParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _PermissionService_DuplicateRole_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DuplicateRoleParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PermissionServiceServer).DuplicateRole(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.permission.v1.PermissionService/DuplicateRole",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PermissionServiceServer).DuplicateRole(ctx, req.(*DuplicateRoleParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _PermissionService_EditPermissions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EditPermissionsParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PermissionServiceServer).EditPermissions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.permission.v1.PermissionService/EditPermissions",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PermissionServiceServer).EditPermissions(ctx, req.(*EditPermissionsParams))
	}
	return interceptor(ctx, in, info, handler)
}

// PermissionService_ServiceDesc is the grpc.ServiceDesc for PermissionService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PermissionService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.enterprise.permission.v1.PermissionService",
	HandlerType: (*PermissionServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListRoles",
			Handler:    _PermissionService_ListRoles_Handler,
		},
		{
			MethodName: "GetRole",
			Handler:    _PermissionService_GetRole_Handler,
		},
		{
			MethodName: "CreateRole",
			Handler:    _PermissionService_CreateRole_Handler,
		},
		{
			MethodName: "UpdateRole",
			Handler:    _PermissionService_UpdateRole_Handler,
		},
		{
			MethodName: "DeleteRole",
			Handler:    _PermissionService_DeleteRole_Handler,
		},
		{
			MethodName: "DuplicateRole",
			Handler:    _PermissionService_DuplicateRole_Handler,
		},
		{
			MethodName: "EditPermissions",
			Handler:    _PermissionService_EditPermissions_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/enterprise/permission/v1/permission_api.proto",
}
