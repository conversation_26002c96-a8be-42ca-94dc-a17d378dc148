// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/enterprise/permission/v1/permission_api.proto

package permissionapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/enterprise/v1"
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/permission/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// get role list request
type ListRolesParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// enterprise id
	EnterpriseId int64 `protobuf:"varint,1,opt,name=enterprise_id,json=enterpriseId,proto3" json:"enterprise_id,omitempty"`
	// need permission
	NeedPermission bool `protobuf:"varint,2,opt,name=need_permission,json=needPermission,proto3" json:"need_permission,omitempty"`
}

func (x *ListRolesParams) Reset() {
	*x = ListRolesParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_permission_v1_permission_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListRolesParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListRolesParams) ProtoMessage() {}

func (x *ListRolesParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_permission_v1_permission_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListRolesParams.ProtoReflect.Descriptor instead.
func (*ListRolesParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_permission_v1_permission_api_proto_rawDescGZIP(), []int{0}
}

func (x *ListRolesParams) GetEnterpriseId() int64 {
	if x != nil {
		return x.EnterpriseId
	}
	return 0
}

func (x *ListRolesParams) GetNeedPermission() bool {
	if x != nil {
		return x.NeedPermission
	}
	return false
}

// get role list response
type ListRolesResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// role list
	Roles []*ListRolesResult_Result `protobuf:"bytes,1,rep,name=roles,proto3" json:"roles,omitempty"`
}

func (x *ListRolesResult) Reset() {
	*x = ListRolesResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_permission_v1_permission_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListRolesResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListRolesResult) ProtoMessage() {}

func (x *ListRolesResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_permission_v1_permission_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListRolesResult.ProtoReflect.Descriptor instead.
func (*ListRolesResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_permission_v1_permission_api_proto_rawDescGZIP(), []int{1}
}

func (x *ListRolesResult) GetRoles() []*ListRolesResult_Result {
	if x != nil {
		return x.Roles
	}
	return nil
}

// get role request
type GetRoleParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// role id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// need permission
	NeedPermission bool `protobuf:"varint,2,opt,name=need_permission,json=needPermission,proto3" json:"need_permission,omitempty"`
}

func (x *GetRoleParams) Reset() {
	*x = GetRoleParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_permission_v1_permission_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRoleParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRoleParams) ProtoMessage() {}

func (x *GetRoleParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_permission_v1_permission_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRoleParams.ProtoReflect.Descriptor instead.
func (*GetRoleParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_permission_v1_permission_api_proto_rawDescGZIP(), []int{2}
}

func (x *GetRoleParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetRoleParams) GetNeedPermission() bool {
	if x != nil {
		return x.NeedPermission
	}
	return false
}

// get role response
type GetRoleResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// role
	Role *v1.RoleModel `protobuf:"bytes,1,opt,name=role,proto3" json:"role,omitempty"`
	// role permissions
	PermissionCategoryList []*v11.PermissionCategoryModel `protobuf:"bytes,2,rep,name=permission_category_list,json=permissionCategoryList,proto3" json:"permission_category_list,omitempty"`
}

func (x *GetRoleResult) Reset() {
	*x = GetRoleResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_permission_v1_permission_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRoleResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRoleResult) ProtoMessage() {}

func (x *GetRoleResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_permission_v1_permission_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRoleResult.ProtoReflect.Descriptor instead.
func (*GetRoleResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_permission_v1_permission_api_proto_rawDescGZIP(), []int{3}
}

func (x *GetRoleResult) GetRole() *v1.RoleModel {
	if x != nil {
		return x.Role
	}
	return nil
}

func (x *GetRoleResult) GetPermissionCategoryList() []*v11.PermissionCategoryModel {
	if x != nil {
		return x.PermissionCategoryList
	}
	return nil
}

// create role request
type CreateRoleParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// name
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *CreateRoleParams) Reset() {
	*x = CreateRoleParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_permission_v1_permission_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateRoleParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateRoleParams) ProtoMessage() {}

func (x *CreateRoleParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_permission_v1_permission_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateRoleParams.ProtoReflect.Descriptor instead.
func (*CreateRoleParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_permission_v1_permission_api_proto_rawDescGZIP(), []int{4}
}

func (x *CreateRoleParams) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// create role response
type CreateRoleResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// created role id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// role list after created
	Roles []*v1.RoleModel `protobuf:"bytes,2,rep,name=roles,proto3" json:"roles,omitempty"`
}

func (x *CreateRoleResult) Reset() {
	*x = CreateRoleResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_permission_v1_permission_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateRoleResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateRoleResult) ProtoMessage() {}

func (x *CreateRoleResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_permission_v1_permission_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateRoleResult.ProtoReflect.Descriptor instead.
func (*CreateRoleResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_permission_v1_permission_api_proto_rawDescGZIP(), []int{5}
}

func (x *CreateRoleResult) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CreateRoleResult) GetRoles() []*v1.RoleModel {
	if x != nil {
		return x.Roles
	}
	return nil
}

// update role request
type UpdateRoleParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// role id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// role name
	Name *string `protobuf:"bytes,2,opt,name=name,proto3,oneof" json:"name,omitempty"`
}

func (x *UpdateRoleParams) Reset() {
	*x = UpdateRoleParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_permission_v1_permission_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateRoleParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateRoleParams) ProtoMessage() {}

func (x *UpdateRoleParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_permission_v1_permission_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateRoleParams.ProtoReflect.Descriptor instead.
func (*UpdateRoleParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_permission_v1_permission_api_proto_rawDescGZIP(), []int{6}
}

func (x *UpdateRoleParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateRoleParams) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

// update role response
type UpdateRoleResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// role after updated
	Role *v1.RoleModel `protobuf:"bytes,1,opt,name=role,proto3" json:"role,omitempty"`
}

func (x *UpdateRoleResult) Reset() {
	*x = UpdateRoleResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_permission_v1_permission_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateRoleResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateRoleResult) ProtoMessage() {}

func (x *UpdateRoleResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_permission_v1_permission_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateRoleResult.ProtoReflect.Descriptor instead.
func (*UpdateRoleResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_permission_v1_permission_api_proto_rawDescGZIP(), []int{7}
}

func (x *UpdateRoleResult) GetRole() *v1.RoleModel {
	if x != nil {
		return x.Role
	}
	return nil
}

// delete role request
type DeleteRoleParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// role id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *DeleteRoleParams) Reset() {
	*x = DeleteRoleParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_permission_v1_permission_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteRoleParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteRoleParams) ProtoMessage() {}

func (x *DeleteRoleParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_permission_v1_permission_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteRoleParams.ProtoReflect.Descriptor instead.
func (*DeleteRoleParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_permission_v1_permission_api_proto_rawDescGZIP(), []int{8}
}

func (x *DeleteRoleParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// delete role response
type DeleteRoleResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// role list after deleted
	Roles []*v1.RoleModel `protobuf:"bytes,1,rep,name=roles,proto3" json:"roles,omitempty"`
}

func (x *DeleteRoleResult) Reset() {
	*x = DeleteRoleResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_permission_v1_permission_api_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteRoleResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteRoleResult) ProtoMessage() {}

func (x *DeleteRoleResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_permission_v1_permission_api_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteRoleResult.ProtoReflect.Descriptor instead.
func (*DeleteRoleResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_permission_v1_permission_api_proto_rawDescGZIP(), []int{9}
}

func (x *DeleteRoleResult) GetRoles() []*v1.RoleModel {
	if x != nil {
		return x.Roles
	}
	return nil
}

// duplicate role request
type DuplicateRoleParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// role id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// role name
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *DuplicateRoleParams) Reset() {
	*x = DuplicateRoleParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_permission_v1_permission_api_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DuplicateRoleParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DuplicateRoleParams) ProtoMessage() {}

func (x *DuplicateRoleParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_permission_v1_permission_api_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DuplicateRoleParams.ProtoReflect.Descriptor instead.
func (*DuplicateRoleParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_permission_v1_permission_api_proto_rawDescGZIP(), []int{10}
}

func (x *DuplicateRoleParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DuplicateRoleParams) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// duplicate role response
type DuplicateRoleResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// role list after duplicated
	Roles []*v1.RoleModel `protobuf:"bytes,1,rep,name=roles,proto3" json:"roles,omitempty"`
}

func (x *DuplicateRoleResult) Reset() {
	*x = DuplicateRoleResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_permission_v1_permission_api_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DuplicateRoleResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DuplicateRoleResult) ProtoMessage() {}

func (x *DuplicateRoleResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_permission_v1_permission_api_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DuplicateRoleResult.ProtoReflect.Descriptor instead.
func (*DuplicateRoleResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_permission_v1_permission_api_proto_rawDescGZIP(), []int{11}
}

func (x *DuplicateRoleResult) GetRoles() []*v1.RoleModel {
	if x != nil {
		return x.Roles
	}
	return nil
}

// edit permissions request
type EditPermissionsParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// role id
	RoleId int64 `protobuf:"varint,1,opt,name=role_id,json=roleId,proto3" json:"role_id,omitempty"`
	// permission list in a category
	PermissionCategories []*v11.EditCategoryPermissionDef `protobuf:"bytes,2,rep,name=permission_categories,json=permissionCategories,proto3" json:"permission_categories,omitempty"`
}

func (x *EditPermissionsParams) Reset() {
	*x = EditPermissionsParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_permission_v1_permission_api_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditPermissionsParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditPermissionsParams) ProtoMessage() {}

func (x *EditPermissionsParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_permission_v1_permission_api_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditPermissionsParams.ProtoReflect.Descriptor instead.
func (*EditPermissionsParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_permission_v1_permission_api_proto_rawDescGZIP(), []int{12}
}

func (x *EditPermissionsParams) GetRoleId() int64 {
	if x != nil {
		return x.RoleId
	}
	return 0
}

func (x *EditPermissionsParams) GetPermissionCategories() []*v11.EditCategoryPermissionDef {
	if x != nil {
		return x.PermissionCategories
	}
	return nil
}

// edit permissions response
type EditPermissionsResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// permission list after edited
	PermissionCategories []*v11.PermissionCategoryModel `protobuf:"bytes,1,rep,name=permission_categories,json=permissionCategories,proto3" json:"permission_categories,omitempty"`
}

func (x *EditPermissionsResult) Reset() {
	*x = EditPermissionsResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_permission_v1_permission_api_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EditPermissionsResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EditPermissionsResult) ProtoMessage() {}

func (x *EditPermissionsResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_permission_v1_permission_api_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EditPermissionsResult.ProtoReflect.Descriptor instead.
func (*EditPermissionsResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_permission_v1_permission_api_proto_rawDescGZIP(), []int{13}
}

func (x *EditPermissionsResult) GetPermissionCategories() []*v11.PermissionCategoryModel {
	if x != nil {
		return x.PermissionCategories
	}
	return nil
}

// role result
type ListRolesResult_Result struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// role
	Role *v1.RoleModel `protobuf:"bytes,1,opt,name=role,proto3" json:"role,omitempty"`
	// role permissions
	PermissionCategoryList []*v11.PermissionCategoryModel `protobuf:"bytes,2,rep,name=permission_category_list,json=permissionCategoryList,proto3" json:"permission_category_list,omitempty"`
}

func (x *ListRolesResult_Result) Reset() {
	*x = ListRolesResult_Result{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_permission_v1_permission_api_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListRolesResult_Result) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListRolesResult_Result) ProtoMessage() {}

func (x *ListRolesResult_Result) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_permission_v1_permission_api_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListRolesResult_Result.ProtoReflect.Descriptor instead.
func (*ListRolesResult_Result) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_permission_v1_permission_api_proto_rawDescGZIP(), []int{1, 0}
}

func (x *ListRolesResult_Result) GetRole() *v1.RoleModel {
	if x != nil {
		return x.Role
	}
	return nil
}

func (x *ListRolesResult_Result) GetPermissionCategoryList() []*v11.PermissionCategoryModel {
	if x != nil {
		return x.PermissionCategoryList
	}
	return nil
}

var File_moego_enterprise_permission_v1_permission_api_proto protoreflect.FileDescriptor

var file_moego_enterprise_permission_v1_permission_api_proto_rawDesc = []byte{
	0x0a, 0x33, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x2f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31,
	0x2f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x70, 0x69, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x1a, 0x2c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x76,
	0x31, 0x2f, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x30, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f,
	0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2f, 0x76,
	0x31, 0x2f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0x5f, 0x0a, 0x0f, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x6f, 0x6c, 0x65, 0x73, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x49, 0x64, 0x12, 0x27, 0x0a, 0x0f, 0x6e, 0x65,
	0x65, 0x64, 0x5f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0e, 0x6e, 0x65, 0x65, 0x64, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x22, 0x94, 0x02, 0x0a, 0x0f, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x6f, 0x6c, 0x65,
	0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x4c, 0x0a, 0x05, 0x72, 0x6f, 0x6c, 0x65, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x6f, 0x6c, 0x65,
	0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x05,
	0x72, 0x6f, 0x6c, 0x65, 0x73, 0x1a, 0xb2, 0x01, 0x0a, 0x06, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x39, 0x0a, 0x04, 0x72, 0x6f, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x6f, 0x6c, 0x65,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x04, 0x72, 0x6f, 0x6c, 0x65, 0x12, 0x6d, 0x0a, 0x18, 0x70,
	0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x79, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x33, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x65, 0x72,
	0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x72, 0x6d, 0x69,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x52, 0x16, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x51, 0x0a, 0x0d, 0x47, 0x65,
	0x74, 0x52, 0x6f, 0x6c, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x17, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x28, 0x00,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x27, 0x0a, 0x0f, 0x6e, 0x65, 0x65, 0x64, 0x5f, 0x70, 0x65, 0x72,
	0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x6e,
	0x65, 0x65, 0x64, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0xb9, 0x01,
	0x0a, 0x0d, 0x47, 0x65, 0x74, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x39, 0x0a, 0x04, 0x72, 0x6f, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x6f, 0x6c, 0x65, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x52, 0x04, 0x72, 0x6f, 0x6c, 0x65, 0x12, 0x6d, 0x0a, 0x18, 0x70, 0x65,
	0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x65, 0x72, 0x6d,
	0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x52, 0x16, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x32, 0x0a, 0x10, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x1e, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07,
	0x72, 0x05, 0x10, 0x01, 0x18, 0xff, 0x01, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x5f, 0x0a,
	0x10, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x3b, 0x0a, 0x05, 0x72, 0x6f, 0x6c, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x6f,
	0x6c, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x05, 0x72, 0x6f, 0x6c, 0x65, 0x73, 0x22, 0x59,
	0x0a, 0x10, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x23, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05,
	0x10, 0x01, 0x18, 0xff, 0x01, 0x48, 0x00, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01,
	0x42, 0x07, 0x0a, 0x05, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x4d, 0x0a, 0x10, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x39, 0x0a,
	0x04, 0x72, 0x6f, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x6f, 0x6c, 0x65, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x52, 0x04, 0x72, 0x6f, 0x6c, 0x65, 0x22, 0x2b, 0x0a, 0x10, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x17, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x02, 0x69, 0x64, 0x22, 0x4f, 0x0a, 0x10, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52,
	0x6f, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x3b, 0x0a, 0x05, 0x72, 0x6f, 0x6c,
	0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x6f, 0x6c, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52,
	0x05, 0x72, 0x6f, 0x6c, 0x65, 0x73, 0x22, 0x4e, 0x0a, 0x13, 0x44, 0x75, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x74, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x17, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1e, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0x18, 0xff, 0x01,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x52, 0x0a, 0x13, 0x44, 0x75, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x74, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x3b, 0x0a,
	0x05, 0x72, 0x6f, 0x6c, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x6f, 0x6c, 0x65, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x52, 0x05, 0x72, 0x6f, 0x6c, 0x65, 0x73, 0x22, 0xa5, 0x01, 0x0a, 0x15, 0x45,
	0x64, 0x69, 0x74, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x12, 0x20, 0x0a, 0x07, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06,
	0x72, 0x6f, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x6a, 0x0a, 0x15, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x45, 0x64, 0x69, 0x74, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x50,
	0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x66, 0x52, 0x14, 0x70, 0x65,
	0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69,
	0x65, 0x73, 0x22, 0x81, 0x01, 0x0a, 0x15, 0x45, 0x64, 0x69, 0x74, 0x50, 0x65, 0x72, 0x6d, 0x69,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x68, 0x0a, 0x15,
	0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x69, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x65, 0x72, 0x6d, 0x69,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x52, 0x14, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x32, 0xcc, 0x06, 0x0a, 0x11, 0x50, 0x65, 0x72, 0x6d, 0x69,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x6f, 0x0a, 0x09,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x6f, 0x6c, 0x65, 0x73, 0x12, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x70, 0x65, 0x72,
	0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x6f, 0x6c, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2f, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x70, 0x65,
	0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x6f, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x00, 0x12, 0x69, 0x0a,
	0x07, 0x47, 0x65, 0x74, 0x52, 0x6f, 0x6c, 0x65, 0x12, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x70, 0x65, 0x72, 0x6d,
	0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x6f, 0x6c,
	0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x70, 0x65, 0x72, 0x6d, 0x69,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x6f, 0x6c, 0x65,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x00, 0x12, 0x72, 0x0a, 0x0a, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x12, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x6f,
	0x6c, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x70, 0x65, 0x72, 0x6d,
	0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x52, 0x6f, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x00, 0x12, 0x72, 0x0a, 0x0a,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x12, 0x30, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x70, 0x65,
	0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x30, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e,
	0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x00,
	0x12, 0x72, 0x0a, 0x0a, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x12, 0x30,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73,
	0x65, 0x2e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x1a, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x2e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x22, 0x00, 0x12, 0x7b, 0x0a, 0x0d, 0x44, 0x75, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74,
	0x65, 0x52, 0x6f, 0x6c, 0x65, 0x12, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x75, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x65,
	0x52, 0x6f, 0x6c, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x33, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x70, 0x65,
	0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x75, 0x70, 0x6c,
	0x69, 0x63, 0x61, 0x74, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22,
	0x00, 0x12, 0x81, 0x01, 0x0a, 0x0f, 0x45, 0x64, 0x69, 0x74, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x64, 0x69, 0x74, 0x50, 0x65, 0x72, 0x6d, 0x69,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x35, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e,
	0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x64,
	0x69, 0x74, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x22, 0x00, 0x42, 0x8f, 0x01, 0x0a, 0x26, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x2e, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x50, 0x01, 0x5a, 0x63, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d,
	0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x3b, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_enterprise_permission_v1_permission_api_proto_rawDescOnce sync.Once
	file_moego_enterprise_permission_v1_permission_api_proto_rawDescData = file_moego_enterprise_permission_v1_permission_api_proto_rawDesc
)

func file_moego_enterprise_permission_v1_permission_api_proto_rawDescGZIP() []byte {
	file_moego_enterprise_permission_v1_permission_api_proto_rawDescOnce.Do(func() {
		file_moego_enterprise_permission_v1_permission_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_enterprise_permission_v1_permission_api_proto_rawDescData)
	})
	return file_moego_enterprise_permission_v1_permission_api_proto_rawDescData
}

var file_moego_enterprise_permission_v1_permission_api_proto_msgTypes = make([]protoimpl.MessageInfo, 15)
var file_moego_enterprise_permission_v1_permission_api_proto_goTypes = []interface{}{
	(*ListRolesParams)(nil),               // 0: moego.enterprise.permission.v1.ListRolesParams
	(*ListRolesResult)(nil),               // 1: moego.enterprise.permission.v1.ListRolesResult
	(*GetRoleParams)(nil),                 // 2: moego.enterprise.permission.v1.GetRoleParams
	(*GetRoleResult)(nil),                 // 3: moego.enterprise.permission.v1.GetRoleResult
	(*CreateRoleParams)(nil),              // 4: moego.enterprise.permission.v1.CreateRoleParams
	(*CreateRoleResult)(nil),              // 5: moego.enterprise.permission.v1.CreateRoleResult
	(*UpdateRoleParams)(nil),              // 6: moego.enterprise.permission.v1.UpdateRoleParams
	(*UpdateRoleResult)(nil),              // 7: moego.enterprise.permission.v1.UpdateRoleResult
	(*DeleteRoleParams)(nil),              // 8: moego.enterprise.permission.v1.DeleteRoleParams
	(*DeleteRoleResult)(nil),              // 9: moego.enterprise.permission.v1.DeleteRoleResult
	(*DuplicateRoleParams)(nil),           // 10: moego.enterprise.permission.v1.DuplicateRoleParams
	(*DuplicateRoleResult)(nil),           // 11: moego.enterprise.permission.v1.DuplicateRoleResult
	(*EditPermissionsParams)(nil),         // 12: moego.enterprise.permission.v1.EditPermissionsParams
	(*EditPermissionsResult)(nil),         // 13: moego.enterprise.permission.v1.EditPermissionsResult
	(*ListRolesResult_Result)(nil),        // 14: moego.enterprise.permission.v1.ListRolesResult.Result
	(*v1.RoleModel)(nil),                  // 15: moego.models.enterprise.v1.RoleModel
	(*v11.PermissionCategoryModel)(nil),   // 16: moego.models.permission.v1.PermissionCategoryModel
	(*v11.EditCategoryPermissionDef)(nil), // 17: moego.models.permission.v1.EditCategoryPermissionDef
}
var file_moego_enterprise_permission_v1_permission_api_proto_depIdxs = []int32{
	14, // 0: moego.enterprise.permission.v1.ListRolesResult.roles:type_name -> moego.enterprise.permission.v1.ListRolesResult.Result
	15, // 1: moego.enterprise.permission.v1.GetRoleResult.role:type_name -> moego.models.enterprise.v1.RoleModel
	16, // 2: moego.enterprise.permission.v1.GetRoleResult.permission_category_list:type_name -> moego.models.permission.v1.PermissionCategoryModel
	15, // 3: moego.enterprise.permission.v1.CreateRoleResult.roles:type_name -> moego.models.enterprise.v1.RoleModel
	15, // 4: moego.enterprise.permission.v1.UpdateRoleResult.role:type_name -> moego.models.enterprise.v1.RoleModel
	15, // 5: moego.enterprise.permission.v1.DeleteRoleResult.roles:type_name -> moego.models.enterprise.v1.RoleModel
	15, // 6: moego.enterprise.permission.v1.DuplicateRoleResult.roles:type_name -> moego.models.enterprise.v1.RoleModel
	17, // 7: moego.enterprise.permission.v1.EditPermissionsParams.permission_categories:type_name -> moego.models.permission.v1.EditCategoryPermissionDef
	16, // 8: moego.enterprise.permission.v1.EditPermissionsResult.permission_categories:type_name -> moego.models.permission.v1.PermissionCategoryModel
	15, // 9: moego.enterprise.permission.v1.ListRolesResult.Result.role:type_name -> moego.models.enterprise.v1.RoleModel
	16, // 10: moego.enterprise.permission.v1.ListRolesResult.Result.permission_category_list:type_name -> moego.models.permission.v1.PermissionCategoryModel
	0,  // 11: moego.enterprise.permission.v1.PermissionService.ListRoles:input_type -> moego.enterprise.permission.v1.ListRolesParams
	2,  // 12: moego.enterprise.permission.v1.PermissionService.GetRole:input_type -> moego.enterprise.permission.v1.GetRoleParams
	4,  // 13: moego.enterprise.permission.v1.PermissionService.CreateRole:input_type -> moego.enterprise.permission.v1.CreateRoleParams
	6,  // 14: moego.enterprise.permission.v1.PermissionService.UpdateRole:input_type -> moego.enterprise.permission.v1.UpdateRoleParams
	8,  // 15: moego.enterprise.permission.v1.PermissionService.DeleteRole:input_type -> moego.enterprise.permission.v1.DeleteRoleParams
	10, // 16: moego.enterprise.permission.v1.PermissionService.DuplicateRole:input_type -> moego.enterprise.permission.v1.DuplicateRoleParams
	12, // 17: moego.enterprise.permission.v1.PermissionService.EditPermissions:input_type -> moego.enterprise.permission.v1.EditPermissionsParams
	1,  // 18: moego.enterprise.permission.v1.PermissionService.ListRoles:output_type -> moego.enterprise.permission.v1.ListRolesResult
	3,  // 19: moego.enterprise.permission.v1.PermissionService.GetRole:output_type -> moego.enterprise.permission.v1.GetRoleResult
	5,  // 20: moego.enterprise.permission.v1.PermissionService.CreateRole:output_type -> moego.enterprise.permission.v1.CreateRoleResult
	7,  // 21: moego.enterprise.permission.v1.PermissionService.UpdateRole:output_type -> moego.enterprise.permission.v1.UpdateRoleResult
	9,  // 22: moego.enterprise.permission.v1.PermissionService.DeleteRole:output_type -> moego.enterprise.permission.v1.DeleteRoleResult
	11, // 23: moego.enterprise.permission.v1.PermissionService.DuplicateRole:output_type -> moego.enterprise.permission.v1.DuplicateRoleResult
	13, // 24: moego.enterprise.permission.v1.PermissionService.EditPermissions:output_type -> moego.enterprise.permission.v1.EditPermissionsResult
	18, // [18:25] is the sub-list for method output_type
	11, // [11:18] is the sub-list for method input_type
	11, // [11:11] is the sub-list for extension type_name
	11, // [11:11] is the sub-list for extension extendee
	0,  // [0:11] is the sub-list for field type_name
}

func init() { file_moego_enterprise_permission_v1_permission_api_proto_init() }
func file_moego_enterprise_permission_v1_permission_api_proto_init() {
	if File_moego_enterprise_permission_v1_permission_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_enterprise_permission_v1_permission_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListRolesParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_permission_v1_permission_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListRolesResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_permission_v1_permission_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRoleParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_permission_v1_permission_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRoleResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_permission_v1_permission_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateRoleParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_permission_v1_permission_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateRoleResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_permission_v1_permission_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateRoleParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_permission_v1_permission_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateRoleResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_permission_v1_permission_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteRoleParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_permission_v1_permission_api_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteRoleResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_permission_v1_permission_api_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DuplicateRoleParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_permission_v1_permission_api_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DuplicateRoleResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_permission_v1_permission_api_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EditPermissionsParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_permission_v1_permission_api_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EditPermissionsResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_permission_v1_permission_api_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListRolesResult_Result); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_enterprise_permission_v1_permission_api_proto_msgTypes[6].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_enterprise_permission_v1_permission_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   15,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_enterprise_permission_v1_permission_api_proto_goTypes,
		DependencyIndexes: file_moego_enterprise_permission_v1_permission_api_proto_depIdxs,
		MessageInfos:      file_moego_enterprise_permission_v1_permission_api_proto_msgTypes,
	}.Build()
	File_moego_enterprise_permission_v1_permission_api_proto = out.File
	file_moego_enterprise_permission_v1_permission_api_proto_rawDesc = nil
	file_moego_enterprise_permission_v1_permission_api_proto_goTypes = nil
	file_moego_enterprise_permission_v1_permission_api_proto_depIdxs = nil
}
