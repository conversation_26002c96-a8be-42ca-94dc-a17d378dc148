// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/enterprise/subscription/v1/subscription_api.proto

package subscriptionapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// SubscriptionServiceClient is the client API for SubscriptionService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SubscriptionServiceClient interface {
	// ListCards
	ListCards(ctx context.Context, in *ListCardsParams, opts ...grpc.CallOption) (*ListCardsResponse, error)
	// GetEnterpriseUnit
	GetEnterpriseUnit(ctx context.Context, in *GetEnterpriseUnitParams, opts ...grpc.CallOption) (*GetEnterpriseUnitResponse, error)
}

type subscriptionServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewSubscriptionServiceClient(cc grpc.ClientConnInterface) SubscriptionServiceClient {
	return &subscriptionServiceClient{cc}
}

func (c *subscriptionServiceClient) ListCards(ctx context.Context, in *ListCardsParams, opts ...grpc.CallOption) (*ListCardsResponse, error) {
	out := new(ListCardsResponse)
	err := c.cc.Invoke(ctx, "/moego.enterprise.subscription.v1.SubscriptionService/ListCards", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *subscriptionServiceClient) GetEnterpriseUnit(ctx context.Context, in *GetEnterpriseUnitParams, opts ...grpc.CallOption) (*GetEnterpriseUnitResponse, error) {
	out := new(GetEnterpriseUnitResponse)
	err := c.cc.Invoke(ctx, "/moego.enterprise.subscription.v1.SubscriptionService/GetEnterpriseUnit", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SubscriptionServiceServer is the server API for SubscriptionService service.
// All implementations must embed UnimplementedSubscriptionServiceServer
// for forward compatibility
type SubscriptionServiceServer interface {
	// ListCards
	ListCards(context.Context, *ListCardsParams) (*ListCardsResponse, error)
	// GetEnterpriseUnit
	GetEnterpriseUnit(context.Context, *GetEnterpriseUnitParams) (*GetEnterpriseUnitResponse, error)
	mustEmbedUnimplementedSubscriptionServiceServer()
}

// UnimplementedSubscriptionServiceServer must be embedded to have forward compatible implementations.
type UnimplementedSubscriptionServiceServer struct {
}

func (UnimplementedSubscriptionServiceServer) ListCards(context.Context, *ListCardsParams) (*ListCardsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListCards not implemented")
}
func (UnimplementedSubscriptionServiceServer) GetEnterpriseUnit(context.Context, *GetEnterpriseUnitParams) (*GetEnterpriseUnitResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetEnterpriseUnit not implemented")
}
func (UnimplementedSubscriptionServiceServer) mustEmbedUnimplementedSubscriptionServiceServer() {}

// UnsafeSubscriptionServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SubscriptionServiceServer will
// result in compilation errors.
type UnsafeSubscriptionServiceServer interface {
	mustEmbedUnimplementedSubscriptionServiceServer()
}

func RegisterSubscriptionServiceServer(s grpc.ServiceRegistrar, srv SubscriptionServiceServer) {
	s.RegisterService(&SubscriptionService_ServiceDesc, srv)
}

func _SubscriptionService_ListCards_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListCardsParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubscriptionServiceServer).ListCards(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.subscription.v1.SubscriptionService/ListCards",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubscriptionServiceServer).ListCards(ctx, req.(*ListCardsParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _SubscriptionService_GetEnterpriseUnit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEnterpriseUnitParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubscriptionServiceServer).GetEnterpriseUnit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.subscription.v1.SubscriptionService/GetEnterpriseUnit",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubscriptionServiceServer).GetEnterpriseUnit(ctx, req.(*GetEnterpriseUnitParams))
	}
	return interceptor(ctx, in, info, handler)
}

// SubscriptionService_ServiceDesc is the grpc.ServiceDesc for SubscriptionService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var SubscriptionService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.enterprise.subscription.v1.SubscriptionService",
	HandlerType: (*SubscriptionServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListCards",
			Handler:    _SubscriptionService_ListCards_Handler,
		},
		{
			MethodName: "GetEnterpriseUnit",
			Handler:    _SubscriptionService_GetEnterpriseUnit_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/enterprise/subscription/v1/subscription_api.proto",
}
