// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/enterprise/subscription/v1/subscription_api.proto

package subscriptionapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/payment/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// ListCardsParams
type ListCardsParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Enterprise ID
	EnterpriseId int64 `protobuf:"varint,1,opt,name=enterprise_id,json=enterpriseId,proto3" json:"enterprise_id,omitempty"`
}

func (x *ListCardsParams) Reset() {
	*x = ListCardsParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_subscription_v1_subscription_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListCardsParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCardsParams) ProtoMessage() {}

func (x *ListCardsParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_subscription_v1_subscription_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCardsParams.ProtoReflect.Descriptor instead.
func (*ListCardsParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_subscription_v1_subscription_api_proto_rawDescGZIP(), []int{0}
}

func (x *ListCardsParams) GetEnterpriseId() int64 {
	if x != nil {
		return x.EnterpriseId
	}
	return 0
}

// ListCardsResponse
type ListCardsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Cards
	Cards []*v1.Card `protobuf:"bytes,1,rep,name=cards,proto3" json:"cards,omitempty"`
}

func (x *ListCardsResponse) Reset() {
	*x = ListCardsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_subscription_v1_subscription_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListCardsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCardsResponse) ProtoMessage() {}

func (x *ListCardsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_subscription_v1_subscription_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCardsResponse.ProtoReflect.Descriptor instead.
func (*ListCardsResponse) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_subscription_v1_subscription_api_proto_rawDescGZIP(), []int{1}
}

func (x *ListCardsResponse) GetCards() []*v1.Card {
	if x != nil {
		return x.Cards
	}
	return nil
}

// GetEnterpriseUnitParams
type GetEnterpriseUnitParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetEnterpriseUnitParams) Reset() {
	*x = GetEnterpriseUnitParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_subscription_v1_subscription_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEnterpriseUnitParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEnterpriseUnitParams) ProtoMessage() {}

func (x *GetEnterpriseUnitParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_subscription_v1_subscription_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEnterpriseUnitParams.ProtoReflect.Descriptor instead.
func (*GetEnterpriseUnitParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_subscription_v1_subscription_api_proto_rawDescGZIP(), []int{2}
}

// GetEnterpriseUnitResponse
type GetEnterpriseUnitResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// total location num in subscription
	TotalLocationNum int64 `protobuf:"varint,1,opt,name=total_location_num,json=totalLocationNum,proto3" json:"total_location_num,omitempty"`
	// total van num in subscription
	TotalVanNum int64 `protobuf:"varint,2,opt,name=total_van_num,json=totalVanNum,proto3" json:"total_van_num,omitempty"`
	// used location num
	UsedLocationNum int64 `protobuf:"varint,3,opt,name=used_location_num,json=usedLocationNum,proto3" json:"used_location_num,omitempty"`
	// used van num
	UsedVanNum int64 `protobuf:"varint,4,opt,name=used_van_num,json=usedVanNum,proto3" json:"used_van_num,omitempty"`
}

func (x *GetEnterpriseUnitResponse) Reset() {
	*x = GetEnterpriseUnitResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_subscription_v1_subscription_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEnterpriseUnitResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEnterpriseUnitResponse) ProtoMessage() {}

func (x *GetEnterpriseUnitResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_subscription_v1_subscription_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEnterpriseUnitResponse.ProtoReflect.Descriptor instead.
func (*GetEnterpriseUnitResponse) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_subscription_v1_subscription_api_proto_rawDescGZIP(), []int{3}
}

func (x *GetEnterpriseUnitResponse) GetTotalLocationNum() int64 {
	if x != nil {
		return x.TotalLocationNum
	}
	return 0
}

func (x *GetEnterpriseUnitResponse) GetTotalVanNum() int64 {
	if x != nil {
		return x.TotalVanNum
	}
	return 0
}

func (x *GetEnterpriseUnitResponse) GetUsedLocationNum() int64 {
	if x != nil {
		return x.UsedLocationNum
	}
	return 0
}

func (x *GetEnterpriseUnitResponse) GetUsedVanNum() int64 {
	if x != nil {
		return x.UsedVanNum
	}
	return 0
}

var File_moego_enterprise_subscription_v1_subscription_api_proto protoreflect.FileDescriptor

var file_moego_enterprise_subscription_v1_subscription_api_proto_rawDesc = []byte{
	0x0a, 0x37, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2f,
	0x76, 0x31, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x20, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x73, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x1a, 0x33, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6d, 0x65, 0x74,
	0x68, 0x6f, 0x64, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x3f, 0x0a, 0x0f, 0x4c, 0x69, 0x73,
	0x74, 0x43, 0x61, 0x72, 0x64, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x2c, 0x0a, 0x0d,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0c, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x49, 0x64, 0x22, 0x48, 0x0a, 0x11, 0x4c, 0x69,
	0x73, 0x74, 0x43, 0x61, 0x72, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x33, 0x0a, 0x05, 0x63, 0x61, 0x72, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x52, 0x05, 0x63,
	0x61, 0x72, 0x64, 0x73, 0x22, 0x19, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x55, 0x6e, 0x69, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x22,
	0xdf, 0x01, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73,
	0x65, 0x55, 0x6e, 0x69, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x35, 0x0a,
	0x12, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x6e, 0x75, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x28, 0x00, 0x52, 0x10, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x4e, 0x75, 0x6d, 0x12, 0x2b, 0x0a, 0x0d, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x76, 0x61,
	0x6e, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x28, 0x00, 0x52, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x56, 0x61, 0x6e, 0x4e, 0x75,
	0x6d, 0x12, 0x33, 0x0a, 0x11, 0x75, 0x73, 0x65, 0x64, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x28, 0x00, 0x52, 0x0f, 0x75, 0x73, 0x65, 0x64, 0x4c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x4e, 0x75, 0x6d, 0x12, 0x29, 0x0a, 0x0c, 0x75, 0x73, 0x65, 0x64, 0x5f, 0x76,
	0x61, 0x6e, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x28, 0x00, 0x52, 0x0a, 0x75, 0x73, 0x65, 0x64, 0x56, 0x61, 0x6e, 0x4e, 0x75,
	0x6d, 0x32, 0x9c, 0x02, 0x0a, 0x13, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x75, 0x0a, 0x09, 0x4c, 0x69, 0x73,
	0x74, 0x43, 0x61, 0x72, 0x64, 0x73, 0x12, 0x31, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x61,
	0x72, 0x64, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x73, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x43, 0x61, 0x72, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x12, 0x8d, 0x01, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x55, 0x6e, 0x69, 0x74, 0x12, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x55, 0x6e, 0x69, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x1a, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x55, 0x6e, 0x69, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x42, 0x95, 0x01, 0x0a, 0x28, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69,
	0x64, 0x6c, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x73, 0x75,
	0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a,
	0x67, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47,
	0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61,
	0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f,
	0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x3b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_enterprise_subscription_v1_subscription_api_proto_rawDescOnce sync.Once
	file_moego_enterprise_subscription_v1_subscription_api_proto_rawDescData = file_moego_enterprise_subscription_v1_subscription_api_proto_rawDesc
)

func file_moego_enterprise_subscription_v1_subscription_api_proto_rawDescGZIP() []byte {
	file_moego_enterprise_subscription_v1_subscription_api_proto_rawDescOnce.Do(func() {
		file_moego_enterprise_subscription_v1_subscription_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_enterprise_subscription_v1_subscription_api_proto_rawDescData)
	})
	return file_moego_enterprise_subscription_v1_subscription_api_proto_rawDescData
}

var file_moego_enterprise_subscription_v1_subscription_api_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_moego_enterprise_subscription_v1_subscription_api_proto_goTypes = []interface{}{
	(*ListCardsParams)(nil),           // 0: moego.enterprise.subscription.v1.ListCardsParams
	(*ListCardsResponse)(nil),         // 1: moego.enterprise.subscription.v1.ListCardsResponse
	(*GetEnterpriseUnitParams)(nil),   // 2: moego.enterprise.subscription.v1.GetEnterpriseUnitParams
	(*GetEnterpriseUnitResponse)(nil), // 3: moego.enterprise.subscription.v1.GetEnterpriseUnitResponse
	(*v1.Card)(nil),                   // 4: moego.models.payment.v1.Card
}
var file_moego_enterprise_subscription_v1_subscription_api_proto_depIdxs = []int32{
	4, // 0: moego.enterprise.subscription.v1.ListCardsResponse.cards:type_name -> moego.models.payment.v1.Card
	0, // 1: moego.enterprise.subscription.v1.SubscriptionService.ListCards:input_type -> moego.enterprise.subscription.v1.ListCardsParams
	2, // 2: moego.enterprise.subscription.v1.SubscriptionService.GetEnterpriseUnit:input_type -> moego.enterprise.subscription.v1.GetEnterpriseUnitParams
	1, // 3: moego.enterprise.subscription.v1.SubscriptionService.ListCards:output_type -> moego.enterprise.subscription.v1.ListCardsResponse
	3, // 4: moego.enterprise.subscription.v1.SubscriptionService.GetEnterpriseUnit:output_type -> moego.enterprise.subscription.v1.GetEnterpriseUnitResponse
	3, // [3:5] is the sub-list for method output_type
	1, // [1:3] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_moego_enterprise_subscription_v1_subscription_api_proto_init() }
func file_moego_enterprise_subscription_v1_subscription_api_proto_init() {
	if File_moego_enterprise_subscription_v1_subscription_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_enterprise_subscription_v1_subscription_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListCardsParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_subscription_v1_subscription_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListCardsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_subscription_v1_subscription_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEnterpriseUnitParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_subscription_v1_subscription_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEnterpriseUnitResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_enterprise_subscription_v1_subscription_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_enterprise_subscription_v1_subscription_api_proto_goTypes,
		DependencyIndexes: file_moego_enterprise_subscription_v1_subscription_api_proto_depIdxs,
		MessageInfos:      file_moego_enterprise_subscription_v1_subscription_api_proto_msgTypes,
	}.Build()
	File_moego_enterprise_subscription_v1_subscription_api_proto = out.File
	file_moego_enterprise_subscription_v1_subscription_api_proto_rawDesc = nil
	file_moego_enterprise_subscription_v1_subscription_api_proto_goTypes = nil
	file_moego_enterprise_subscription_v1_subscription_api_proto_depIdxs = nil
}
