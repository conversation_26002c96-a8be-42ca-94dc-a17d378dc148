// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/enterprise/configuration/v1/pet_settings.proto

package configurationapipb

import (
	v12 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1"
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/enterprise/v1"
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/enterprise/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// create pet code params
type CreatePetCodeParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet code
	PetCodeDef *v1.PetCodeCreateDef `protobuf:"bytes,1,opt,name=pet_code_def,json=petCodeDef,proto3" json:"pet_code_def,omitempty"`
}

func (x *CreatePetCodeParams) Reset() {
	*x = CreatePetCodeParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_configuration_v1_pet_settings_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreatePetCodeParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePetCodeParams) ProtoMessage() {}

func (x *CreatePetCodeParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_configuration_v1_pet_settings_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePetCodeParams.ProtoReflect.Descriptor instead.
func (*CreatePetCodeParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_configuration_v1_pet_settings_proto_rawDescGZIP(), []int{0}
}

func (x *CreatePetCodeParams) GetPetCodeDef() *v1.PetCodeCreateDef {
	if x != nil {
		return x.PetCodeDef
	}
	return nil
}

// create pet code result
type CreatePetCodeResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet code
	PetCode *v1.PetCode `protobuf:"bytes,1,opt,name=pet_code,json=petCode,proto3" json:"pet_code,omitempty"`
}

func (x *CreatePetCodeResult) Reset() {
	*x = CreatePetCodeResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_configuration_v1_pet_settings_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreatePetCodeResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePetCodeResult) ProtoMessage() {}

func (x *CreatePetCodeResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_configuration_v1_pet_settings_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePetCodeResult.ProtoReflect.Descriptor instead.
func (*CreatePetCodeResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_configuration_v1_pet_settings_proto_rawDescGZIP(), []int{1}
}

func (x *CreatePetCodeResult) GetPetCode() *v1.PetCode {
	if x != nil {
		return x.PetCode
	}
	return nil
}

// update pet code params
type UpdatePetCodeParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id of the pet code
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// update pet code
	PetCodeDef *v1.PetCodeUpdateDef `protobuf:"bytes,2,opt,name=pet_code_def,json=petCodeDef,proto3" json:"pet_code_def,omitempty"`
}

func (x *UpdatePetCodeParams) Reset() {
	*x = UpdatePetCodeParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_configuration_v1_pet_settings_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdatePetCodeParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePetCodeParams) ProtoMessage() {}

func (x *UpdatePetCodeParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_configuration_v1_pet_settings_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePetCodeParams.ProtoReflect.Descriptor instead.
func (*UpdatePetCodeParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_configuration_v1_pet_settings_proto_rawDescGZIP(), []int{2}
}

func (x *UpdatePetCodeParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdatePetCodeParams) GetPetCodeDef() *v1.PetCodeUpdateDef {
	if x != nil {
		return x.PetCodeDef
	}
	return nil
}

// update pet code result
type UpdatePetCodeResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet code
	PetCode *v1.PetCode `protobuf:"bytes,1,opt,name=pet_code,json=petCode,proto3" json:"pet_code,omitempty"`
}

func (x *UpdatePetCodeResult) Reset() {
	*x = UpdatePetCodeResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_configuration_v1_pet_settings_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdatePetCodeResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePetCodeResult) ProtoMessage() {}

func (x *UpdatePetCodeResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_configuration_v1_pet_settings_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePetCodeResult.ProtoReflect.Descriptor instead.
func (*UpdatePetCodeResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_configuration_v1_pet_settings_proto_rawDescGZIP(), []int{3}
}

func (x *UpdatePetCodeResult) GetPetCode() *v1.PetCode {
	if x != nil {
		return x.PetCode
	}
	return nil
}

// list pet codes params
type ListPetCodesParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// filter
	Filter *ListPetCodesParams_Filter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
}

func (x *ListPetCodesParams) Reset() {
	*x = ListPetCodesParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_configuration_v1_pet_settings_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPetCodesParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPetCodesParams) ProtoMessage() {}

func (x *ListPetCodesParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_configuration_v1_pet_settings_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPetCodesParams.ProtoReflect.Descriptor instead.
func (*ListPetCodesParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_configuration_v1_pet_settings_proto_rawDescGZIP(), []int{4}
}

func (x *ListPetCodesParams) GetFilter() *ListPetCodesParams_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

// list pet codes result
type ListPetCodesResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet codes
	PetCodes []*v1.PetCode `protobuf:"bytes,1,rep,name=pet_codes,json=petCodes,proto3" json:"pet_codes,omitempty"`
}

func (x *ListPetCodesResult) Reset() {
	*x = ListPetCodesResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_configuration_v1_pet_settings_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPetCodesResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPetCodesResult) ProtoMessage() {}

func (x *ListPetCodesResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_configuration_v1_pet_settings_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPetCodesResult.ProtoReflect.Descriptor instead.
func (*ListPetCodesResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_configuration_v1_pet_settings_proto_rawDescGZIP(), []int{5}
}

func (x *ListPetCodesResult) GetPetCodes() []*v1.PetCode {
	if x != nil {
		return x.PetCodes
	}
	return nil
}

// delete pet code params
type DeletePetCodeParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id of the pet code
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *DeletePetCodeParams) Reset() {
	*x = DeletePetCodeParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_configuration_v1_pet_settings_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeletePetCodeParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePetCodeParams) ProtoMessage() {}

func (x *DeletePetCodeParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_configuration_v1_pet_settings_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePetCodeParams.ProtoReflect.Descriptor instead.
func (*DeletePetCodeParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_configuration_v1_pet_settings_proto_rawDescGZIP(), []int{6}
}

func (x *DeletePetCodeParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// delete pet code result
type DeletePetCodeResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeletePetCodeResult) Reset() {
	*x = DeletePetCodeResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_configuration_v1_pet_settings_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeletePetCodeResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePetCodeResult) ProtoMessage() {}

func (x *DeletePetCodeResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_configuration_v1_pet_settings_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePetCodeResult.ProtoReflect.Descriptor instead.
func (*DeletePetCodeResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_configuration_v1_pet_settings_proto_rawDescGZIP(), []int{7}
}

// sort pet codes params
type SortPetCodesParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet code ids
	Ids []int64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
}

func (x *SortPetCodesParams) Reset() {
	*x = SortPetCodesParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_configuration_v1_pet_settings_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SortPetCodesParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SortPetCodesParams) ProtoMessage() {}

func (x *SortPetCodesParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_configuration_v1_pet_settings_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SortPetCodesParams.ProtoReflect.Descriptor instead.
func (*SortPetCodesParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_configuration_v1_pet_settings_proto_rawDescGZIP(), []int{8}
}

func (x *SortPetCodesParams) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

// sort pet codes result
type SortPetCodesResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SortPetCodesResult) Reset() {
	*x = SortPetCodesResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_configuration_v1_pet_settings_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SortPetCodesResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SortPetCodesResult) ProtoMessage() {}

func (x *SortPetCodesResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_configuration_v1_pet_settings_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SortPetCodesResult.ProtoReflect.Descriptor instead.
func (*SortPetCodesResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_configuration_v1_pet_settings_proto_rawDescGZIP(), []int{9}
}

// PushPetCodesParams
type PushPetCodesParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet code ids
	Ids []int64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	// targets
	Targets []*v1.TenantObject `protobuf:"bytes,2,rep,name=targets,proto3" json:"targets,omitempty"`
}

func (x *PushPetCodesParams) Reset() {
	*x = PushPetCodesParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_configuration_v1_pet_settings_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PushPetCodesParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushPetCodesParams) ProtoMessage() {}

func (x *PushPetCodesParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_configuration_v1_pet_settings_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushPetCodesParams.ProtoReflect.Descriptor instead.
func (*PushPetCodesParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_configuration_v1_pet_settings_proto_rawDescGZIP(), []int{10}
}

func (x *PushPetCodesParams) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *PushPetCodesParams) GetTargets() []*v1.TenantObject {
	if x != nil {
		return x.Targets
	}
	return nil
}

// PushPetCodesResult
type PushPetCodesResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// success company ids
	SuccessCompanyIds []int64 `protobuf:"varint,1,rep,packed,name=success_company_ids,json=successCompanyIds,proto3" json:"success_company_ids,omitempty"`
	// failed company ids
	FailedCompanyIds []int64 `protobuf:"varint,2,rep,packed,name=failed_company_ids,json=failedCompanyIds,proto3" json:"failed_company_ids,omitempty"`
}

func (x *PushPetCodesResult) Reset() {
	*x = PushPetCodesResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_configuration_v1_pet_settings_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PushPetCodesResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushPetCodesResult) ProtoMessage() {}

func (x *PushPetCodesResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_configuration_v1_pet_settings_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushPetCodesResult.ProtoReflect.Descriptor instead.
func (*PushPetCodesResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_configuration_v1_pet_settings_proto_rawDescGZIP(), []int{11}
}

func (x *PushPetCodesResult) GetSuccessCompanyIds() []int64 {
	if x != nil {
		return x.SuccessCompanyIds
	}
	return nil
}

func (x *PushPetCodesResult) GetFailedCompanyIds() []int64 {
	if x != nil {
		return x.FailedCompanyIds
	}
	return nil
}

// CreatePetMetadataParams
type CreatePetMetadataParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// def
	Def *v1.PetMetadataCreateDef `protobuf:"bytes,1,opt,name=def,proto3" json:"def,omitempty"`
}

func (x *CreatePetMetadataParams) Reset() {
	*x = CreatePetMetadataParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_configuration_v1_pet_settings_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreatePetMetadataParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePetMetadataParams) ProtoMessage() {}

func (x *CreatePetMetadataParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_configuration_v1_pet_settings_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePetMetadataParams.ProtoReflect.Descriptor instead.
func (*CreatePetMetadataParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_configuration_v1_pet_settings_proto_rawDescGZIP(), []int{12}
}

func (x *CreatePetMetadataParams) GetDef() *v1.PetMetadataCreateDef {
	if x != nil {
		return x.Def
	}
	return nil
}

// CreatePetMetadataResult
type CreatePetMetadataResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet metadata
	PetMetadata *v1.PetMetadata `protobuf:"bytes,1,opt,name=pet_metadata,json=petMetadata,proto3" json:"pet_metadata,omitempty"`
}

func (x *CreatePetMetadataResult) Reset() {
	*x = CreatePetMetadataResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_configuration_v1_pet_settings_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreatePetMetadataResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePetMetadataResult) ProtoMessage() {}

func (x *CreatePetMetadataResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_configuration_v1_pet_settings_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePetMetadataResult.ProtoReflect.Descriptor instead.
func (*CreatePetMetadataResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_configuration_v1_pet_settings_proto_rawDescGZIP(), []int{13}
}

func (x *CreatePetMetadataResult) GetPetMetadata() *v1.PetMetadata {
	if x != nil {
		return x.PetMetadata
	}
	return nil
}

// UpdatePetMetadataParams
type UpdatePetMetadataParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id of the pet metadata
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// update def
	Def *v1.PetMetadataUpdateDef `protobuf:"bytes,2,opt,name=def,proto3" json:"def,omitempty"`
}

func (x *UpdatePetMetadataParams) Reset() {
	*x = UpdatePetMetadataParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_configuration_v1_pet_settings_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdatePetMetadataParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePetMetadataParams) ProtoMessage() {}

func (x *UpdatePetMetadataParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_configuration_v1_pet_settings_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePetMetadataParams.ProtoReflect.Descriptor instead.
func (*UpdatePetMetadataParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_configuration_v1_pet_settings_proto_rawDescGZIP(), []int{14}
}

func (x *UpdatePetMetadataParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdatePetMetadataParams) GetDef() *v1.PetMetadataUpdateDef {
	if x != nil {
		return x.Def
	}
	return nil
}

// UpdatePetMetadataResult
type UpdatePetMetadataResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet metadata
	PetMetadata *v1.PetMetadata `protobuf:"bytes,1,opt,name=pet_metadata,json=petMetadata,proto3" json:"pet_metadata,omitempty"`
}

func (x *UpdatePetMetadataResult) Reset() {
	*x = UpdatePetMetadataResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_configuration_v1_pet_settings_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdatePetMetadataResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePetMetadataResult) ProtoMessage() {}

func (x *UpdatePetMetadataResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_configuration_v1_pet_settings_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePetMetadataResult.ProtoReflect.Descriptor instead.
func (*UpdatePetMetadataResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_configuration_v1_pet_settings_proto_rawDescGZIP(), []int{15}
}

func (x *UpdatePetMetadataResult) GetPetMetadata() *v1.PetMetadata {
	if x != nil {
		return x.PetMetadata
	}
	return nil
}

// ListPetMetadataParams
type ListPetMetadataParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// filter
	Filter *v11.ListPetMetadataRequest_Filter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
}

func (x *ListPetMetadataParams) Reset() {
	*x = ListPetMetadataParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_configuration_v1_pet_settings_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPetMetadataParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPetMetadataParams) ProtoMessage() {}

func (x *ListPetMetadataParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_configuration_v1_pet_settings_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPetMetadataParams.ProtoReflect.Descriptor instead.
func (*ListPetMetadataParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_configuration_v1_pet_settings_proto_rawDescGZIP(), []int{16}
}

func (x *ListPetMetadataParams) GetFilter() *v11.ListPetMetadataRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

// ListPetMetadataResult
type ListPetMetadataResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet metadata
	PetMetadata []*v1.PetMetadata `protobuf:"bytes,1,rep,name=pet_metadata,json=petMetadata,proto3" json:"pet_metadata,omitempty"`
}

func (x *ListPetMetadataResult) Reset() {
	*x = ListPetMetadataResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_configuration_v1_pet_settings_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPetMetadataResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPetMetadataResult) ProtoMessage() {}

func (x *ListPetMetadataResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_configuration_v1_pet_settings_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPetMetadataResult.ProtoReflect.Descriptor instead.
func (*ListPetMetadataResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_configuration_v1_pet_settings_proto_rawDescGZIP(), []int{17}
}

func (x *ListPetMetadataResult) GetPetMetadata() []*v1.PetMetadata {
	if x != nil {
		return x.PetMetadata
	}
	return nil
}

// DeletePetMetadataParams
type DeletePetMetadataParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id of the pet metadata
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *DeletePetMetadataParams) Reset() {
	*x = DeletePetMetadataParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_configuration_v1_pet_settings_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeletePetMetadataParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePetMetadataParams) ProtoMessage() {}

func (x *DeletePetMetadataParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_configuration_v1_pet_settings_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePetMetadataParams.ProtoReflect.Descriptor instead.
func (*DeletePetMetadataParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_configuration_v1_pet_settings_proto_rawDescGZIP(), []int{18}
}

func (x *DeletePetMetadataParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// DeletePetMetadataResult
type DeletePetMetadataResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeletePetMetadataResult) Reset() {
	*x = DeletePetMetadataResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_configuration_v1_pet_settings_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeletePetMetadataResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePetMetadataResult) ProtoMessage() {}

func (x *DeletePetMetadataResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_configuration_v1_pet_settings_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePetMetadataResult.ProtoReflect.Descriptor instead.
func (*DeletePetMetadataResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_configuration_v1_pet_settings_proto_rawDescGZIP(), []int{19}
}

// SortPetMetadataParams
type SortPetMetadataParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ids
	Ids []int64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
}

func (x *SortPetMetadataParams) Reset() {
	*x = SortPetMetadataParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_configuration_v1_pet_settings_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SortPetMetadataParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SortPetMetadataParams) ProtoMessage() {}

func (x *SortPetMetadataParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_configuration_v1_pet_settings_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SortPetMetadataParams.ProtoReflect.Descriptor instead.
func (*SortPetMetadataParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_configuration_v1_pet_settings_proto_rawDescGZIP(), []int{20}
}

func (x *SortPetMetadataParams) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

// SortPetMetadataResult
type SortPetMetadataResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SortPetMetadataResult) Reset() {
	*x = SortPetMetadataResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_configuration_v1_pet_settings_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SortPetMetadataResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SortPetMetadataResult) ProtoMessage() {}

func (x *SortPetMetadataResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_configuration_v1_pet_settings_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SortPetMetadataResult.ProtoReflect.Descriptor instead.
func (*SortPetMetadataResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_configuration_v1_pet_settings_proto_rawDescGZIP(), []int{21}
}

// PushPetMetadataParams
type PushPetMetadataParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// metadata name
	MetadataNames []v12.BusinessPetMetadataName `protobuf:"varint,1,rep,packed,name=metadata_names,json=metadataNames,proto3,enum=moego.models.business_customer.v1.BusinessPetMetadataName" json:"metadata_names,omitempty"`
	// targets
	Targets []*v1.TenantObject `protobuf:"bytes,2,rep,name=targets,proto3" json:"targets,omitempty"`
}

func (x *PushPetMetadataParams) Reset() {
	*x = PushPetMetadataParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_configuration_v1_pet_settings_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PushPetMetadataParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushPetMetadataParams) ProtoMessage() {}

func (x *PushPetMetadataParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_configuration_v1_pet_settings_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushPetMetadataParams.ProtoReflect.Descriptor instead.
func (*PushPetMetadataParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_configuration_v1_pet_settings_proto_rawDescGZIP(), []int{22}
}

func (x *PushPetMetadataParams) GetMetadataNames() []v12.BusinessPetMetadataName {
	if x != nil {
		return x.MetadataNames
	}
	return nil
}

func (x *PushPetMetadataParams) GetTargets() []*v1.TenantObject {
	if x != nil {
		return x.Targets
	}
	return nil
}

// PushPetMetadataResult
type PushPetMetadataResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// success company ids
	SuccessCompanyIds []int64 `protobuf:"varint,1,rep,packed,name=success_company_ids,json=successCompanyIds,proto3" json:"success_company_ids,omitempty"`
	// failed company ids
	FailedCompanyIds []int64 `protobuf:"varint,2,rep,packed,name=failed_company_ids,json=failedCompanyIds,proto3" json:"failed_company_ids,omitempty"`
}

func (x *PushPetMetadataResult) Reset() {
	*x = PushPetMetadataResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_configuration_v1_pet_settings_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PushPetMetadataResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushPetMetadataResult) ProtoMessage() {}

func (x *PushPetMetadataResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_configuration_v1_pet_settings_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushPetMetadataResult.ProtoReflect.Descriptor instead.
func (*PushPetMetadataResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_configuration_v1_pet_settings_proto_rawDescGZIP(), []int{23}
}

func (x *PushPetMetadataResult) GetSuccessCompanyIds() []int64 {
	if x != nil {
		return x.SuccessCompanyIds
	}
	return nil
}

func (x *PushPetMetadataResult) GetFailedCompanyIds() []int64 {
	if x != nil {
		return x.FailedCompanyIds
	}
	return nil
}

// filter
type ListPetCodesParams_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ids
	Ids []int64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
}

func (x *ListPetCodesParams_Filter) Reset() {
	*x = ListPetCodesParams_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_configuration_v1_pet_settings_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPetCodesParams_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPetCodesParams_Filter) ProtoMessage() {}

func (x *ListPetCodesParams_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_configuration_v1_pet_settings_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPetCodesParams_Filter.ProtoReflect.Descriptor instead.
func (*ListPetCodesParams_Filter) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_configuration_v1_pet_settings_proto_rawDescGZIP(), []int{4, 0}
}

func (x *ListPetCodesParams_Filter) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

var File_moego_enterprise_configuration_v1_pet_settings_proto protoreflect.FileDescriptor

var file_moego_enterprise_configuration_v1_pet_settings_proto_rawDesc = []byte{
	0x0a, 0x34, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2f, 0x76, 0x31, 0x2f, 0x70, 0x65, 0x74, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x21, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x1a, 0x43, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73,
	0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61,
	0x74, 0x61, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x34,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x65, 0x74, 0x5f, 0x73,
	0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x76, 0x31,
	0x2f, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x76,
	0x31, 0x2f, 0x70, 0x65, 0x74, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x6f, 0x0a,
	0x13, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x12, 0x58, 0x0a, 0x0c, 0x70, 0x65, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65,
	0x5f, 0x64, 0x65, 0x66, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02,
	0x10, 0x01, 0x52, 0x0a, 0x70, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x44, 0x65, 0x66, 0x22, 0x55,
	0x0a, 0x13, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x3e, 0x0a, 0x08, 0x70, 0x65, 0x74, 0x5f, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x07, 0x70, 0x65,
	0x74, 0x43, 0x6f, 0x64, 0x65, 0x22, 0x88, 0x01, 0x0a, 0x13, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x50, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x17, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x58, 0x0a, 0x0c, 0x70, 0x65, 0x74, 0x5f, 0x63, 0x6f,
	0x64, 0x65, 0x5f, 0x64, 0x65, 0x66, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x64,
	0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a,
	0x01, 0x02, 0x10, 0x01, 0x52, 0x0a, 0x70, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x44, 0x65, 0x66,
	0x22, 0x55, 0x0a, 0x13, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x64,
	0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x3e, 0x0a, 0x08, 0x70, 0x65, 0x74, 0x5f, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x07,
	0x70, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x22, 0x86, 0x01, 0x0a, 0x12, 0x4c, 0x69, 0x73, 0x74,
	0x50, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x54,
	0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3c,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73,
	0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x73, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x06, 0x66, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x1a, 0x1a, 0x0a, 0x06, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x10,
	0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x03, 0x69, 0x64, 0x73,
	0x22, 0x56, 0x0a, 0x12, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x73,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x40, 0x0a, 0x09, 0x70, 0x65, 0x74, 0x5f, 0x63, 0x6f,
	0x64, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x08,
	0x70, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x73, 0x22, 0x2e, 0x0a, 0x13, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12,
	0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x22, 0x15, 0x0a, 0x13, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22,
	0x37, 0x0a, 0x12, 0x53, 0x6f, 0x72, 0x74, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x73, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x21, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x03, 0x42, 0x0f, 0xfa, 0x42, 0x0c, 0x92, 0x01, 0x09, 0x10, 0xe8, 0x07, 0x22, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x03, 0x69, 0x64, 0x73, 0x22, 0x14, 0x0a, 0x12, 0x53, 0x6f, 0x72, 0x74,
	0x50, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x85,
	0x01, 0x0a, 0x12, 0x50, 0x75, 0x73, 0x68, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x73, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x21, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x03, 0x42, 0x0f, 0xfa, 0x42, 0x0c, 0x92, 0x01, 0x09, 0x10, 0xe8, 0x07, 0x22, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x03, 0x69, 0x64, 0x73, 0x12, 0x4c, 0x0a, 0x07, 0x74, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x4f, 0x62, 0x6a,
	0x65, 0x63, 0x74, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x08, 0x01, 0x52, 0x07, 0x74,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x73, 0x22, 0x72, 0x0a, 0x12, 0x50, 0x75, 0x73, 0x68, 0x50, 0x65,
	0x74, 0x43, 0x6f, 0x64, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x2e, 0x0a, 0x13,
	0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f,
	0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x11, 0x73, 0x75, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x73, 0x12, 0x2c, 0x0a, 0x12,
	0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69,
	0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x52, 0x10, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64,
	0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x73, 0x22, 0x67, 0x0a, 0x17, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x4c, 0x0a, 0x03, 0x64, 0x65, 0x66, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x50, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x03,
	0x64, 0x65, 0x66, 0x22, 0x65, 0x0a, 0x17, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74,
	0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x4a,
	0x0a, 0x0c, 0x70, 0x65, 0x74, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x50, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x0b, 0x70,
	0x65, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x22, 0x80, 0x01, 0x0a, 0x17, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x4c, 0x0a, 0x03, 0x64, 0x65, 0x66, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x4d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x42, 0x08,
	0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x03, 0x64, 0x65, 0x66, 0x22, 0x65, 0x0a,
	0x17, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61,
	0x74, 0x61, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x4a, 0x0a, 0x0c, 0x70, 0x65, 0x74, 0x5f,
	0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x4d,
	0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x0b, 0x70, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0x22, 0x75, 0x0a, 0x15, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x4d,
	0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x5c, 0x0a,
	0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3a, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x50, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01,
	0x02, 0x10, 0x01, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x22, 0x63, 0x0a, 0x15, 0x4c,
	0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x4a, 0x0a, 0x0c, 0x70, 0x65, 0x74, 0x5f, 0x6d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64,
	0x61, 0x74, 0x61, 0x52, 0x0b, 0x70, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61,
	0x22, 0x32, 0x0a, 0x17, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x65, 0x74, 0x4d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x17, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x52, 0x02, 0x69, 0x64, 0x22, 0x19, 0x0a, 0x17, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x65,
	0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22,
	0x3a, 0x0a, 0x15, 0x53, 0x6f, 0x72, 0x74, 0x50, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61,
	0x74, 0x61, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x21, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x03, 0x42, 0x0f, 0xfa, 0x42, 0x0c, 0x92, 0x01, 0x09, 0x10, 0xe8, 0x07,
	0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x03, 0x69, 0x64, 0x73, 0x22, 0x17, 0x0a, 0x15, 0x53,
	0x6f, 0x72, 0x74, 0x50, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x22, 0xd4, 0x01, 0x0a, 0x15, 0x50, 0x75, 0x73, 0x68, 0x50, 0x65, 0x74,
	0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x6d,
	0x0a, 0x0e, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e,
	0x65, 0x73, 0x73, 0x50, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x4e, 0x61,
	0x6d, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x92, 0x01, 0x04, 0x08, 0x01, 0x18, 0x01, 0x52, 0x0d,
	0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x12, 0x4c, 0x0a,
	0x07, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x6e, 0x61,
	0x6e, 0x74, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02,
	0x08, 0x01, 0x52, 0x07, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x73, 0x22, 0x75, 0x0a, 0x15, 0x50,
	0x75, 0x73, 0x68, 0x50, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x2e, 0x0a, 0x13, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f,
	0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x03, 0x52, 0x11, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x49, 0x64, 0x73, 0x12, 0x2c, 0x0a, 0x12, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x5f, 0x63,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03,
	0x52, 0x10, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49,
	0x64, 0x73, 0x42, 0x98, 0x01, 0x0a, 0x29, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x50, 0x01, 0x5a, 0x69, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d,
	0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x3b, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_enterprise_configuration_v1_pet_settings_proto_rawDescOnce sync.Once
	file_moego_enterprise_configuration_v1_pet_settings_proto_rawDescData = file_moego_enterprise_configuration_v1_pet_settings_proto_rawDesc
)

func file_moego_enterprise_configuration_v1_pet_settings_proto_rawDescGZIP() []byte {
	file_moego_enterprise_configuration_v1_pet_settings_proto_rawDescOnce.Do(func() {
		file_moego_enterprise_configuration_v1_pet_settings_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_enterprise_configuration_v1_pet_settings_proto_rawDescData)
	})
	return file_moego_enterprise_configuration_v1_pet_settings_proto_rawDescData
}

var file_moego_enterprise_configuration_v1_pet_settings_proto_msgTypes = make([]protoimpl.MessageInfo, 25)
var file_moego_enterprise_configuration_v1_pet_settings_proto_goTypes = []interface{}{
	(*CreatePetCodeParams)(nil),               // 0: moego.enterprise.configuration.v1.CreatePetCodeParams
	(*CreatePetCodeResult)(nil),               // 1: moego.enterprise.configuration.v1.CreatePetCodeResult
	(*UpdatePetCodeParams)(nil),               // 2: moego.enterprise.configuration.v1.UpdatePetCodeParams
	(*UpdatePetCodeResult)(nil),               // 3: moego.enterprise.configuration.v1.UpdatePetCodeResult
	(*ListPetCodesParams)(nil),                // 4: moego.enterprise.configuration.v1.ListPetCodesParams
	(*ListPetCodesResult)(nil),                // 5: moego.enterprise.configuration.v1.ListPetCodesResult
	(*DeletePetCodeParams)(nil),               // 6: moego.enterprise.configuration.v1.DeletePetCodeParams
	(*DeletePetCodeResult)(nil),               // 7: moego.enterprise.configuration.v1.DeletePetCodeResult
	(*SortPetCodesParams)(nil),                // 8: moego.enterprise.configuration.v1.SortPetCodesParams
	(*SortPetCodesResult)(nil),                // 9: moego.enterprise.configuration.v1.SortPetCodesResult
	(*PushPetCodesParams)(nil),                // 10: moego.enterprise.configuration.v1.PushPetCodesParams
	(*PushPetCodesResult)(nil),                // 11: moego.enterprise.configuration.v1.PushPetCodesResult
	(*CreatePetMetadataParams)(nil),           // 12: moego.enterprise.configuration.v1.CreatePetMetadataParams
	(*CreatePetMetadataResult)(nil),           // 13: moego.enterprise.configuration.v1.CreatePetMetadataResult
	(*UpdatePetMetadataParams)(nil),           // 14: moego.enterprise.configuration.v1.UpdatePetMetadataParams
	(*UpdatePetMetadataResult)(nil),           // 15: moego.enterprise.configuration.v1.UpdatePetMetadataResult
	(*ListPetMetadataParams)(nil),             // 16: moego.enterprise.configuration.v1.ListPetMetadataParams
	(*ListPetMetadataResult)(nil),             // 17: moego.enterprise.configuration.v1.ListPetMetadataResult
	(*DeletePetMetadataParams)(nil),           // 18: moego.enterprise.configuration.v1.DeletePetMetadataParams
	(*DeletePetMetadataResult)(nil),           // 19: moego.enterprise.configuration.v1.DeletePetMetadataResult
	(*SortPetMetadataParams)(nil),             // 20: moego.enterprise.configuration.v1.SortPetMetadataParams
	(*SortPetMetadataResult)(nil),             // 21: moego.enterprise.configuration.v1.SortPetMetadataResult
	(*PushPetMetadataParams)(nil),             // 22: moego.enterprise.configuration.v1.PushPetMetadataParams
	(*PushPetMetadataResult)(nil),             // 23: moego.enterprise.configuration.v1.PushPetMetadataResult
	(*ListPetCodesParams_Filter)(nil),         // 24: moego.enterprise.configuration.v1.ListPetCodesParams.Filter
	(*v1.PetCodeCreateDef)(nil),               // 25: moego.models.enterprise.v1.PetCodeCreateDef
	(*v1.PetCode)(nil),                        // 26: moego.models.enterprise.v1.PetCode
	(*v1.PetCodeUpdateDef)(nil),               // 27: moego.models.enterprise.v1.PetCodeUpdateDef
	(*v1.TenantObject)(nil),                   // 28: moego.models.enterprise.v1.TenantObject
	(*v1.PetMetadataCreateDef)(nil),           // 29: moego.models.enterprise.v1.PetMetadataCreateDef
	(*v1.PetMetadata)(nil),                    // 30: moego.models.enterprise.v1.PetMetadata
	(*v1.PetMetadataUpdateDef)(nil),           // 31: moego.models.enterprise.v1.PetMetadataUpdateDef
	(*v11.ListPetMetadataRequest_Filter)(nil), // 32: moego.service.enterprise.v1.ListPetMetadataRequest.Filter
	(v12.BusinessPetMetadataName)(0),          // 33: moego.models.business_customer.v1.BusinessPetMetadataName
}
var file_moego_enterprise_configuration_v1_pet_settings_proto_depIdxs = []int32{
	25, // 0: moego.enterprise.configuration.v1.CreatePetCodeParams.pet_code_def:type_name -> moego.models.enterprise.v1.PetCodeCreateDef
	26, // 1: moego.enterprise.configuration.v1.CreatePetCodeResult.pet_code:type_name -> moego.models.enterprise.v1.PetCode
	27, // 2: moego.enterprise.configuration.v1.UpdatePetCodeParams.pet_code_def:type_name -> moego.models.enterprise.v1.PetCodeUpdateDef
	26, // 3: moego.enterprise.configuration.v1.UpdatePetCodeResult.pet_code:type_name -> moego.models.enterprise.v1.PetCode
	24, // 4: moego.enterprise.configuration.v1.ListPetCodesParams.filter:type_name -> moego.enterprise.configuration.v1.ListPetCodesParams.Filter
	26, // 5: moego.enterprise.configuration.v1.ListPetCodesResult.pet_codes:type_name -> moego.models.enterprise.v1.PetCode
	28, // 6: moego.enterprise.configuration.v1.PushPetCodesParams.targets:type_name -> moego.models.enterprise.v1.TenantObject
	29, // 7: moego.enterprise.configuration.v1.CreatePetMetadataParams.def:type_name -> moego.models.enterprise.v1.PetMetadataCreateDef
	30, // 8: moego.enterprise.configuration.v1.CreatePetMetadataResult.pet_metadata:type_name -> moego.models.enterprise.v1.PetMetadata
	31, // 9: moego.enterprise.configuration.v1.UpdatePetMetadataParams.def:type_name -> moego.models.enterprise.v1.PetMetadataUpdateDef
	30, // 10: moego.enterprise.configuration.v1.UpdatePetMetadataResult.pet_metadata:type_name -> moego.models.enterprise.v1.PetMetadata
	32, // 11: moego.enterprise.configuration.v1.ListPetMetadataParams.filter:type_name -> moego.service.enterprise.v1.ListPetMetadataRequest.Filter
	30, // 12: moego.enterprise.configuration.v1.ListPetMetadataResult.pet_metadata:type_name -> moego.models.enterprise.v1.PetMetadata
	33, // 13: moego.enterprise.configuration.v1.PushPetMetadataParams.metadata_names:type_name -> moego.models.business_customer.v1.BusinessPetMetadataName
	28, // 14: moego.enterprise.configuration.v1.PushPetMetadataParams.targets:type_name -> moego.models.enterprise.v1.TenantObject
	15, // [15:15] is the sub-list for method output_type
	15, // [15:15] is the sub-list for method input_type
	15, // [15:15] is the sub-list for extension type_name
	15, // [15:15] is the sub-list for extension extendee
	0,  // [0:15] is the sub-list for field type_name
}

func init() { file_moego_enterprise_configuration_v1_pet_settings_proto_init() }
func file_moego_enterprise_configuration_v1_pet_settings_proto_init() {
	if File_moego_enterprise_configuration_v1_pet_settings_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_enterprise_configuration_v1_pet_settings_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreatePetCodeParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_configuration_v1_pet_settings_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreatePetCodeResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_configuration_v1_pet_settings_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdatePetCodeParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_configuration_v1_pet_settings_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdatePetCodeResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_configuration_v1_pet_settings_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPetCodesParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_configuration_v1_pet_settings_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPetCodesResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_configuration_v1_pet_settings_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeletePetCodeParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_configuration_v1_pet_settings_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeletePetCodeResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_configuration_v1_pet_settings_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SortPetCodesParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_configuration_v1_pet_settings_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SortPetCodesResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_configuration_v1_pet_settings_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PushPetCodesParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_configuration_v1_pet_settings_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PushPetCodesResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_configuration_v1_pet_settings_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreatePetMetadataParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_configuration_v1_pet_settings_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreatePetMetadataResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_configuration_v1_pet_settings_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdatePetMetadataParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_configuration_v1_pet_settings_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdatePetMetadataResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_configuration_v1_pet_settings_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPetMetadataParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_configuration_v1_pet_settings_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPetMetadataResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_configuration_v1_pet_settings_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeletePetMetadataParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_configuration_v1_pet_settings_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeletePetMetadataResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_configuration_v1_pet_settings_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SortPetMetadataParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_configuration_v1_pet_settings_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SortPetMetadataResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_configuration_v1_pet_settings_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PushPetMetadataParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_configuration_v1_pet_settings_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PushPetMetadataResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_configuration_v1_pet_settings_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPetCodesParams_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_enterprise_configuration_v1_pet_settings_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   25,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_enterprise_configuration_v1_pet_settings_proto_goTypes,
		DependencyIndexes: file_moego_enterprise_configuration_v1_pet_settings_proto_depIdxs,
		MessageInfos:      file_moego_enterprise_configuration_v1_pet_settings_proto_msgTypes,
	}.Build()
	File_moego_enterprise_configuration_v1_pet_settings_proto = out.File
	file_moego_enterprise_configuration_v1_pet_settings_proto_rawDesc = nil
	file_moego_enterprise_configuration_v1_pet_settings_proto_goTypes = nil
	file_moego_enterprise_configuration_v1_pet_settings_proto_depIdxs = nil
}
