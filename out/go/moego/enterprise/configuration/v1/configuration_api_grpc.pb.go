// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/enterprise/configuration/v1/configuration_api.proto

package configurationapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// ConfigurationServiceClient is the client API for ConfigurationService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ConfigurationServiceClient interface {
	// create lodging type
	CreateLodgingType(ctx context.Context, in *CreateLodgingTypeParams, opts ...grpc.CallOption) (*CreateLodgingTypeResult, error)
	// update lodging type
	UpdateLodgingType(ctx context.Context, in *UpdateLodgingTypeParams, opts ...grpc.CallOption) (*UpdateLodgingTypeResult, error)
	// list lodging types
	ListLodgingTypes(ctx context.Context, in *ListLodgingTypesParams, opts ...grpc.CallOption) (*ListLodgingTypesResult, error)
	// push lodging types
	PushLodgingTypes(ctx context.Context, in *PushLodgingTypesParams, opts ...grpc.CallOption) (*PushLodgingTypesResult, error)
	// create pet code
	CreatePetCode(ctx context.Context, in *CreatePetCodeParams, opts ...grpc.CallOption) (*CreatePetCodeResult, error)
	// update pet code
	UpdatePetCode(ctx context.Context, in *UpdatePetCodeParams, opts ...grpc.CallOption) (*UpdatePetCodeResult, error)
	// list pet codes
	ListPetCodes(ctx context.Context, in *ListPetCodesParams, opts ...grpc.CallOption) (*ListPetCodesResult, error)
	// delete pet code
	DeletePetCode(ctx context.Context, in *DeletePetCodeParams, opts ...grpc.CallOption) (*DeletePetCodeResult, error)
	// push pet codes
	PushPetCodes(ctx context.Context, in *PushPetCodesParams, opts ...grpc.CallOption) (*PushPetCodesResult, error)
	// sort pet codes
	SortPetCodes(ctx context.Context, in *SortPetCodesParams, opts ...grpc.CallOption) (*SortPetCodesResult, error)
	// create pet metadata
	CreatePetMetadata(ctx context.Context, in *CreatePetMetadataParams, opts ...grpc.CallOption) (*CreatePetMetadataResult, error)
	// update pet metadata
	UpdatePetMetadata(ctx context.Context, in *UpdatePetMetadataParams, opts ...grpc.CallOption) (*UpdatePetMetadataResult, error)
	// delete pet metadata
	DeletePetMetadata(ctx context.Context, in *DeletePetMetadataParams, opts ...grpc.CallOption) (*DeletePetMetadataResult, error)
	// list pet metadata
	ListPetMetadata(ctx context.Context, in *ListPetMetadataParams, opts ...grpc.CallOption) (*ListPetMetadataResult, error)
	// sort pet metadata
	SortPetMetadata(ctx context.Context, in *SortPetMetadataParams, opts ...grpc.CallOption) (*SortPetMetadataResult, error)
	// push pet metadata
	PushPetMetadata(ctx context.Context, in *PushPetMetadataParams, opts ...grpc.CallOption) (*PushPetMetadataResult, error)
	// ListSurchargeAssociatedFoodSource
	ListSurchargeAssociatedFoodSource(ctx context.Context, in *ListSurchargeAssociatedFoodSourceParams, opts ...grpc.CallOption) (*ListSurchargeAssociatedFoodSourceResult, error)
	// get pet settings time info
	GetPetsSettingTimeInfo(ctx context.Context, in *GetPetsSettingTimeInfoParams, opts ...grpc.CallOption) (*GetPetsSettingTimeInfoResult, error)
}

type configurationServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewConfigurationServiceClient(cc grpc.ClientConnInterface) ConfigurationServiceClient {
	return &configurationServiceClient{cc}
}

func (c *configurationServiceClient) CreateLodgingType(ctx context.Context, in *CreateLodgingTypeParams, opts ...grpc.CallOption) (*CreateLodgingTypeResult, error) {
	out := new(CreateLodgingTypeResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.configuration.v1.ConfigurationService/CreateLodgingType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *configurationServiceClient) UpdateLodgingType(ctx context.Context, in *UpdateLodgingTypeParams, opts ...grpc.CallOption) (*UpdateLodgingTypeResult, error) {
	out := new(UpdateLodgingTypeResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.configuration.v1.ConfigurationService/UpdateLodgingType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *configurationServiceClient) ListLodgingTypes(ctx context.Context, in *ListLodgingTypesParams, opts ...grpc.CallOption) (*ListLodgingTypesResult, error) {
	out := new(ListLodgingTypesResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.configuration.v1.ConfigurationService/ListLodgingTypes", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *configurationServiceClient) PushLodgingTypes(ctx context.Context, in *PushLodgingTypesParams, opts ...grpc.CallOption) (*PushLodgingTypesResult, error) {
	out := new(PushLodgingTypesResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.configuration.v1.ConfigurationService/PushLodgingTypes", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *configurationServiceClient) CreatePetCode(ctx context.Context, in *CreatePetCodeParams, opts ...grpc.CallOption) (*CreatePetCodeResult, error) {
	out := new(CreatePetCodeResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.configuration.v1.ConfigurationService/CreatePetCode", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *configurationServiceClient) UpdatePetCode(ctx context.Context, in *UpdatePetCodeParams, opts ...grpc.CallOption) (*UpdatePetCodeResult, error) {
	out := new(UpdatePetCodeResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.configuration.v1.ConfigurationService/UpdatePetCode", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *configurationServiceClient) ListPetCodes(ctx context.Context, in *ListPetCodesParams, opts ...grpc.CallOption) (*ListPetCodesResult, error) {
	out := new(ListPetCodesResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.configuration.v1.ConfigurationService/ListPetCodes", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *configurationServiceClient) DeletePetCode(ctx context.Context, in *DeletePetCodeParams, opts ...grpc.CallOption) (*DeletePetCodeResult, error) {
	out := new(DeletePetCodeResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.configuration.v1.ConfigurationService/DeletePetCode", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *configurationServiceClient) PushPetCodes(ctx context.Context, in *PushPetCodesParams, opts ...grpc.CallOption) (*PushPetCodesResult, error) {
	out := new(PushPetCodesResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.configuration.v1.ConfigurationService/PushPetCodes", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *configurationServiceClient) SortPetCodes(ctx context.Context, in *SortPetCodesParams, opts ...grpc.CallOption) (*SortPetCodesResult, error) {
	out := new(SortPetCodesResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.configuration.v1.ConfigurationService/SortPetCodes", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *configurationServiceClient) CreatePetMetadata(ctx context.Context, in *CreatePetMetadataParams, opts ...grpc.CallOption) (*CreatePetMetadataResult, error) {
	out := new(CreatePetMetadataResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.configuration.v1.ConfigurationService/CreatePetMetadata", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *configurationServiceClient) UpdatePetMetadata(ctx context.Context, in *UpdatePetMetadataParams, opts ...grpc.CallOption) (*UpdatePetMetadataResult, error) {
	out := new(UpdatePetMetadataResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.configuration.v1.ConfigurationService/UpdatePetMetadata", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *configurationServiceClient) DeletePetMetadata(ctx context.Context, in *DeletePetMetadataParams, opts ...grpc.CallOption) (*DeletePetMetadataResult, error) {
	out := new(DeletePetMetadataResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.configuration.v1.ConfigurationService/DeletePetMetadata", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *configurationServiceClient) ListPetMetadata(ctx context.Context, in *ListPetMetadataParams, opts ...grpc.CallOption) (*ListPetMetadataResult, error) {
	out := new(ListPetMetadataResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.configuration.v1.ConfigurationService/ListPetMetadata", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *configurationServiceClient) SortPetMetadata(ctx context.Context, in *SortPetMetadataParams, opts ...grpc.CallOption) (*SortPetMetadataResult, error) {
	out := new(SortPetMetadataResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.configuration.v1.ConfigurationService/SortPetMetadata", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *configurationServiceClient) PushPetMetadata(ctx context.Context, in *PushPetMetadataParams, opts ...grpc.CallOption) (*PushPetMetadataResult, error) {
	out := new(PushPetMetadataResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.configuration.v1.ConfigurationService/PushPetMetadata", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *configurationServiceClient) ListSurchargeAssociatedFoodSource(ctx context.Context, in *ListSurchargeAssociatedFoodSourceParams, opts ...grpc.CallOption) (*ListSurchargeAssociatedFoodSourceResult, error) {
	out := new(ListSurchargeAssociatedFoodSourceResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.configuration.v1.ConfigurationService/ListSurchargeAssociatedFoodSource", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *configurationServiceClient) GetPetsSettingTimeInfo(ctx context.Context, in *GetPetsSettingTimeInfoParams, opts ...grpc.CallOption) (*GetPetsSettingTimeInfoResult, error) {
	out := new(GetPetsSettingTimeInfoResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.configuration.v1.ConfigurationService/GetPetsSettingTimeInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ConfigurationServiceServer is the server API for ConfigurationService service.
// All implementations must embed UnimplementedConfigurationServiceServer
// for forward compatibility
type ConfigurationServiceServer interface {
	// create lodging type
	CreateLodgingType(context.Context, *CreateLodgingTypeParams) (*CreateLodgingTypeResult, error)
	// update lodging type
	UpdateLodgingType(context.Context, *UpdateLodgingTypeParams) (*UpdateLodgingTypeResult, error)
	// list lodging types
	ListLodgingTypes(context.Context, *ListLodgingTypesParams) (*ListLodgingTypesResult, error)
	// push lodging types
	PushLodgingTypes(context.Context, *PushLodgingTypesParams) (*PushLodgingTypesResult, error)
	// create pet code
	CreatePetCode(context.Context, *CreatePetCodeParams) (*CreatePetCodeResult, error)
	// update pet code
	UpdatePetCode(context.Context, *UpdatePetCodeParams) (*UpdatePetCodeResult, error)
	// list pet codes
	ListPetCodes(context.Context, *ListPetCodesParams) (*ListPetCodesResult, error)
	// delete pet code
	DeletePetCode(context.Context, *DeletePetCodeParams) (*DeletePetCodeResult, error)
	// push pet codes
	PushPetCodes(context.Context, *PushPetCodesParams) (*PushPetCodesResult, error)
	// sort pet codes
	SortPetCodes(context.Context, *SortPetCodesParams) (*SortPetCodesResult, error)
	// create pet metadata
	CreatePetMetadata(context.Context, *CreatePetMetadataParams) (*CreatePetMetadataResult, error)
	// update pet metadata
	UpdatePetMetadata(context.Context, *UpdatePetMetadataParams) (*UpdatePetMetadataResult, error)
	// delete pet metadata
	DeletePetMetadata(context.Context, *DeletePetMetadataParams) (*DeletePetMetadataResult, error)
	// list pet metadata
	ListPetMetadata(context.Context, *ListPetMetadataParams) (*ListPetMetadataResult, error)
	// sort pet metadata
	SortPetMetadata(context.Context, *SortPetMetadataParams) (*SortPetMetadataResult, error)
	// push pet metadata
	PushPetMetadata(context.Context, *PushPetMetadataParams) (*PushPetMetadataResult, error)
	// ListSurchargeAssociatedFoodSource
	ListSurchargeAssociatedFoodSource(context.Context, *ListSurchargeAssociatedFoodSourceParams) (*ListSurchargeAssociatedFoodSourceResult, error)
	// get pet settings time info
	GetPetsSettingTimeInfo(context.Context, *GetPetsSettingTimeInfoParams) (*GetPetsSettingTimeInfoResult, error)
	mustEmbedUnimplementedConfigurationServiceServer()
}

// UnimplementedConfigurationServiceServer must be embedded to have forward compatible implementations.
type UnimplementedConfigurationServiceServer struct {
}

func (UnimplementedConfigurationServiceServer) CreateLodgingType(context.Context, *CreateLodgingTypeParams) (*CreateLodgingTypeResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateLodgingType not implemented")
}
func (UnimplementedConfigurationServiceServer) UpdateLodgingType(context.Context, *UpdateLodgingTypeParams) (*UpdateLodgingTypeResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateLodgingType not implemented")
}
func (UnimplementedConfigurationServiceServer) ListLodgingTypes(context.Context, *ListLodgingTypesParams) (*ListLodgingTypesResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListLodgingTypes not implemented")
}
func (UnimplementedConfigurationServiceServer) PushLodgingTypes(context.Context, *PushLodgingTypesParams) (*PushLodgingTypesResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PushLodgingTypes not implemented")
}
func (UnimplementedConfigurationServiceServer) CreatePetCode(context.Context, *CreatePetCodeParams) (*CreatePetCodeResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreatePetCode not implemented")
}
func (UnimplementedConfigurationServiceServer) UpdatePetCode(context.Context, *UpdatePetCodeParams) (*UpdatePetCodeResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdatePetCode not implemented")
}
func (UnimplementedConfigurationServiceServer) ListPetCodes(context.Context, *ListPetCodesParams) (*ListPetCodesResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListPetCodes not implemented")
}
func (UnimplementedConfigurationServiceServer) DeletePetCode(context.Context, *DeletePetCodeParams) (*DeletePetCodeResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeletePetCode not implemented")
}
func (UnimplementedConfigurationServiceServer) PushPetCodes(context.Context, *PushPetCodesParams) (*PushPetCodesResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PushPetCodes not implemented")
}
func (UnimplementedConfigurationServiceServer) SortPetCodes(context.Context, *SortPetCodesParams) (*SortPetCodesResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SortPetCodes not implemented")
}
func (UnimplementedConfigurationServiceServer) CreatePetMetadata(context.Context, *CreatePetMetadataParams) (*CreatePetMetadataResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreatePetMetadata not implemented")
}
func (UnimplementedConfigurationServiceServer) UpdatePetMetadata(context.Context, *UpdatePetMetadataParams) (*UpdatePetMetadataResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdatePetMetadata not implemented")
}
func (UnimplementedConfigurationServiceServer) DeletePetMetadata(context.Context, *DeletePetMetadataParams) (*DeletePetMetadataResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeletePetMetadata not implemented")
}
func (UnimplementedConfigurationServiceServer) ListPetMetadata(context.Context, *ListPetMetadataParams) (*ListPetMetadataResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListPetMetadata not implemented")
}
func (UnimplementedConfigurationServiceServer) SortPetMetadata(context.Context, *SortPetMetadataParams) (*SortPetMetadataResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SortPetMetadata not implemented")
}
func (UnimplementedConfigurationServiceServer) PushPetMetadata(context.Context, *PushPetMetadataParams) (*PushPetMetadataResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PushPetMetadata not implemented")
}
func (UnimplementedConfigurationServiceServer) ListSurchargeAssociatedFoodSource(context.Context, *ListSurchargeAssociatedFoodSourceParams) (*ListSurchargeAssociatedFoodSourceResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListSurchargeAssociatedFoodSource not implemented")
}
func (UnimplementedConfigurationServiceServer) GetPetsSettingTimeInfo(context.Context, *GetPetsSettingTimeInfoParams) (*GetPetsSettingTimeInfoResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPetsSettingTimeInfo not implemented")
}
func (UnimplementedConfigurationServiceServer) mustEmbedUnimplementedConfigurationServiceServer() {}

// UnsafeConfigurationServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ConfigurationServiceServer will
// result in compilation errors.
type UnsafeConfigurationServiceServer interface {
	mustEmbedUnimplementedConfigurationServiceServer()
}

func RegisterConfigurationServiceServer(s grpc.ServiceRegistrar, srv ConfigurationServiceServer) {
	s.RegisterService(&ConfigurationService_ServiceDesc, srv)
}

func _ConfigurationService_CreateLodgingType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateLodgingTypeParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConfigurationServiceServer).CreateLodgingType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.configuration.v1.ConfigurationService/CreateLodgingType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConfigurationServiceServer).CreateLodgingType(ctx, req.(*CreateLodgingTypeParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConfigurationService_UpdateLodgingType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateLodgingTypeParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConfigurationServiceServer).UpdateLodgingType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.configuration.v1.ConfigurationService/UpdateLodgingType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConfigurationServiceServer).UpdateLodgingType(ctx, req.(*UpdateLodgingTypeParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConfigurationService_ListLodgingTypes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListLodgingTypesParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConfigurationServiceServer).ListLodgingTypes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.configuration.v1.ConfigurationService/ListLodgingTypes",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConfigurationServiceServer).ListLodgingTypes(ctx, req.(*ListLodgingTypesParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConfigurationService_PushLodgingTypes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PushLodgingTypesParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConfigurationServiceServer).PushLodgingTypes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.configuration.v1.ConfigurationService/PushLodgingTypes",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConfigurationServiceServer).PushLodgingTypes(ctx, req.(*PushLodgingTypesParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConfigurationService_CreatePetCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreatePetCodeParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConfigurationServiceServer).CreatePetCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.configuration.v1.ConfigurationService/CreatePetCode",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConfigurationServiceServer).CreatePetCode(ctx, req.(*CreatePetCodeParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConfigurationService_UpdatePetCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePetCodeParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConfigurationServiceServer).UpdatePetCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.configuration.v1.ConfigurationService/UpdatePetCode",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConfigurationServiceServer).UpdatePetCode(ctx, req.(*UpdatePetCodeParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConfigurationService_ListPetCodes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListPetCodesParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConfigurationServiceServer).ListPetCodes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.configuration.v1.ConfigurationService/ListPetCodes",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConfigurationServiceServer).ListPetCodes(ctx, req.(*ListPetCodesParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConfigurationService_DeletePetCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeletePetCodeParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConfigurationServiceServer).DeletePetCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.configuration.v1.ConfigurationService/DeletePetCode",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConfigurationServiceServer).DeletePetCode(ctx, req.(*DeletePetCodeParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConfigurationService_PushPetCodes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PushPetCodesParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConfigurationServiceServer).PushPetCodes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.configuration.v1.ConfigurationService/PushPetCodes",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConfigurationServiceServer).PushPetCodes(ctx, req.(*PushPetCodesParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConfigurationService_SortPetCodes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SortPetCodesParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConfigurationServiceServer).SortPetCodes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.configuration.v1.ConfigurationService/SortPetCodes",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConfigurationServiceServer).SortPetCodes(ctx, req.(*SortPetCodesParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConfigurationService_CreatePetMetadata_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreatePetMetadataParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConfigurationServiceServer).CreatePetMetadata(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.configuration.v1.ConfigurationService/CreatePetMetadata",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConfigurationServiceServer).CreatePetMetadata(ctx, req.(*CreatePetMetadataParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConfigurationService_UpdatePetMetadata_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePetMetadataParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConfigurationServiceServer).UpdatePetMetadata(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.configuration.v1.ConfigurationService/UpdatePetMetadata",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConfigurationServiceServer).UpdatePetMetadata(ctx, req.(*UpdatePetMetadataParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConfigurationService_DeletePetMetadata_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeletePetMetadataParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConfigurationServiceServer).DeletePetMetadata(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.configuration.v1.ConfigurationService/DeletePetMetadata",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConfigurationServiceServer).DeletePetMetadata(ctx, req.(*DeletePetMetadataParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConfigurationService_ListPetMetadata_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListPetMetadataParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConfigurationServiceServer).ListPetMetadata(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.configuration.v1.ConfigurationService/ListPetMetadata",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConfigurationServiceServer).ListPetMetadata(ctx, req.(*ListPetMetadataParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConfigurationService_SortPetMetadata_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SortPetMetadataParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConfigurationServiceServer).SortPetMetadata(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.configuration.v1.ConfigurationService/SortPetMetadata",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConfigurationServiceServer).SortPetMetadata(ctx, req.(*SortPetMetadataParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConfigurationService_PushPetMetadata_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PushPetMetadataParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConfigurationServiceServer).PushPetMetadata(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.configuration.v1.ConfigurationService/PushPetMetadata",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConfigurationServiceServer).PushPetMetadata(ctx, req.(*PushPetMetadataParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConfigurationService_ListSurchargeAssociatedFoodSource_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListSurchargeAssociatedFoodSourceParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConfigurationServiceServer).ListSurchargeAssociatedFoodSource(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.configuration.v1.ConfigurationService/ListSurchargeAssociatedFoodSource",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConfigurationServiceServer).ListSurchargeAssociatedFoodSource(ctx, req.(*ListSurchargeAssociatedFoodSourceParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConfigurationService_GetPetsSettingTimeInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPetsSettingTimeInfoParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConfigurationServiceServer).GetPetsSettingTimeInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.configuration.v1.ConfigurationService/GetPetsSettingTimeInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConfigurationServiceServer).GetPetsSettingTimeInfo(ctx, req.(*GetPetsSettingTimeInfoParams))
	}
	return interceptor(ctx, in, info, handler)
}

// ConfigurationService_ServiceDesc is the grpc.ServiceDesc for ConfigurationService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ConfigurationService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.enterprise.configuration.v1.ConfigurationService",
	HandlerType: (*ConfigurationServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateLodgingType",
			Handler:    _ConfigurationService_CreateLodgingType_Handler,
		},
		{
			MethodName: "UpdateLodgingType",
			Handler:    _ConfigurationService_UpdateLodgingType_Handler,
		},
		{
			MethodName: "ListLodgingTypes",
			Handler:    _ConfigurationService_ListLodgingTypes_Handler,
		},
		{
			MethodName: "PushLodgingTypes",
			Handler:    _ConfigurationService_PushLodgingTypes_Handler,
		},
		{
			MethodName: "CreatePetCode",
			Handler:    _ConfigurationService_CreatePetCode_Handler,
		},
		{
			MethodName: "UpdatePetCode",
			Handler:    _ConfigurationService_UpdatePetCode_Handler,
		},
		{
			MethodName: "ListPetCodes",
			Handler:    _ConfigurationService_ListPetCodes_Handler,
		},
		{
			MethodName: "DeletePetCode",
			Handler:    _ConfigurationService_DeletePetCode_Handler,
		},
		{
			MethodName: "PushPetCodes",
			Handler:    _ConfigurationService_PushPetCodes_Handler,
		},
		{
			MethodName: "SortPetCodes",
			Handler:    _ConfigurationService_SortPetCodes_Handler,
		},
		{
			MethodName: "CreatePetMetadata",
			Handler:    _ConfigurationService_CreatePetMetadata_Handler,
		},
		{
			MethodName: "UpdatePetMetadata",
			Handler:    _ConfigurationService_UpdatePetMetadata_Handler,
		},
		{
			MethodName: "DeletePetMetadata",
			Handler:    _ConfigurationService_DeletePetMetadata_Handler,
		},
		{
			MethodName: "ListPetMetadata",
			Handler:    _ConfigurationService_ListPetMetadata_Handler,
		},
		{
			MethodName: "SortPetMetadata",
			Handler:    _ConfigurationService_SortPetMetadata_Handler,
		},
		{
			MethodName: "PushPetMetadata",
			Handler:    _ConfigurationService_PushPetMetadata_Handler,
		},
		{
			MethodName: "ListSurchargeAssociatedFoodSource",
			Handler:    _ConfigurationService_ListSurchargeAssociatedFoodSource_Handler,
		},
		{
			MethodName: "GetPetsSettingTimeInfo",
			Handler:    _ConfigurationService_GetPetsSettingTimeInfo_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/enterprise/configuration/v1/configuration_api.proto",
}
