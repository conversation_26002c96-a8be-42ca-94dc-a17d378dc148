// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/enterprise/configuration/v1/service_settings.proto

package configurationapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/enterprise/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// create lodging type request
type CreateLodgingTypeParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// lodging type
	LodgingTypeDef *v1.CreateLodgingTypeDef `protobuf:"bytes,1,opt,name=lodging_type_def,json=lodgingTypeDef,proto3" json:"lodging_type_def,omitempty"`
}

func (x *CreateLodgingTypeParams) Reset() {
	*x = CreateLodgingTypeParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_configuration_v1_service_settings_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateLodgingTypeParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateLodgingTypeParams) ProtoMessage() {}

func (x *CreateLodgingTypeParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_configuration_v1_service_settings_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateLodgingTypeParams.ProtoReflect.Descriptor instead.
func (*CreateLodgingTypeParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_configuration_v1_service_settings_proto_rawDescGZIP(), []int{0}
}

func (x *CreateLodgingTypeParams) GetLodgingTypeDef() *v1.CreateLodgingTypeDef {
	if x != nil {
		return x.LodgingTypeDef
	}
	return nil
}

// create lodging type response
type CreateLodgingTypeResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// lodging type
	LodgingType *v1.LodgingType `protobuf:"bytes,1,opt,name=lodging_type,json=lodgingType,proto3" json:"lodging_type,omitempty"`
}

func (x *CreateLodgingTypeResult) Reset() {
	*x = CreateLodgingTypeResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_configuration_v1_service_settings_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateLodgingTypeResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateLodgingTypeResult) ProtoMessage() {}

func (x *CreateLodgingTypeResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_configuration_v1_service_settings_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateLodgingTypeResult.ProtoReflect.Descriptor instead.
func (*CreateLodgingTypeResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_configuration_v1_service_settings_proto_rawDescGZIP(), []int{1}
}

func (x *CreateLodgingTypeResult) GetLodgingType() *v1.LodgingType {
	if x != nil {
		return x.LodgingType
	}
	return nil
}

// update lodging type request
type UpdateLodgingTypeParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id of the lodging type
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// update lodging type
	LodgingTypeDef *v1.UpdateLodgingTypeDef `protobuf:"bytes,2,opt,name=lodging_type_def,json=lodgingTypeDef,proto3" json:"lodging_type_def,omitempty"`
}

func (x *UpdateLodgingTypeParams) Reset() {
	*x = UpdateLodgingTypeParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_configuration_v1_service_settings_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateLodgingTypeParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateLodgingTypeParams) ProtoMessage() {}

func (x *UpdateLodgingTypeParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_configuration_v1_service_settings_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateLodgingTypeParams.ProtoReflect.Descriptor instead.
func (*UpdateLodgingTypeParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_configuration_v1_service_settings_proto_rawDescGZIP(), []int{2}
}

func (x *UpdateLodgingTypeParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateLodgingTypeParams) GetLodgingTypeDef() *v1.UpdateLodgingTypeDef {
	if x != nil {
		return x.LodgingTypeDef
	}
	return nil
}

// update lodging type response
type UpdateLodgingTypeResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// lodging type
	LodgingType *v1.LodgingType `protobuf:"bytes,1,opt,name=lodging_type,json=lodgingType,proto3" json:"lodging_type,omitempty"`
}

func (x *UpdateLodgingTypeResult) Reset() {
	*x = UpdateLodgingTypeResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_configuration_v1_service_settings_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateLodgingTypeResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateLodgingTypeResult) ProtoMessage() {}

func (x *UpdateLodgingTypeResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_configuration_v1_service_settings_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateLodgingTypeResult.ProtoReflect.Descriptor instead.
func (*UpdateLodgingTypeResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_configuration_v1_service_settings_proto_rawDescGZIP(), []int{3}
}

func (x *UpdateLodgingTypeResult) GetLodgingType() *v1.LodgingType {
	if x != nil {
		return x.LodgingType
	}
	return nil
}

// list lodging types request
type ListLodgingTypesParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// filter
	Filter *ListLodgingTypesParams_Filter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
}

func (x *ListLodgingTypesParams) Reset() {
	*x = ListLodgingTypesParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_configuration_v1_service_settings_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListLodgingTypesParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListLodgingTypesParams) ProtoMessage() {}

func (x *ListLodgingTypesParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_configuration_v1_service_settings_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListLodgingTypesParams.ProtoReflect.Descriptor instead.
func (*ListLodgingTypesParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_configuration_v1_service_settings_proto_rawDescGZIP(), []int{4}
}

func (x *ListLodgingTypesParams) GetFilter() *ListLodgingTypesParams_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

// list lodging types response
type ListLodgingTypesResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// lodging types
	LodgingTypes []*v1.LodgingType `protobuf:"bytes,1,rep,name=lodging_types,json=lodgingTypes,proto3" json:"lodging_types,omitempty"`
}

func (x *ListLodgingTypesResult) Reset() {
	*x = ListLodgingTypesResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_configuration_v1_service_settings_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListLodgingTypesResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListLodgingTypesResult) ProtoMessage() {}

func (x *ListLodgingTypesResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_configuration_v1_service_settings_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListLodgingTypesResult.ProtoReflect.Descriptor instead.
func (*ListLodgingTypesResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_configuration_v1_service_settings_proto_rawDescGZIP(), []int{5}
}

func (x *ListLodgingTypesResult) GetLodgingTypes() []*v1.LodgingType {
	if x != nil {
		return x.LodgingTypes
	}
	return nil
}

// PushLodgingTypesParams
type PushLodgingTypesParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// lodging ids
	Ids []int64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	// targets
	Targets []*v1.TenantObject `protobuf:"bytes,2,rep,name=targets,proto3" json:"targets,omitempty"`
}

func (x *PushLodgingTypesParams) Reset() {
	*x = PushLodgingTypesParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_configuration_v1_service_settings_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PushLodgingTypesParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushLodgingTypesParams) ProtoMessage() {}

func (x *PushLodgingTypesParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_configuration_v1_service_settings_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushLodgingTypesParams.ProtoReflect.Descriptor instead.
func (*PushLodgingTypesParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_configuration_v1_service_settings_proto_rawDescGZIP(), []int{6}
}

func (x *PushLodgingTypesParams) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *PushLodgingTypesParams) GetTargets() []*v1.TenantObject {
	if x != nil {
		return x.Targets
	}
	return nil
}

// PushLodgingsResult
type PushLodgingTypesResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// success company ids
	SuccessCompanyIds []int64 `protobuf:"varint,1,rep,packed,name=success_company_ids,json=successCompanyIds,proto3" json:"success_company_ids,omitempty"`
	// failed company ids
	FailedCompanyIds []int64 `protobuf:"varint,2,rep,packed,name=failed_company_ids,json=failedCompanyIds,proto3" json:"failed_company_ids,omitempty"`
}

func (x *PushLodgingTypesResult) Reset() {
	*x = PushLodgingTypesResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_configuration_v1_service_settings_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PushLodgingTypesResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushLodgingTypesResult) ProtoMessage() {}

func (x *PushLodgingTypesResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_configuration_v1_service_settings_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushLodgingTypesResult.ProtoReflect.Descriptor instead.
func (*PushLodgingTypesResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_configuration_v1_service_settings_proto_rawDescGZIP(), []int{7}
}

func (x *PushLodgingTypesResult) GetSuccessCompanyIds() []int64 {
	if x != nil {
		return x.SuccessCompanyIds
	}
	return nil
}

func (x *PushLodgingTypesResult) GetFailedCompanyIds() []int64 {
	if x != nil {
		return x.FailedCompanyIds
	}
	return nil
}

// filter
type ListLodgingTypesParams_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// lodging type ids
	Ids []int64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
}

func (x *ListLodgingTypesParams_Filter) Reset() {
	*x = ListLodgingTypesParams_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_configuration_v1_service_settings_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListLodgingTypesParams_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListLodgingTypesParams_Filter) ProtoMessage() {}

func (x *ListLodgingTypesParams_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_configuration_v1_service_settings_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListLodgingTypesParams_Filter.ProtoReflect.Descriptor instead.
func (*ListLodgingTypesParams_Filter) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_configuration_v1_service_settings_proto_rawDescGZIP(), []int{4, 0}
}

func (x *ListLodgingTypesParams_Filter) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

var File_moego_enterprise_configuration_v1_service_settings_proto protoreflect.FileDescriptor

var file_moego_enterprise_configuration_v1_service_settings_proto_rawDesc = []byte{
	0x0a, 0x38, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x73, 0x65, 0x74, 0x74,
	0x69, 0x6e, 0x67, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x21, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x1a, 0x32, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f,
	0x62, 0x6f, 0x6f, 0x6b, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x65,
	0x6e, 0x61, 0x6e, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x7f, 0x0a, 0x17, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x64, 0x0a, 0x10, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x64, 0x65, 0x66, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x44, 0x65,
	0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0e, 0x6c, 0x6f, 0x64,
	0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x44, 0x65, 0x66, 0x22, 0x65, 0x0a, 0x17, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x4a, 0x0a, 0x0c, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e,
	0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e,
	0x67, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79,
	0x70, 0x65, 0x22, 0x98, 0x01, 0x0a, 0x17, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4c, 0x6f, 0x64,
	0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x17,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x64, 0x0a, 0x10, 0x6c, 0x6f, 0x64, 0x67, 0x69,
	0x6e, 0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x64, 0x65, 0x66, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65,
	0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0e, 0x6c,
	0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x44, 0x65, 0x66, 0x22, 0x65, 0x0a,
	0x17, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x4a, 0x0a, 0x0c, 0x6c, 0x6f, 0x64, 0x67,
	0x69, 0x6e, 0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x64, 0x67,
	0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67,
	0x54, 0x79, 0x70, 0x65, 0x22, 0x9f, 0x01, 0x0a, 0x16, 0x4c, 0x69, 0x73, 0x74, 0x4c, 0x6f, 0x64,
	0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12,
	0x58, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x40, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54,
	0x79, 0x70, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x1a, 0x2b, 0x0a, 0x06, 0x46, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x12, 0x21, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03,
	0x42, 0x0f, 0xfa, 0x42, 0x0c, 0x92, 0x01, 0x09, 0x10, 0xe8, 0x07, 0x22, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x03, 0x69, 0x64, 0x73, 0x22, 0x66, 0x0a, 0x16, 0x4c, 0x69, 0x73, 0x74, 0x4c, 0x6f,
	0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x4c, 0x0a, 0x0d, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x0c, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x73, 0x22, 0x6e,
	0x0a, 0x16, 0x50, 0x75, 0x73, 0x68, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70,
	0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x03, 0x69, 0x64, 0x73, 0x12, 0x42, 0x0a, 0x07, 0x74, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x4f,
	0x62, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x07, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x73, 0x22, 0x76,
	0x0a, 0x16, 0x50, 0x75, 0x73, 0x68, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70,
	0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x2e, 0x0a, 0x13, 0x73, 0x75, 0x63, 0x63,
	0x65, 0x73, 0x73, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x11, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x43, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x73, 0x12, 0x2c, 0x0a, 0x12, 0x66, 0x61, 0x69, 0x6c,
	0x65, 0x64, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x03, 0x52, 0x10, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x43, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x49, 0x64, 0x73, 0x42, 0x98, 0x01, 0x0a, 0x29, 0x63, 0x6f, 0x6d, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x69, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x3b, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x70, 0x69, 0x70,
	0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_enterprise_configuration_v1_service_settings_proto_rawDescOnce sync.Once
	file_moego_enterprise_configuration_v1_service_settings_proto_rawDescData = file_moego_enterprise_configuration_v1_service_settings_proto_rawDesc
)

func file_moego_enterprise_configuration_v1_service_settings_proto_rawDescGZIP() []byte {
	file_moego_enterprise_configuration_v1_service_settings_proto_rawDescOnce.Do(func() {
		file_moego_enterprise_configuration_v1_service_settings_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_enterprise_configuration_v1_service_settings_proto_rawDescData)
	})
	return file_moego_enterprise_configuration_v1_service_settings_proto_rawDescData
}

var file_moego_enterprise_configuration_v1_service_settings_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_moego_enterprise_configuration_v1_service_settings_proto_goTypes = []interface{}{
	(*CreateLodgingTypeParams)(nil),       // 0: moego.enterprise.configuration.v1.CreateLodgingTypeParams
	(*CreateLodgingTypeResult)(nil),       // 1: moego.enterprise.configuration.v1.CreateLodgingTypeResult
	(*UpdateLodgingTypeParams)(nil),       // 2: moego.enterprise.configuration.v1.UpdateLodgingTypeParams
	(*UpdateLodgingTypeResult)(nil),       // 3: moego.enterprise.configuration.v1.UpdateLodgingTypeResult
	(*ListLodgingTypesParams)(nil),        // 4: moego.enterprise.configuration.v1.ListLodgingTypesParams
	(*ListLodgingTypesResult)(nil),        // 5: moego.enterprise.configuration.v1.ListLodgingTypesResult
	(*PushLodgingTypesParams)(nil),        // 6: moego.enterprise.configuration.v1.PushLodgingTypesParams
	(*PushLodgingTypesResult)(nil),        // 7: moego.enterprise.configuration.v1.PushLodgingTypesResult
	(*ListLodgingTypesParams_Filter)(nil), // 8: moego.enterprise.configuration.v1.ListLodgingTypesParams.Filter
	(*v1.CreateLodgingTypeDef)(nil),       // 9: moego.models.enterprise.v1.CreateLodgingTypeDef
	(*v1.LodgingType)(nil),                // 10: moego.models.enterprise.v1.LodgingType
	(*v1.UpdateLodgingTypeDef)(nil),       // 11: moego.models.enterprise.v1.UpdateLodgingTypeDef
	(*v1.TenantObject)(nil),               // 12: moego.models.enterprise.v1.TenantObject
}
var file_moego_enterprise_configuration_v1_service_settings_proto_depIdxs = []int32{
	9,  // 0: moego.enterprise.configuration.v1.CreateLodgingTypeParams.lodging_type_def:type_name -> moego.models.enterprise.v1.CreateLodgingTypeDef
	10, // 1: moego.enterprise.configuration.v1.CreateLodgingTypeResult.lodging_type:type_name -> moego.models.enterprise.v1.LodgingType
	11, // 2: moego.enterprise.configuration.v1.UpdateLodgingTypeParams.lodging_type_def:type_name -> moego.models.enterprise.v1.UpdateLodgingTypeDef
	10, // 3: moego.enterprise.configuration.v1.UpdateLodgingTypeResult.lodging_type:type_name -> moego.models.enterprise.v1.LodgingType
	8,  // 4: moego.enterprise.configuration.v1.ListLodgingTypesParams.filter:type_name -> moego.enterprise.configuration.v1.ListLodgingTypesParams.Filter
	10, // 5: moego.enterprise.configuration.v1.ListLodgingTypesResult.lodging_types:type_name -> moego.models.enterprise.v1.LodgingType
	12, // 6: moego.enterprise.configuration.v1.PushLodgingTypesParams.targets:type_name -> moego.models.enterprise.v1.TenantObject
	7,  // [7:7] is the sub-list for method output_type
	7,  // [7:7] is the sub-list for method input_type
	7,  // [7:7] is the sub-list for extension type_name
	7,  // [7:7] is the sub-list for extension extendee
	0,  // [0:7] is the sub-list for field type_name
}

func init() { file_moego_enterprise_configuration_v1_service_settings_proto_init() }
func file_moego_enterprise_configuration_v1_service_settings_proto_init() {
	if File_moego_enterprise_configuration_v1_service_settings_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_enterprise_configuration_v1_service_settings_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateLodgingTypeParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_configuration_v1_service_settings_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateLodgingTypeResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_configuration_v1_service_settings_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateLodgingTypeParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_configuration_v1_service_settings_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateLodgingTypeResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_configuration_v1_service_settings_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListLodgingTypesParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_configuration_v1_service_settings_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListLodgingTypesResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_configuration_v1_service_settings_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PushLodgingTypesParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_configuration_v1_service_settings_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PushLodgingTypesResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_configuration_v1_service_settings_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListLodgingTypesParams_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_enterprise_configuration_v1_service_settings_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_enterprise_configuration_v1_service_settings_proto_goTypes,
		DependencyIndexes: file_moego_enterprise_configuration_v1_service_settings_proto_depIdxs,
		MessageInfos:      file_moego_enterprise_configuration_v1_service_settings_proto_msgTypes,
	}.Build()
	File_moego_enterprise_configuration_v1_service_settings_proto = out.File
	file_moego_enterprise_configuration_v1_service_settings_proto_rawDesc = nil
	file_moego_enterprise_configuration_v1_service_settings_proto_goTypes = nil
	file_moego_enterprise_configuration_v1_service_settings_proto_depIdxs = nil
}
