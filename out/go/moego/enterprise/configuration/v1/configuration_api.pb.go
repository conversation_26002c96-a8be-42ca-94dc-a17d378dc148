// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/enterprise/configuration/v1/configuration_api.proto

package configurationapipb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// setting type
type GetPetsSettingTimeInfoResult_SettingType int32

const (
	// Unspecified setting type
	GetPetsSettingTimeInfoResult_SETTING_TYPE_UNSPECIFIED GetPetsSettingTimeInfoResult_SettingType = 0
	// pet code
	GetPetsSettingTimeInfoResult_PET_CODE GetPetsSettingTimeInfoResult_SettingType = 1
	// pet feeding
	GetPetsSettingTimeInfoResult_PET_FEEDING GetPetsSettingTimeInfoResult_SettingType = 2
)

// Enum value maps for GetPetsSettingTimeInfoResult_SettingType.
var (
	GetPetsSettingTimeInfoResult_SettingType_name = map[int32]string{
		0: "SETTING_TYPE_UNSPECIFIED",
		1: "PET_CODE",
		2: "PET_FEEDING",
	}
	GetPetsSettingTimeInfoResult_SettingType_value = map[string]int32{
		"SETTING_TYPE_UNSPECIFIED": 0,
		"PET_CODE":                 1,
		"PET_FEEDING":              2,
	}
)

func (x GetPetsSettingTimeInfoResult_SettingType) Enum() *GetPetsSettingTimeInfoResult_SettingType {
	p := new(GetPetsSettingTimeInfoResult_SettingType)
	*p = x
	return p
}

func (x GetPetsSettingTimeInfoResult_SettingType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetPetsSettingTimeInfoResult_SettingType) Descriptor() protoreflect.EnumDescriptor {
	return file_moego_enterprise_configuration_v1_configuration_api_proto_enumTypes[0].Descriptor()
}

func (GetPetsSettingTimeInfoResult_SettingType) Type() protoreflect.EnumType {
	return &file_moego_enterprise_configuration_v1_configuration_api_proto_enumTypes[0]
}

func (x GetPetsSettingTimeInfoResult_SettingType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetPetsSettingTimeInfoResult_SettingType.Descriptor instead.
func (GetPetsSettingTimeInfoResult_SettingType) EnumDescriptor() ([]byte, []int) {
	return file_moego_enterprise_configuration_v1_configuration_api_proto_rawDescGZIP(), []int{3, 0}
}

// ListSurchargeAssociatedFoodSource
type ListSurchargeAssociatedFoodSourceParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ListSurchargeAssociatedFoodSourceParams) Reset() {
	*x = ListSurchargeAssociatedFoodSourceParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_configuration_v1_configuration_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListSurchargeAssociatedFoodSourceParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSurchargeAssociatedFoodSourceParams) ProtoMessage() {}

func (x *ListSurchargeAssociatedFoodSourceParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_configuration_v1_configuration_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSurchargeAssociatedFoodSourceParams.ProtoReflect.Descriptor instead.
func (*ListSurchargeAssociatedFoodSourceParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_configuration_v1_configuration_api_proto_rawDescGZIP(), []int{0}
}

// ListSurchargeAssociatedFoodSourceResponse
type ListSurchargeAssociatedFoodSourceResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// food source ids
	FoodSourceIds []int64 `protobuf:"varint,1,rep,packed,name=food_source_ids,json=foodSourceIds,proto3" json:"food_source_ids,omitempty"`
}

func (x *ListSurchargeAssociatedFoodSourceResult) Reset() {
	*x = ListSurchargeAssociatedFoodSourceResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_configuration_v1_configuration_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListSurchargeAssociatedFoodSourceResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSurchargeAssociatedFoodSourceResult) ProtoMessage() {}

func (x *ListSurchargeAssociatedFoodSourceResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_configuration_v1_configuration_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSurchargeAssociatedFoodSourceResult.ProtoReflect.Descriptor instead.
func (*ListSurchargeAssociatedFoodSourceResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_configuration_v1_configuration_api_proto_rawDescGZIP(), []int{1}
}

func (x *ListSurchargeAssociatedFoodSourceResult) GetFoodSourceIds() []int64 {
	if x != nil {
		return x.FoodSourceIds
	}
	return nil
}

// get pet settings time info request
type GetPetsSettingTimeInfoParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetPetsSettingTimeInfoParams) Reset() {
	*x = GetPetsSettingTimeInfoParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_configuration_v1_configuration_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPetsSettingTimeInfoParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPetsSettingTimeInfoParams) ProtoMessage() {}

func (x *GetPetsSettingTimeInfoParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_configuration_v1_configuration_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPetsSettingTimeInfoParams.ProtoReflect.Descriptor instead.
func (*GetPetsSettingTimeInfoParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_configuration_v1_configuration_api_proto_rawDescGZIP(), []int{2}
}

// get pet settings time info response
type GetPetsSettingTimeInfoResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// time infos
	TimeInfos []*GetPetsSettingTimeInfoResult_TimeInfo `protobuf:"bytes,1,rep,name=time_infos,json=timeInfos,proto3" json:"time_infos,omitempty"`
}

func (x *GetPetsSettingTimeInfoResult) Reset() {
	*x = GetPetsSettingTimeInfoResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_configuration_v1_configuration_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPetsSettingTimeInfoResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPetsSettingTimeInfoResult) ProtoMessage() {}

func (x *GetPetsSettingTimeInfoResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_configuration_v1_configuration_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPetsSettingTimeInfoResult.ProtoReflect.Descriptor instead.
func (*GetPetsSettingTimeInfoResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_configuration_v1_configuration_api_proto_rawDescGZIP(), []int{3}
}

func (x *GetPetsSettingTimeInfoResult) GetTimeInfos() []*GetPetsSettingTimeInfoResult_TimeInfo {
	if x != nil {
		return x.TimeInfos
	}
	return nil
}

// time info
type GetPetsSettingTimeInfoResult_TimeInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// setting type
	Type GetPetsSettingTimeInfoResult_SettingType `protobuf:"varint,1,opt,name=type,proto3,enum=moego.enterprise.configuration.v1.GetPetsSettingTimeInfoResult_SettingType" json:"type,omitempty"`
	// last update time
	LastUpdatedAt *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=last_updated_at,json=lastUpdatedAt,proto3" json:"last_updated_at,omitempty"`
	// last push time
	LastPushedAt *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=last_pushed_at,json=lastPushedAt,proto3" json:"last_pushed_at,omitempty"`
}

func (x *GetPetsSettingTimeInfoResult_TimeInfo) Reset() {
	*x = GetPetsSettingTimeInfoResult_TimeInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_configuration_v1_configuration_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPetsSettingTimeInfoResult_TimeInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPetsSettingTimeInfoResult_TimeInfo) ProtoMessage() {}

func (x *GetPetsSettingTimeInfoResult_TimeInfo) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_configuration_v1_configuration_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPetsSettingTimeInfoResult_TimeInfo.ProtoReflect.Descriptor instead.
func (*GetPetsSettingTimeInfoResult_TimeInfo) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_configuration_v1_configuration_api_proto_rawDescGZIP(), []int{3, 0}
}

func (x *GetPetsSettingTimeInfoResult_TimeInfo) GetType() GetPetsSettingTimeInfoResult_SettingType {
	if x != nil {
		return x.Type
	}
	return GetPetsSettingTimeInfoResult_SETTING_TYPE_UNSPECIFIED
}

func (x *GetPetsSettingTimeInfoResult_TimeInfo) GetLastUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.LastUpdatedAt
	}
	return nil
}

func (x *GetPetsSettingTimeInfoResult_TimeInfo) GetLastPushedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.LastPushedAt
	}
	return nil
}

var File_moego_enterprise_configuration_v1_configuration_api_proto protoreflect.FileDescriptor

var file_moego_enterprise_configuration_v1_configuration_api_proto_rawDesc = []byte{
	0x0a, 0x39, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2f, 0x76, 0x31, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x21, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x1a, 0x1f,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x34, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73,
	0x65, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f,
	0x76, 0x31, 0x2f, 0x70, 0x65, 0x74, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x38, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0x29, 0x0a, 0x27, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x75, 0x72, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65,
	0x41, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x65, 0x64, 0x46, 0x6f, 0x6f, 0x64, 0x53, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x22, 0x51, 0x0a, 0x27, 0x4c, 0x69,
	0x73, 0x74, 0x53, 0x75, 0x72, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x41, 0x73, 0x73, 0x6f, 0x63,
	0x69, 0x61, 0x74, 0x65, 0x64, 0x46, 0x6f, 0x6f, 0x64, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x26, 0x0a, 0x0f, 0x66, 0x6f, 0x6f, 0x64, 0x5f, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0d,
	0x66, 0x6f, 0x6f, 0x64, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x73, 0x22, 0x1e, 0x0a,
	0x1c, 0x47, 0x65, 0x74, 0x50, 0x65, 0x74, 0x73, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x54,
	0x69, 0x6d, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x22, 0xc7, 0x03,
	0x0a, 0x1c, 0x47, 0x65, 0x74, 0x50, 0x65, 0x74, 0x73, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67,
	0x54, 0x69, 0x6d, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x67,
	0x0a, 0x0a, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x48, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x65, 0x74, 0x73, 0x53, 0x65,
	0x74, 0x74, 0x69, 0x6e, 0x67, 0x54, 0x69, 0x6d, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x74, 0x69,
	0x6d, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x1a, 0xf1, 0x01, 0x0a, 0x08, 0x54, 0x69, 0x6d, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x5f, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x4b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x65, 0x74, 0x73, 0x53, 0x65,
	0x74, 0x74, 0x69, 0x6e, 0x67, 0x54, 0x69, 0x6d, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x2e, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x42, 0x0a, 0x0f, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0d, 0x6c, 0x61, 0x73, 0x74,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x40, 0x0a, 0x0e, 0x6c, 0x61, 0x73,
	0x74, 0x5f, 0x70, 0x75, 0x73, 0x68, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0c, 0x6c,
	0x61, 0x73, 0x74, 0x50, 0x75, 0x73, 0x68, 0x65, 0x64, 0x41, 0x74, 0x22, 0x4a, 0x0a, 0x0b, 0x53,
	0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x18, 0x53, 0x45,
	0x54, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x50, 0x45, 0x54, 0x5f,
	0x43, 0x4f, 0x44, 0x45, 0x10, 0x01, 0x12, 0x0f, 0x0a, 0x0b, 0x50, 0x45, 0x54, 0x5f, 0x46, 0x45,
	0x45, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x02, 0x32, 0x89, 0x14, 0x0a, 0x14, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x12, 0x8d, 0x01, 0x0a, 0x11, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4c, 0x6f, 0x64, 0x67, 0x69,
	0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x1a, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4c, 0x6f, 0x64,
	0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x00,
	0x12, 0x8d, 0x01, 0x0a, 0x11, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4c, 0x6f, 0x64, 0x67, 0x69,
	0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x1a, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4c, 0x6f, 0x64,
	0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x00,
	0x12, 0x8a, 0x01, 0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67,
	0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4c, 0x6f,
	0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x1a, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67,
	0x54, 0x79, 0x70, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x00, 0x12, 0x8a, 0x01,
	0x0a, 0x10, 0x50, 0x75, 0x73, 0x68, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70,
	0x65, 0x73, 0x12, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x75, 0x73, 0x68, 0x4c, 0x6f, 0x64, 0x67, 0x69,
	0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x39, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x50, 0x75, 0x73, 0x68, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70,
	0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x00, 0x12, 0x81, 0x01, 0x0a, 0x0d, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x36, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x1a, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50,
	0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x00, 0x12, 0x81,
	0x01, 0x0a, 0x0d, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65,
	0x12, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x43, 0x6f,
	0x64, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x22, 0x00, 0x12, 0x7e, 0x0a, 0x0c, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x64,
	0x65, 0x73, 0x12, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x43, 0x6f,
	0x64, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x22, 0x00, 0x12, 0x81, 0x01, 0x0a, 0x0d, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x65, 0x74,
	0x43, 0x6f, 0x64, 0x65, 0x12, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50,
	0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x36, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x22, 0x00, 0x12, 0x7e, 0x0a, 0x0c, 0x50, 0x75, 0x73, 0x68, 0x50, 0x65,
	0x74, 0x43, 0x6f, 0x64, 0x65, 0x73, 0x12, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x75, 0x73, 0x68, 0x50,
	0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x35, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x50, 0x75, 0x73, 0x68, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x73, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x22, 0x00, 0x12, 0x7e, 0x0a, 0x0c, 0x53, 0x6f, 0x72, 0x74, 0x50, 0x65,
	0x74, 0x43, 0x6f, 0x64, 0x65, 0x73, 0x12, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f, 0x72, 0x74, 0x50,
	0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x35, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x6f, 0x72, 0x74, 0x50, 0x65, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x73, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x22, 0x00, 0x12, 0x8d, 0x01, 0x0a, 0x11, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x50, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x3a, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61,
	0x74, 0x61, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x22, 0x00, 0x12, 0x8d, 0x01, 0x0a, 0x11, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x50, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x3a, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61,
	0x74, 0x61, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x50, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x22, 0x00, 0x12, 0x8d, 0x01, 0x0a, 0x11, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x50, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x3a, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61,
	0x74, 0x61, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x50, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x22, 0x00, 0x12, 0x87, 0x01, 0x0a, 0x0f, 0x4c, 0x69, 0x73, 0x74, 0x50,
	0x65, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x38, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x1a, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74,
	0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x00,
	0x12, 0x87, 0x01, 0x0a, 0x0f, 0x53, 0x6f, 0x72, 0x74, 0x50, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0x12, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f, 0x72, 0x74, 0x50, 0x65, 0x74,
	0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x38,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73,
	0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x6f, 0x72, 0x74, 0x50, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61,
	0x74, 0x61, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x00, 0x12, 0x87, 0x01, 0x0a, 0x0f, 0x50,
	0x75, 0x73, 0x68, 0x50, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x38,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73,
	0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x50, 0x75, 0x73, 0x68, 0x50, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61,
	0x74, 0x61, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x75, 0x73,
	0x68, 0x50, 0x65, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x22, 0x00, 0x12, 0xbd, 0x01, 0x0a, 0x21, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x75, 0x72,
	0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x41, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x65, 0x64,
	0x46, 0x6f, 0x6f, 0x64, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x4a, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x53, 0x75, 0x72, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x41, 0x73, 0x73, 0x6f,
	0x63, 0x69, 0x61, 0x74, 0x65, 0x64, 0x46, 0x6f, 0x6f, 0x64, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x4a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53,
	0x75, 0x72, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x41, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74,
	0x65, 0x64, 0x46, 0x6f, 0x6f, 0x64, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x22, 0x00, 0x12, 0x9c, 0x01, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x50, 0x65, 0x74, 0x73,
	0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x54, 0x69, 0x6d, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x3f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x65, 0x74, 0x73, 0x53, 0x65, 0x74, 0x74, 0x69,
	0x6e, 0x67, 0x54, 0x69, 0x6d, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x1a, 0x3f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x65, 0x74, 0x73, 0x53, 0x65, 0x74, 0x74,
	0x69, 0x6e, 0x67, 0x54, 0x69, 0x6d, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x22, 0x00, 0x42, 0x98, 0x01, 0x0a, 0x29, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76,
	0x31, 0x50, 0x01, 0x5a, 0x69, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x3b, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_enterprise_configuration_v1_configuration_api_proto_rawDescOnce sync.Once
	file_moego_enterprise_configuration_v1_configuration_api_proto_rawDescData = file_moego_enterprise_configuration_v1_configuration_api_proto_rawDesc
)

func file_moego_enterprise_configuration_v1_configuration_api_proto_rawDescGZIP() []byte {
	file_moego_enterprise_configuration_v1_configuration_api_proto_rawDescOnce.Do(func() {
		file_moego_enterprise_configuration_v1_configuration_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_enterprise_configuration_v1_configuration_api_proto_rawDescData)
	})
	return file_moego_enterprise_configuration_v1_configuration_api_proto_rawDescData
}

var file_moego_enterprise_configuration_v1_configuration_api_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_moego_enterprise_configuration_v1_configuration_api_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_moego_enterprise_configuration_v1_configuration_api_proto_goTypes = []interface{}{
	(GetPetsSettingTimeInfoResult_SettingType)(0),   // 0: moego.enterprise.configuration.v1.GetPetsSettingTimeInfoResult.SettingType
	(*ListSurchargeAssociatedFoodSourceParams)(nil), // 1: moego.enterprise.configuration.v1.ListSurchargeAssociatedFoodSourceParams
	(*ListSurchargeAssociatedFoodSourceResult)(nil), // 2: moego.enterprise.configuration.v1.ListSurchargeAssociatedFoodSourceResult
	(*GetPetsSettingTimeInfoParams)(nil),            // 3: moego.enterprise.configuration.v1.GetPetsSettingTimeInfoParams
	(*GetPetsSettingTimeInfoResult)(nil),            // 4: moego.enterprise.configuration.v1.GetPetsSettingTimeInfoResult
	(*GetPetsSettingTimeInfoResult_TimeInfo)(nil),   // 5: moego.enterprise.configuration.v1.GetPetsSettingTimeInfoResult.TimeInfo
	(*timestamppb.Timestamp)(nil),                   // 6: google.protobuf.Timestamp
	(*CreateLodgingTypeParams)(nil),                 // 7: moego.enterprise.configuration.v1.CreateLodgingTypeParams
	(*UpdateLodgingTypeParams)(nil),                 // 8: moego.enterprise.configuration.v1.UpdateLodgingTypeParams
	(*ListLodgingTypesParams)(nil),                  // 9: moego.enterprise.configuration.v1.ListLodgingTypesParams
	(*PushLodgingTypesParams)(nil),                  // 10: moego.enterprise.configuration.v1.PushLodgingTypesParams
	(*CreatePetCodeParams)(nil),                     // 11: moego.enterprise.configuration.v1.CreatePetCodeParams
	(*UpdatePetCodeParams)(nil),                     // 12: moego.enterprise.configuration.v1.UpdatePetCodeParams
	(*ListPetCodesParams)(nil),                      // 13: moego.enterprise.configuration.v1.ListPetCodesParams
	(*DeletePetCodeParams)(nil),                     // 14: moego.enterprise.configuration.v1.DeletePetCodeParams
	(*PushPetCodesParams)(nil),                      // 15: moego.enterprise.configuration.v1.PushPetCodesParams
	(*SortPetCodesParams)(nil),                      // 16: moego.enterprise.configuration.v1.SortPetCodesParams
	(*CreatePetMetadataParams)(nil),                 // 17: moego.enterprise.configuration.v1.CreatePetMetadataParams
	(*UpdatePetMetadataParams)(nil),                 // 18: moego.enterprise.configuration.v1.UpdatePetMetadataParams
	(*DeletePetMetadataParams)(nil),                 // 19: moego.enterprise.configuration.v1.DeletePetMetadataParams
	(*ListPetMetadataParams)(nil),                   // 20: moego.enterprise.configuration.v1.ListPetMetadataParams
	(*SortPetMetadataParams)(nil),                   // 21: moego.enterprise.configuration.v1.SortPetMetadataParams
	(*PushPetMetadataParams)(nil),                   // 22: moego.enterprise.configuration.v1.PushPetMetadataParams
	(*CreateLodgingTypeResult)(nil),                 // 23: moego.enterprise.configuration.v1.CreateLodgingTypeResult
	(*UpdateLodgingTypeResult)(nil),                 // 24: moego.enterprise.configuration.v1.UpdateLodgingTypeResult
	(*ListLodgingTypesResult)(nil),                  // 25: moego.enterprise.configuration.v1.ListLodgingTypesResult
	(*PushLodgingTypesResult)(nil),                  // 26: moego.enterprise.configuration.v1.PushLodgingTypesResult
	(*CreatePetCodeResult)(nil),                     // 27: moego.enterprise.configuration.v1.CreatePetCodeResult
	(*UpdatePetCodeResult)(nil),                     // 28: moego.enterprise.configuration.v1.UpdatePetCodeResult
	(*ListPetCodesResult)(nil),                      // 29: moego.enterprise.configuration.v1.ListPetCodesResult
	(*DeletePetCodeResult)(nil),                     // 30: moego.enterprise.configuration.v1.DeletePetCodeResult
	(*PushPetCodesResult)(nil),                      // 31: moego.enterprise.configuration.v1.PushPetCodesResult
	(*SortPetCodesResult)(nil),                      // 32: moego.enterprise.configuration.v1.SortPetCodesResult
	(*CreatePetMetadataResult)(nil),                 // 33: moego.enterprise.configuration.v1.CreatePetMetadataResult
	(*UpdatePetMetadataResult)(nil),                 // 34: moego.enterprise.configuration.v1.UpdatePetMetadataResult
	(*DeletePetMetadataResult)(nil),                 // 35: moego.enterprise.configuration.v1.DeletePetMetadataResult
	(*ListPetMetadataResult)(nil),                   // 36: moego.enterprise.configuration.v1.ListPetMetadataResult
	(*SortPetMetadataResult)(nil),                   // 37: moego.enterprise.configuration.v1.SortPetMetadataResult
	(*PushPetMetadataResult)(nil),                   // 38: moego.enterprise.configuration.v1.PushPetMetadataResult
}
var file_moego_enterprise_configuration_v1_configuration_api_proto_depIdxs = []int32{
	5,  // 0: moego.enterprise.configuration.v1.GetPetsSettingTimeInfoResult.time_infos:type_name -> moego.enterprise.configuration.v1.GetPetsSettingTimeInfoResult.TimeInfo
	0,  // 1: moego.enterprise.configuration.v1.GetPetsSettingTimeInfoResult.TimeInfo.type:type_name -> moego.enterprise.configuration.v1.GetPetsSettingTimeInfoResult.SettingType
	6,  // 2: moego.enterprise.configuration.v1.GetPetsSettingTimeInfoResult.TimeInfo.last_updated_at:type_name -> google.protobuf.Timestamp
	6,  // 3: moego.enterprise.configuration.v1.GetPetsSettingTimeInfoResult.TimeInfo.last_pushed_at:type_name -> google.protobuf.Timestamp
	7,  // 4: moego.enterprise.configuration.v1.ConfigurationService.CreateLodgingType:input_type -> moego.enterprise.configuration.v1.CreateLodgingTypeParams
	8,  // 5: moego.enterprise.configuration.v1.ConfigurationService.UpdateLodgingType:input_type -> moego.enterprise.configuration.v1.UpdateLodgingTypeParams
	9,  // 6: moego.enterprise.configuration.v1.ConfigurationService.ListLodgingTypes:input_type -> moego.enterprise.configuration.v1.ListLodgingTypesParams
	10, // 7: moego.enterprise.configuration.v1.ConfigurationService.PushLodgingTypes:input_type -> moego.enterprise.configuration.v1.PushLodgingTypesParams
	11, // 8: moego.enterprise.configuration.v1.ConfigurationService.CreatePetCode:input_type -> moego.enterprise.configuration.v1.CreatePetCodeParams
	12, // 9: moego.enterprise.configuration.v1.ConfigurationService.UpdatePetCode:input_type -> moego.enterprise.configuration.v1.UpdatePetCodeParams
	13, // 10: moego.enterprise.configuration.v1.ConfigurationService.ListPetCodes:input_type -> moego.enterprise.configuration.v1.ListPetCodesParams
	14, // 11: moego.enterprise.configuration.v1.ConfigurationService.DeletePetCode:input_type -> moego.enterprise.configuration.v1.DeletePetCodeParams
	15, // 12: moego.enterprise.configuration.v1.ConfigurationService.PushPetCodes:input_type -> moego.enterprise.configuration.v1.PushPetCodesParams
	16, // 13: moego.enterprise.configuration.v1.ConfigurationService.SortPetCodes:input_type -> moego.enterprise.configuration.v1.SortPetCodesParams
	17, // 14: moego.enterprise.configuration.v1.ConfigurationService.CreatePetMetadata:input_type -> moego.enterprise.configuration.v1.CreatePetMetadataParams
	18, // 15: moego.enterprise.configuration.v1.ConfigurationService.UpdatePetMetadata:input_type -> moego.enterprise.configuration.v1.UpdatePetMetadataParams
	19, // 16: moego.enterprise.configuration.v1.ConfigurationService.DeletePetMetadata:input_type -> moego.enterprise.configuration.v1.DeletePetMetadataParams
	20, // 17: moego.enterprise.configuration.v1.ConfigurationService.ListPetMetadata:input_type -> moego.enterprise.configuration.v1.ListPetMetadataParams
	21, // 18: moego.enterprise.configuration.v1.ConfigurationService.SortPetMetadata:input_type -> moego.enterprise.configuration.v1.SortPetMetadataParams
	22, // 19: moego.enterprise.configuration.v1.ConfigurationService.PushPetMetadata:input_type -> moego.enterprise.configuration.v1.PushPetMetadataParams
	1,  // 20: moego.enterprise.configuration.v1.ConfigurationService.ListSurchargeAssociatedFoodSource:input_type -> moego.enterprise.configuration.v1.ListSurchargeAssociatedFoodSourceParams
	3,  // 21: moego.enterprise.configuration.v1.ConfigurationService.GetPetsSettingTimeInfo:input_type -> moego.enterprise.configuration.v1.GetPetsSettingTimeInfoParams
	23, // 22: moego.enterprise.configuration.v1.ConfigurationService.CreateLodgingType:output_type -> moego.enterprise.configuration.v1.CreateLodgingTypeResult
	24, // 23: moego.enterprise.configuration.v1.ConfigurationService.UpdateLodgingType:output_type -> moego.enterprise.configuration.v1.UpdateLodgingTypeResult
	25, // 24: moego.enterprise.configuration.v1.ConfigurationService.ListLodgingTypes:output_type -> moego.enterprise.configuration.v1.ListLodgingTypesResult
	26, // 25: moego.enterprise.configuration.v1.ConfigurationService.PushLodgingTypes:output_type -> moego.enterprise.configuration.v1.PushLodgingTypesResult
	27, // 26: moego.enterprise.configuration.v1.ConfigurationService.CreatePetCode:output_type -> moego.enterprise.configuration.v1.CreatePetCodeResult
	28, // 27: moego.enterprise.configuration.v1.ConfigurationService.UpdatePetCode:output_type -> moego.enterprise.configuration.v1.UpdatePetCodeResult
	29, // 28: moego.enterprise.configuration.v1.ConfigurationService.ListPetCodes:output_type -> moego.enterprise.configuration.v1.ListPetCodesResult
	30, // 29: moego.enterprise.configuration.v1.ConfigurationService.DeletePetCode:output_type -> moego.enterprise.configuration.v1.DeletePetCodeResult
	31, // 30: moego.enterprise.configuration.v1.ConfigurationService.PushPetCodes:output_type -> moego.enterprise.configuration.v1.PushPetCodesResult
	32, // 31: moego.enterprise.configuration.v1.ConfigurationService.SortPetCodes:output_type -> moego.enterprise.configuration.v1.SortPetCodesResult
	33, // 32: moego.enterprise.configuration.v1.ConfigurationService.CreatePetMetadata:output_type -> moego.enterprise.configuration.v1.CreatePetMetadataResult
	34, // 33: moego.enterprise.configuration.v1.ConfigurationService.UpdatePetMetadata:output_type -> moego.enterprise.configuration.v1.UpdatePetMetadataResult
	35, // 34: moego.enterprise.configuration.v1.ConfigurationService.DeletePetMetadata:output_type -> moego.enterprise.configuration.v1.DeletePetMetadataResult
	36, // 35: moego.enterprise.configuration.v1.ConfigurationService.ListPetMetadata:output_type -> moego.enterprise.configuration.v1.ListPetMetadataResult
	37, // 36: moego.enterprise.configuration.v1.ConfigurationService.SortPetMetadata:output_type -> moego.enterprise.configuration.v1.SortPetMetadataResult
	38, // 37: moego.enterprise.configuration.v1.ConfigurationService.PushPetMetadata:output_type -> moego.enterprise.configuration.v1.PushPetMetadataResult
	2,  // 38: moego.enterprise.configuration.v1.ConfigurationService.ListSurchargeAssociatedFoodSource:output_type -> moego.enterprise.configuration.v1.ListSurchargeAssociatedFoodSourceResult
	4,  // 39: moego.enterprise.configuration.v1.ConfigurationService.GetPetsSettingTimeInfo:output_type -> moego.enterprise.configuration.v1.GetPetsSettingTimeInfoResult
	22, // [22:40] is the sub-list for method output_type
	4,  // [4:22] is the sub-list for method input_type
	4,  // [4:4] is the sub-list for extension type_name
	4,  // [4:4] is the sub-list for extension extendee
	0,  // [0:4] is the sub-list for field type_name
}

func init() { file_moego_enterprise_configuration_v1_configuration_api_proto_init() }
func file_moego_enterprise_configuration_v1_configuration_api_proto_init() {
	if File_moego_enterprise_configuration_v1_configuration_api_proto != nil {
		return
	}
	file_moego_enterprise_configuration_v1_pet_settings_proto_init()
	file_moego_enterprise_configuration_v1_service_settings_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_enterprise_configuration_v1_configuration_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListSurchargeAssociatedFoodSourceParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_configuration_v1_configuration_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListSurchargeAssociatedFoodSourceResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_configuration_v1_configuration_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPetsSettingTimeInfoParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_configuration_v1_configuration_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPetsSettingTimeInfoResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_configuration_v1_configuration_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPetsSettingTimeInfoResult_TimeInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_enterprise_configuration_v1_configuration_api_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_enterprise_configuration_v1_configuration_api_proto_goTypes,
		DependencyIndexes: file_moego_enterprise_configuration_v1_configuration_api_proto_depIdxs,
		EnumInfos:         file_moego_enterprise_configuration_v1_configuration_api_proto_enumTypes,
		MessageInfos:      file_moego_enterprise_configuration_v1_configuration_api_proto_msgTypes,
	}.Build()
	File_moego_enterprise_configuration_v1_configuration_api_proto = out.File
	file_moego_enterprise_configuration_v1_configuration_api_proto_rawDesc = nil
	file_moego_enterprise_configuration_v1_configuration_api_proto_goTypes = nil
	file_moego_enterprise_configuration_v1_configuration_api_proto_depIdxs = nil
}
