// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/enterprise/template_push/v1/template_push_api.proto

package templatepushapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// TemplatePushServiceClient is the client API for TemplatePushService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type TemplatePushServiceClient interface {
	// ListTemplatePushChanges
	ListTemplatePushChanges(ctx context.Context, in *ListTemplatePushChangesParams, opts ...grpc.CallOption) (*ListTemplatePushChangesResult, error)
	// ListTemplatePushHistories
	ListTemplatePushHistories(ctx context.Context, in *ListTemplatePushHistoriesParams, opts ...grpc.CallOption) (*ListTemplatePushHistoriesResult, error)
	// check template push
	CheckTemplatePush(ctx context.Context, in *CheckTemplatePushParams, opts ...grpc.CallOption) (*CheckTemplatePushResult, error)
}

type templatePushServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewTemplatePushServiceClient(cc grpc.ClientConnInterface) TemplatePushServiceClient {
	return &templatePushServiceClient{cc}
}

func (c *templatePushServiceClient) ListTemplatePushChanges(ctx context.Context, in *ListTemplatePushChangesParams, opts ...grpc.CallOption) (*ListTemplatePushChangesResult, error) {
	out := new(ListTemplatePushChangesResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.template_push.v1.TemplatePushService/ListTemplatePushChanges", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *templatePushServiceClient) ListTemplatePushHistories(ctx context.Context, in *ListTemplatePushHistoriesParams, opts ...grpc.CallOption) (*ListTemplatePushHistoriesResult, error) {
	out := new(ListTemplatePushHistoriesResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.template_push.v1.TemplatePushService/ListTemplatePushHistories", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *templatePushServiceClient) CheckTemplatePush(ctx context.Context, in *CheckTemplatePushParams, opts ...grpc.CallOption) (*CheckTemplatePushResult, error) {
	out := new(CheckTemplatePushResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.template_push.v1.TemplatePushService/CheckTemplatePush", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TemplatePushServiceServer is the server API for TemplatePushService service.
// All implementations must embed UnimplementedTemplatePushServiceServer
// for forward compatibility
type TemplatePushServiceServer interface {
	// ListTemplatePushChanges
	ListTemplatePushChanges(context.Context, *ListTemplatePushChangesParams) (*ListTemplatePushChangesResult, error)
	// ListTemplatePushHistories
	ListTemplatePushHistories(context.Context, *ListTemplatePushHistoriesParams) (*ListTemplatePushHistoriesResult, error)
	// check template push
	CheckTemplatePush(context.Context, *CheckTemplatePushParams) (*CheckTemplatePushResult, error)
	mustEmbedUnimplementedTemplatePushServiceServer()
}

// UnimplementedTemplatePushServiceServer must be embedded to have forward compatible implementations.
type UnimplementedTemplatePushServiceServer struct {
}

func (UnimplementedTemplatePushServiceServer) ListTemplatePushChanges(context.Context, *ListTemplatePushChangesParams) (*ListTemplatePushChangesResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListTemplatePushChanges not implemented")
}
func (UnimplementedTemplatePushServiceServer) ListTemplatePushHistories(context.Context, *ListTemplatePushHistoriesParams) (*ListTemplatePushHistoriesResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListTemplatePushHistories not implemented")
}
func (UnimplementedTemplatePushServiceServer) CheckTemplatePush(context.Context, *CheckTemplatePushParams) (*CheckTemplatePushResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckTemplatePush not implemented")
}
func (UnimplementedTemplatePushServiceServer) mustEmbedUnimplementedTemplatePushServiceServer() {}

// UnsafeTemplatePushServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to TemplatePushServiceServer will
// result in compilation errors.
type UnsafeTemplatePushServiceServer interface {
	mustEmbedUnimplementedTemplatePushServiceServer()
}

func RegisterTemplatePushServiceServer(s grpc.ServiceRegistrar, srv TemplatePushServiceServer) {
	s.RegisterService(&TemplatePushService_ServiceDesc, srv)
}

func _TemplatePushService_ListTemplatePushChanges_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListTemplatePushChangesParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TemplatePushServiceServer).ListTemplatePushChanges(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.template_push.v1.TemplatePushService/ListTemplatePushChanges",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TemplatePushServiceServer).ListTemplatePushChanges(ctx, req.(*ListTemplatePushChangesParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _TemplatePushService_ListTemplatePushHistories_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListTemplatePushHistoriesParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TemplatePushServiceServer).ListTemplatePushHistories(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.template_push.v1.TemplatePushService/ListTemplatePushHistories",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TemplatePushServiceServer).ListTemplatePushHistories(ctx, req.(*ListTemplatePushHistoriesParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _TemplatePushService_CheckTemplatePush_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckTemplatePushParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TemplatePushServiceServer).CheckTemplatePush(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.template_push.v1.TemplatePushService/CheckTemplatePush",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TemplatePushServiceServer).CheckTemplatePush(ctx, req.(*CheckTemplatePushParams))
	}
	return interceptor(ctx, in, info, handler)
}

// TemplatePushService_ServiceDesc is the grpc.ServiceDesc for TemplatePushService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var TemplatePushService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.enterprise.template_push.v1.TemplatePushService",
	HandlerType: (*TemplatePushServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListTemplatePushChanges",
			Handler:    _TemplatePushService_ListTemplatePushChanges_Handler,
		},
		{
			MethodName: "ListTemplatePushHistories",
			Handler:    _TemplatePushService_ListTemplatePushHistories_Handler,
		},
		{
			MethodName: "CheckTemplatePush",
			Handler:    _TemplatePushService_CheckTemplatePush_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/enterprise/template_push/v1/template_push_api.proto",
}
