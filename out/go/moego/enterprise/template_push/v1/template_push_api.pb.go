// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/enterprise/template_push/v1/template_push_api.proto

package templatepushapipb

import (
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/enterprise/v1"
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/enterprise/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// ListTemplatePushChangesParams
type ListTemplatePushChangesParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// filter
	Filter *v1.ListTemplatePushChangesRequest_Filter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
}

func (x *ListTemplatePushChangesParams) Reset() {
	*x = ListTemplatePushChangesParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_template_push_v1_template_push_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListTemplatePushChangesParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTemplatePushChangesParams) ProtoMessage() {}

func (x *ListTemplatePushChangesParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_template_push_v1_template_push_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTemplatePushChangesParams.ProtoReflect.Descriptor instead.
func (*ListTemplatePushChangesParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_template_push_v1_template_push_api_proto_rawDescGZIP(), []int{0}
}

func (x *ListTemplatePushChangesParams) GetFilter() *v1.ListTemplatePushChangesRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

// ListTemplatePushChangesResult
type ListTemplatePushChangesResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// template push changes
	TemplatePushChanges []*v11.TemplatePushChange `protobuf:"bytes,1,rep,name=template_push_changes,json=templatePushChanges,proto3" json:"template_push_changes,omitempty"`
}

func (x *ListTemplatePushChangesResult) Reset() {
	*x = ListTemplatePushChangesResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_template_push_v1_template_push_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListTemplatePushChangesResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTemplatePushChangesResult) ProtoMessage() {}

func (x *ListTemplatePushChangesResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_template_push_v1_template_push_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTemplatePushChangesResult.ProtoReflect.Descriptor instead.
func (*ListTemplatePushChangesResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_template_push_v1_template_push_api_proto_rawDescGZIP(), []int{1}
}

func (x *ListTemplatePushChangesResult) GetTemplatePushChanges() []*v11.TemplatePushChange {
	if x != nil {
		return x.TemplatePushChanges
	}
	return nil
}

// ListTemplatePushHistoriesParams
type ListTemplatePushHistoriesParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// filter
	Filter *v1.ListTemplatePushHistoriesRequest_Filter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
	// order bys
	OrderBys []*v11.TemplatePushChangeHistoryOrderBy `protobuf:"bytes,2,rep,name=order_bys,json=orderBys,proto3" json:"order_bys,omitempty"`
}

func (x *ListTemplatePushHistoriesParams) Reset() {
	*x = ListTemplatePushHistoriesParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_template_push_v1_template_push_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListTemplatePushHistoriesParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTemplatePushHistoriesParams) ProtoMessage() {}

func (x *ListTemplatePushHistoriesParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_template_push_v1_template_push_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTemplatePushHistoriesParams.ProtoReflect.Descriptor instead.
func (*ListTemplatePushHistoriesParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_template_push_v1_template_push_api_proto_rawDescGZIP(), []int{2}
}

func (x *ListTemplatePushHistoriesParams) GetFilter() *v1.ListTemplatePushHistoriesRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ListTemplatePushHistoriesParams) GetOrderBys() []*v11.TemplatePushChangeHistoryOrderBy {
	if x != nil {
		return x.OrderBys
	}
	return nil
}

// ListTemplatePushHistoriesResult
type ListTemplatePushHistoriesResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// template push histories
	TemplatePushHistories []*v11.TemplatePushHistory `protobuf:"bytes,1,rep,name=template_push_histories,json=templatePushHistories,proto3" json:"template_push_histories,omitempty"`
	// tenants
	Tenants []*v11.TenantModel `protobuf:"bytes,2,rep,name=tenants,proto3" json:"tenants,omitempty"`
}

func (x *ListTemplatePushHistoriesResult) Reset() {
	*x = ListTemplatePushHistoriesResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_template_push_v1_template_push_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListTemplatePushHistoriesResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTemplatePushHistoriesResult) ProtoMessage() {}

func (x *ListTemplatePushHistoriesResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_template_push_v1_template_push_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTemplatePushHistoriesResult.ProtoReflect.Descriptor instead.
func (*ListTemplatePushHistoriesResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_template_push_v1_template_push_api_proto_rawDescGZIP(), []int{3}
}

func (x *ListTemplatePushHistoriesResult) GetTemplatePushHistories() []*v11.TemplatePushHistory {
	if x != nil {
		return x.TemplatePushHistories
	}
	return nil
}

func (x *ListTemplatePushHistoriesResult) GetTenants() []*v11.TenantModel {
	if x != nil {
		return x.Tenants
	}
	return nil
}

// CheckTemplatePushParams
type CheckTemplatePushParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// template type
	TemplateType v11.TemplateType `protobuf:"varint,1,opt,name=template_type,json=templateType,proto3,enum=moego.models.enterprise.v1.TemplateType" json:"template_type,omitempty"`
	// template ids
	TemplateIds []int64 `protobuf:"varint,2,rep,packed,name=template_ids,json=templateIds,proto3" json:"template_ids,omitempty"`
	// target tenant
	Targets []*v11.TenantObject `protobuf:"bytes,3,rep,name=targets,proto3" json:"targets,omitempty"`
}

func (x *CheckTemplatePushParams) Reset() {
	*x = CheckTemplatePushParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_template_push_v1_template_push_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckTemplatePushParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckTemplatePushParams) ProtoMessage() {}

func (x *CheckTemplatePushParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_template_push_v1_template_push_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckTemplatePushParams.ProtoReflect.Descriptor instead.
func (*CheckTemplatePushParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_template_push_v1_template_push_api_proto_rawDescGZIP(), []int{4}
}

func (x *CheckTemplatePushParams) GetTemplateType() v11.TemplateType {
	if x != nil {
		return x.TemplateType
	}
	return v11.TemplateType(0)
}

func (x *CheckTemplatePushParams) GetTemplateIds() []int64 {
	if x != nil {
		return x.TemplateIds
	}
	return nil
}

func (x *CheckTemplatePushParams) GetTargets() []*v11.TenantObject {
	if x != nil {
		return x.Targets
	}
	return nil
}

// CheckTemplatePushResult
type CheckTemplatePushResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// conflicts
	Conflicts []*v11.TemplatePushConflict `protobuf:"bytes,1,rep,name=conflicts,proto3" json:"conflicts,omitempty"`
	// tenants
	Tenants []*v11.TenantModel `protobuf:"bytes,2,rep,name=tenants,proto3" json:"tenants,omitempty"`
	// name mapping
	NameMapping map[int64]string `protobuf:"bytes,3,rep,name=name_mapping,json=nameMapping,proto3" json:"name_mapping,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *CheckTemplatePushResult) Reset() {
	*x = CheckTemplatePushResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_template_push_v1_template_push_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckTemplatePushResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckTemplatePushResult) ProtoMessage() {}

func (x *CheckTemplatePushResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_template_push_v1_template_push_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckTemplatePushResult.ProtoReflect.Descriptor instead.
func (*CheckTemplatePushResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_template_push_v1_template_push_api_proto_rawDescGZIP(), []int{5}
}

func (x *CheckTemplatePushResult) GetConflicts() []*v11.TemplatePushConflict {
	if x != nil {
		return x.Conflicts
	}
	return nil
}

func (x *CheckTemplatePushResult) GetTenants() []*v11.TenantModel {
	if x != nil {
		return x.Tenants
	}
	return nil
}

func (x *CheckTemplatePushResult) GetNameMapping() map[int64]string {
	if x != nil {
		return x.NameMapping
	}
	return nil
}

var File_moego_enterprise_template_push_v1_template_push_api_proto protoreflect.FileDescriptor

var file_moego_enterprise_template_push_v1_template_push_api_proto_rawDesc = []byte{
	0x0a, 0x39, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x2f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x70, 0x75, 0x73, 0x68,
	0x2f, 0x76, 0x31, 0x2f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x70, 0x75, 0x73,
	0x68, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x21, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x74, 0x65,
	0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x70, 0x75, 0x73, 0x68, 0x2e, 0x76, 0x31, 0x1a, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x5f, 0x70, 0x75, 0x73, 0x68, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x65, 0x6e, 0x61, 0x6e,
	0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x37,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x5f, 0x70, 0x75, 0x73, 0x68, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0x7b, 0x0a, 0x1d, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x50, 0x75, 0x73, 0x68, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x12, 0x5a, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x42, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x75, 0x73, 0x68,
	0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x46,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x22, 0x83, 0x01,
	0x0a, 0x1d, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x75,
	0x73, 0x68, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x62, 0x0a, 0x15, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x70, 0x75, 0x73, 0x68,
	0x5f, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x50, 0x75, 0x73, 0x68, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x13,
	0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x75, 0x73, 0x68, 0x43, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x73, 0x22, 0xda, 0x01, 0x0a, 0x1f, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x50, 0x75, 0x73, 0x68, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x69, 0x65,
	0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x5c, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x44, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x50, 0x75, 0x73, 0x68, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x06, 0x66,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x59, 0x0a, 0x09, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x62,
	0x79, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x75,
	0x73, 0x68, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x52, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x73,
	0x22, 0xcd, 0x01, 0x0a, 0x1f, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x50, 0x75, 0x73, 0x68, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x67, 0x0a, 0x17, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x5f, 0x70, 0x75, 0x73, 0x68, 0x5f, 0x68, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x75, 0x73, 0x68, 0x48,
	0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x15, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x50, 0x75, 0x73, 0x68, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x12, 0x41, 0x0a,
	0x07, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x6e, 0x61,
	0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x07, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x73,
	0x22, 0xf5, 0x01, 0x0a, 0x17, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x50, 0x75, 0x73, 0x68, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x59, 0x0a, 0x0d,
	0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa,
	0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x0c, 0x74, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x31, 0x0a, 0x0c, 0x74, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x42, 0x0e, 0xfa,
	0x42, 0x0b, 0x92, 0x01, 0x08, 0x08, 0x01, 0x22, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0b, 0x74,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x49, 0x64, 0x73, 0x12, 0x4c, 0x0a, 0x07, 0x74, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x4f,
	0x62, 0x6a, 0x65, 0x63, 0x74, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x08, 0x01, 0x52,
	0x07, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x73, 0x22, 0xdc, 0x02, 0x0a, 0x17, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x75, 0x73, 0x68, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x4e, 0x0a, 0x09, 0x63, 0x6f, 0x6e, 0x66, 0x6c, 0x69, 0x63, 0x74,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x75, 0x73,
	0x68, 0x43, 0x6f, 0x6e, 0x66, 0x6c, 0x69, 0x63, 0x74, 0x52, 0x09, 0x63, 0x6f, 0x6e, 0x66, 0x6c,
	0x69, 0x63, 0x74, 0x73, 0x12, 0x41, 0x0a, 0x07, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x07,
	0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x73, 0x12, 0x6e, 0x0a, 0x0c, 0x6e, 0x61, 0x6d, 0x65, 0x5f,
	0x6d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4b, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x2e, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x70, 0x75, 0x73, 0x68, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50,
	0x75, 0x73, 0x68, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x4d, 0x61,
	0x70, 0x70, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0b, 0x6e, 0x61, 0x6d, 0x65,
	0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x1a, 0x3e, 0x0a, 0x10, 0x4e, 0x61, 0x6d, 0x65, 0x4d,
	0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x32, 0xe9, 0x03, 0x0a, 0x13, 0x54, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x50, 0x75, 0x73, 0x68, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12,
	0x9d, 0x01, 0x0a, 0x17, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x50, 0x75, 0x73, 0x68, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x12, 0x40, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x74,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x70, 0x75, 0x73, 0x68, 0x2e, 0x76, 0x31, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x75, 0x73, 0x68,
	0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x40, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x2e, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x70, 0x75, 0x73, 0x68, 0x2e, 0x76,
	0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x75,
	0x73, 0x68, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0xa3, 0x01, 0x0a, 0x19, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x50, 0x75, 0x73, 0x68, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x12, 0x42, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x2e, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x70, 0x75, 0x73, 0x68, 0x2e, 0x76,
	0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x75,
	0x73, 0x68, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x1a, 0x42, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x2e, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x70, 0x75,
	0x73, 0x68, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x50, 0x75, 0x73, 0x68, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x8b, 0x01, 0x0a, 0x11, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x54,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x75, 0x73, 0x68, 0x12, 0x3a, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x74,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x70, 0x75, 0x73, 0x68, 0x2e, 0x76, 0x31, 0x2e,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x75, 0x73,
	0x68, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x3a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x74, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x5f, 0x70, 0x75, 0x73, 0x68, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x75, 0x73, 0x68, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x42, 0x96, 0x01, 0x0a, 0x28, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x2e, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x70, 0x75, 0x73, 0x68, 0x2e, 0x76, 0x31,
	0x50, 0x01, 0x5a, 0x68, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d,
	0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x5f, 0x70, 0x75, 0x73, 0x68, 0x2f, 0x76, 0x31, 0x3b, 0x74, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x70, 0x75, 0x73, 0x68, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_enterprise_template_push_v1_template_push_api_proto_rawDescOnce sync.Once
	file_moego_enterprise_template_push_v1_template_push_api_proto_rawDescData = file_moego_enterprise_template_push_v1_template_push_api_proto_rawDesc
)

func file_moego_enterprise_template_push_v1_template_push_api_proto_rawDescGZIP() []byte {
	file_moego_enterprise_template_push_v1_template_push_api_proto_rawDescOnce.Do(func() {
		file_moego_enterprise_template_push_v1_template_push_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_enterprise_template_push_v1_template_push_api_proto_rawDescData)
	})
	return file_moego_enterprise_template_push_v1_template_push_api_proto_rawDescData
}

var file_moego_enterprise_template_push_v1_template_push_api_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_moego_enterprise_template_push_v1_template_push_api_proto_goTypes = []interface{}{
	(*ListTemplatePushChangesParams)(nil),              // 0: moego.enterprise.template_push.v1.ListTemplatePushChangesParams
	(*ListTemplatePushChangesResult)(nil),              // 1: moego.enterprise.template_push.v1.ListTemplatePushChangesResult
	(*ListTemplatePushHistoriesParams)(nil),            // 2: moego.enterprise.template_push.v1.ListTemplatePushHistoriesParams
	(*ListTemplatePushHistoriesResult)(nil),            // 3: moego.enterprise.template_push.v1.ListTemplatePushHistoriesResult
	(*CheckTemplatePushParams)(nil),                    // 4: moego.enterprise.template_push.v1.CheckTemplatePushParams
	(*CheckTemplatePushResult)(nil),                    // 5: moego.enterprise.template_push.v1.CheckTemplatePushResult
	nil,                                                // 6: moego.enterprise.template_push.v1.CheckTemplatePushResult.NameMappingEntry
	(*v1.ListTemplatePushChangesRequest_Filter)(nil),   // 7: moego.service.enterprise.v1.ListTemplatePushChangesRequest.Filter
	(*v11.TemplatePushChange)(nil),                     // 8: moego.models.enterprise.v1.TemplatePushChange
	(*v1.ListTemplatePushHistoriesRequest_Filter)(nil), // 9: moego.service.enterprise.v1.ListTemplatePushHistoriesRequest.Filter
	(*v11.TemplatePushChangeHistoryOrderBy)(nil),       // 10: moego.models.enterprise.v1.TemplatePushChangeHistoryOrderBy
	(*v11.TemplatePushHistory)(nil),                    // 11: moego.models.enterprise.v1.TemplatePushHistory
	(*v11.TenantModel)(nil),                            // 12: moego.models.enterprise.v1.TenantModel
	(v11.TemplateType)(0),                              // 13: moego.models.enterprise.v1.TemplateType
	(*v11.TenantObject)(nil),                           // 14: moego.models.enterprise.v1.TenantObject
	(*v11.TemplatePushConflict)(nil),                   // 15: moego.models.enterprise.v1.TemplatePushConflict
}
var file_moego_enterprise_template_push_v1_template_push_api_proto_depIdxs = []int32{
	7,  // 0: moego.enterprise.template_push.v1.ListTemplatePushChangesParams.filter:type_name -> moego.service.enterprise.v1.ListTemplatePushChangesRequest.Filter
	8,  // 1: moego.enterprise.template_push.v1.ListTemplatePushChangesResult.template_push_changes:type_name -> moego.models.enterprise.v1.TemplatePushChange
	9,  // 2: moego.enterprise.template_push.v1.ListTemplatePushHistoriesParams.filter:type_name -> moego.service.enterprise.v1.ListTemplatePushHistoriesRequest.Filter
	10, // 3: moego.enterprise.template_push.v1.ListTemplatePushHistoriesParams.order_bys:type_name -> moego.models.enterprise.v1.TemplatePushChangeHistoryOrderBy
	11, // 4: moego.enterprise.template_push.v1.ListTemplatePushHistoriesResult.template_push_histories:type_name -> moego.models.enterprise.v1.TemplatePushHistory
	12, // 5: moego.enterprise.template_push.v1.ListTemplatePushHistoriesResult.tenants:type_name -> moego.models.enterprise.v1.TenantModel
	13, // 6: moego.enterprise.template_push.v1.CheckTemplatePushParams.template_type:type_name -> moego.models.enterprise.v1.TemplateType
	14, // 7: moego.enterprise.template_push.v1.CheckTemplatePushParams.targets:type_name -> moego.models.enterprise.v1.TenantObject
	15, // 8: moego.enterprise.template_push.v1.CheckTemplatePushResult.conflicts:type_name -> moego.models.enterprise.v1.TemplatePushConflict
	12, // 9: moego.enterprise.template_push.v1.CheckTemplatePushResult.tenants:type_name -> moego.models.enterprise.v1.TenantModel
	6,  // 10: moego.enterprise.template_push.v1.CheckTemplatePushResult.name_mapping:type_name -> moego.enterprise.template_push.v1.CheckTemplatePushResult.NameMappingEntry
	0,  // 11: moego.enterprise.template_push.v1.TemplatePushService.ListTemplatePushChanges:input_type -> moego.enterprise.template_push.v1.ListTemplatePushChangesParams
	2,  // 12: moego.enterprise.template_push.v1.TemplatePushService.ListTemplatePushHistories:input_type -> moego.enterprise.template_push.v1.ListTemplatePushHistoriesParams
	4,  // 13: moego.enterprise.template_push.v1.TemplatePushService.CheckTemplatePush:input_type -> moego.enterprise.template_push.v1.CheckTemplatePushParams
	1,  // 14: moego.enterprise.template_push.v1.TemplatePushService.ListTemplatePushChanges:output_type -> moego.enterprise.template_push.v1.ListTemplatePushChangesResult
	3,  // 15: moego.enterprise.template_push.v1.TemplatePushService.ListTemplatePushHistories:output_type -> moego.enterprise.template_push.v1.ListTemplatePushHistoriesResult
	5,  // 16: moego.enterprise.template_push.v1.TemplatePushService.CheckTemplatePush:output_type -> moego.enterprise.template_push.v1.CheckTemplatePushResult
	14, // [14:17] is the sub-list for method output_type
	11, // [11:14] is the sub-list for method input_type
	11, // [11:11] is the sub-list for extension type_name
	11, // [11:11] is the sub-list for extension extendee
	0,  // [0:11] is the sub-list for field type_name
}

func init() { file_moego_enterprise_template_push_v1_template_push_api_proto_init() }
func file_moego_enterprise_template_push_v1_template_push_api_proto_init() {
	if File_moego_enterprise_template_push_v1_template_push_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_enterprise_template_push_v1_template_push_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListTemplatePushChangesParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_template_push_v1_template_push_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListTemplatePushChangesResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_template_push_v1_template_push_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListTemplatePushHistoriesParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_template_push_v1_template_push_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListTemplatePushHistoriesResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_template_push_v1_template_push_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckTemplatePushParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_template_push_v1_template_push_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckTemplatePushResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_enterprise_template_push_v1_template_push_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_enterprise_template_push_v1_template_push_api_proto_goTypes,
		DependencyIndexes: file_moego_enterprise_template_push_v1_template_push_api_proto_depIdxs,
		MessageInfos:      file_moego_enterprise_template_push_v1_template_push_api_proto_msgTypes,
	}.Build()
	File_moego_enterprise_template_push_v1_template_push_api_proto = out.File
	file_moego_enterprise_template_push_v1_template_push_api_proto_rawDesc = nil
	file_moego_enterprise_template_push_v1_template_push_api_proto_goTypes = nil
	file_moego_enterprise_template_push_v1_template_push_api_proto_depIdxs = nil
}
