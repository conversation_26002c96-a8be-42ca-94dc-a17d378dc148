// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/enterprise/price_book/v1/price_book_api.proto

package pricebookapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/enterprise/v1"
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	v12 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/enterprise/v1"
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// ListPriceBooksParams
type ListPriceBooksParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ListPriceBooksParams) Reset() {
	*x = ListPriceBooksParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPriceBooksParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPriceBooksParams) ProtoMessage() {}

func (x *ListPriceBooksParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPriceBooksParams.ProtoReflect.Descriptor instead.
func (*ListPriceBooksParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_price_book_v1_price_book_api_proto_rawDescGZIP(), []int{0}
}

// ListPriceBooksResult
type ListPriceBooksResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// price books
	PriceBooks []*v1.PriceBook `protobuf:"bytes,1,rep,name=price_books,json=priceBooks,proto3" json:"price_books,omitempty"`
}

func (x *ListPriceBooksResult) Reset() {
	*x = ListPriceBooksResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPriceBooksResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPriceBooksResult) ProtoMessage() {}

func (x *ListPriceBooksResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPriceBooksResult.ProtoReflect.Descriptor instead.
func (*ListPriceBooksResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_price_book_v1_price_book_api_proto_rawDescGZIP(), []int{1}
}

func (x *ListPriceBooksResult) GetPriceBooks() []*v1.PriceBook {
	if x != nil {
		return x.PriceBooks
	}
	return nil
}

// CreatePriceBookParams
type CreatePriceBookParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// name
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *CreatePriceBookParams) Reset() {
	*x = CreatePriceBookParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreatePriceBookParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePriceBookParams) ProtoMessage() {}

func (x *CreatePriceBookParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePriceBookParams.ProtoReflect.Descriptor instead.
func (*CreatePriceBookParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_price_book_v1_price_book_api_proto_rawDescGZIP(), []int{2}
}

func (x *CreatePriceBookParams) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// CreatePriceBookResult
type CreatePriceBookResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// price book
	PriceBook *v1.PriceBook `protobuf:"bytes,1,opt,name=price_book,json=priceBook,proto3" json:"price_book,omitempty"`
}

func (x *CreatePriceBookResult) Reset() {
	*x = CreatePriceBookResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreatePriceBookResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePriceBookResult) ProtoMessage() {}

func (x *CreatePriceBookResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePriceBookResult.ProtoReflect.Descriptor instead.
func (*CreatePriceBookResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_price_book_v1_price_book_api_proto_rawDescGZIP(), []int{3}
}

func (x *CreatePriceBookResult) GetPriceBook() *v1.PriceBook {
	if x != nil {
		return x.PriceBook
	}
	return nil
}

// UpdatePriceBookParams
type UpdatePriceBookParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// name
	Name *string `protobuf:"bytes,2,opt,name=name,proto3,oneof" json:"name,omitempty"`
}

func (x *UpdatePriceBookParams) Reset() {
	*x = UpdatePriceBookParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdatePriceBookParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePriceBookParams) ProtoMessage() {}

func (x *UpdatePriceBookParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePriceBookParams.ProtoReflect.Descriptor instead.
func (*UpdatePriceBookParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_price_book_v1_price_book_api_proto_rawDescGZIP(), []int{4}
}

func (x *UpdatePriceBookParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdatePriceBookParams) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

// UpdatePriceBookResult
type UpdatePriceBookResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// price book
	PriceBook *v1.PriceBook `protobuf:"bytes,1,opt,name=price_book,json=priceBook,proto3" json:"price_book,omitempty"`
}

func (x *UpdatePriceBookResult) Reset() {
	*x = UpdatePriceBookResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdatePriceBookResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePriceBookResult) ProtoMessage() {}

func (x *UpdatePriceBookResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePriceBookResult.ProtoReflect.Descriptor instead.
func (*UpdatePriceBookResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_price_book_v1_price_book_api_proto_rawDescGZIP(), []int{5}
}

func (x *UpdatePriceBookResult) GetPriceBook() *v1.PriceBook {
	if x != nil {
		return x.PriceBook
	}
	return nil
}

// DeletePriceBookParams
type DeletePriceBookParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *DeletePriceBookParams) Reset() {
	*x = DeletePriceBookParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeletePriceBookParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePriceBookParams) ProtoMessage() {}

func (x *DeletePriceBookParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePriceBookParams.ProtoReflect.Descriptor instead.
func (*DeletePriceBookParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_price_book_v1_price_book_api_proto_rawDescGZIP(), []int{6}
}

func (x *DeletePriceBookParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// DeletePriceBookResult
type DeletePriceBookResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeletePriceBookResult) Reset() {
	*x = DeletePriceBookResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeletePriceBookResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePriceBookResult) ProtoMessage() {}

func (x *DeletePriceBookResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePriceBookResult.ProtoReflect.Descriptor instead.
func (*DeletePriceBookResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_price_book_v1_price_book_api_proto_rawDescGZIP(), []int{7}
}

// DuplicatePriceBookParams
type DuplicatePriceBookParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// name
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *DuplicatePriceBookParams) Reset() {
	*x = DuplicatePriceBookParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DuplicatePriceBookParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DuplicatePriceBookParams) ProtoMessage() {}

func (x *DuplicatePriceBookParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DuplicatePriceBookParams.ProtoReflect.Descriptor instead.
func (*DuplicatePriceBookParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_price_book_v1_price_book_api_proto_rawDescGZIP(), []int{8}
}

func (x *DuplicatePriceBookParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DuplicatePriceBookParams) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// DuplicatePriceBookResult
type DuplicatePriceBookResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// price book
	PriceBook *v1.PriceBook `protobuf:"bytes,1,opt,name=price_book,json=priceBook,proto3" json:"price_book,omitempty"`
}

func (x *DuplicatePriceBookResult) Reset() {
	*x = DuplicatePriceBookResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DuplicatePriceBookResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DuplicatePriceBookResult) ProtoMessage() {}

func (x *DuplicatePriceBookResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DuplicatePriceBookResult.ProtoReflect.Descriptor instead.
func (*DuplicatePriceBookResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_price_book_v1_price_book_api_proto_rawDescGZIP(), []int{9}
}

func (x *DuplicatePriceBookResult) GetPriceBook() *v1.PriceBook {
	if x != nil {
		return x.PriceBook
	}
	return nil
}

// SaveServiceCategoriesParams
type SaveServiceCategoriesParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// categories
	Categories []*v1.ServiceCategory `protobuf:"bytes,1,rep,name=categories,proto3" json:"categories,omitempty"`
	// service type
	ServiceType v11.ServiceType `protobuf:"varint,2,opt,name=service_type,json=serviceType,proto3,enum=moego.models.offering.v1.ServiceType" json:"service_type,omitempty"`
	// service item type
	ServiceItemType v11.ServiceItemType `protobuf:"varint,3,opt,name=service_item_type,json=serviceItemType,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_type,omitempty"`
}

func (x *SaveServiceCategoriesParams) Reset() {
	*x = SaveServiceCategoriesParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaveServiceCategoriesParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveServiceCategoriesParams) ProtoMessage() {}

func (x *SaveServiceCategoriesParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveServiceCategoriesParams.ProtoReflect.Descriptor instead.
func (*SaveServiceCategoriesParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_price_book_v1_price_book_api_proto_rawDescGZIP(), []int{10}
}

func (x *SaveServiceCategoriesParams) GetCategories() []*v1.ServiceCategory {
	if x != nil {
		return x.Categories
	}
	return nil
}

func (x *SaveServiceCategoriesParams) GetServiceType() v11.ServiceType {
	if x != nil {
		return x.ServiceType
	}
	return v11.ServiceType(0)
}

func (x *SaveServiceCategoriesParams) GetServiceItemType() v11.ServiceItemType {
	if x != nil {
		return x.ServiceItemType
	}
	return v11.ServiceItemType(0)
}

// SaveServiceCategoriesResult
type SaveServiceCategoriesResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SaveServiceCategoriesResult) Reset() {
	*x = SaveServiceCategoriesResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaveServiceCategoriesResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveServiceCategoriesResult) ProtoMessage() {}

func (x *SaveServiceCategoriesResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveServiceCategoriesResult.ProtoReflect.Descriptor instead.
func (*SaveServiceCategoriesResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_price_book_v1_price_book_api_proto_rawDescGZIP(), []int{11}
}

// ListServiceCategoriesParams
type ListServiceCategoriesParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// page
	Pagination *v2.PaginationRequest `protobuf:"bytes,1,opt,name=pagination,proto3" json:"pagination,omitempty"`
	// filter
	Filter *v12.ListServiceCategoriesRequest_Filter `protobuf:"bytes,2,opt,name=filter,proto3" json:"filter,omitempty"`
}

func (x *ListServiceCategoriesParams) Reset() {
	*x = ListServiceCategoriesParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListServiceCategoriesParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListServiceCategoriesParams) ProtoMessage() {}

func (x *ListServiceCategoriesParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListServiceCategoriesParams.ProtoReflect.Descriptor instead.
func (*ListServiceCategoriesParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_price_book_v1_price_book_api_proto_rawDescGZIP(), []int{12}
}

func (x *ListServiceCategoriesParams) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListServiceCategoriesParams) GetFilter() *v12.ListServiceCategoriesRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

// ListServiceCategoriesResult
type ListServiceCategoriesResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service categories
	ServiceCategories []*v1.ServiceCategory `protobuf:"bytes,1,rep,name=service_categories,json=serviceCategories,proto3" json:"service_categories,omitempty"`
}

func (x *ListServiceCategoriesResult) Reset() {
	*x = ListServiceCategoriesResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListServiceCategoriesResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListServiceCategoriesResult) ProtoMessage() {}

func (x *ListServiceCategoriesResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListServiceCategoriesResult.ProtoReflect.Descriptor instead.
func (*ListServiceCategoriesResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_price_book_v1_price_book_api_proto_rawDescGZIP(), []int{13}
}

func (x *ListServiceCategoriesResult) GetServiceCategories() []*v1.ServiceCategory {
	if x != nil {
		return x.ServiceCategories
	}
	return nil
}

// ListPetBreedsParams
type ListPetBreedsParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ListPetBreedsParams) Reset() {
	*x = ListPetBreedsParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPetBreedsParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPetBreedsParams) ProtoMessage() {}

func (x *ListPetBreedsParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPetBreedsParams.ProtoReflect.Descriptor instead.
func (*ListPetBreedsParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_price_book_v1_price_book_api_proto_rawDescGZIP(), []int{14}
}

// ListPetBreedsResult {
type ListPetBreedsResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet breeds
	PetBreeds []*v1.PetBreed `protobuf:"bytes,1,rep,name=pet_breeds,json=petBreeds,proto3" json:"pet_breeds,omitempty"`
}

func (x *ListPetBreedsResult) Reset() {
	*x = ListPetBreedsResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPetBreedsResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPetBreedsResult) ProtoMessage() {}

func (x *ListPetBreedsResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPetBreedsResult.ProtoReflect.Descriptor instead.
func (*ListPetBreedsResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_price_book_v1_price_book_api_proto_rawDescGZIP(), []int{15}
}

func (x *ListPetBreedsResult) GetPetBreeds() []*v1.PetBreed {
	if x != nil {
		return x.PetBreeds
	}
	return nil
}

// list pet types params
type ListPetTypesParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ListPetTypesParams) Reset() {
	*x = ListPetTypesParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPetTypesParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPetTypesParams) ProtoMessage() {}

func (x *ListPetTypesParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPetTypesParams.ProtoReflect.Descriptor instead.
func (*ListPetTypesParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_price_book_v1_price_book_api_proto_rawDescGZIP(), []int{16}
}

// list pet types result
type ListPetTypesResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pet type list
	PetTypes []*v1.PetType `protobuf:"bytes,1,rep,name=pet_types,json=petTypes,proto3" json:"pet_types,omitempty"`
}

func (x *ListPetTypesResult) Reset() {
	*x = ListPetTypesResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPetTypesResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPetTypesResult) ProtoMessage() {}

func (x *ListPetTypesResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPetTypesResult.ProtoReflect.Descriptor instead.
func (*ListPetTypesResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_price_book_v1_price_book_api_proto_rawDescGZIP(), []int{17}
}

func (x *ListPetTypesResult) GetPetTypes() []*v1.PetType {
	if x != nil {
		return x.PetTypes
	}
	return nil
}

// CreateServiceParams
type CreateServiceParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// price book
	PriceBook *v1.PriceBook `protobuf:"bytes,1,opt,name=price_book,json=priceBook,proto3" json:"price_book,omitempty"`
	// name
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// service item type
	ServiceItemType v11.ServiceItemType `protobuf:"varint,3,opt,name=service_item_type,json=serviceItemType,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_type,omitempty"`
	// category
	Category *v1.ServiceCategory `protobuf:"bytes,4,opt,name=category,proto3" json:"category,omitempty"`
	// description
	Description string `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"`
	// inactive
	Inactive bool `protobuf:"varint,6,opt,name=inactive,proto3" json:"inactive,omitempty"`
	// color
	Color string `protobuf:"bytes,7,opt,name=color,proto3" json:"color,omitempty"`
	// sort
	Sort int64 `protobuf:"varint,8,opt,name=sort,proto3" json:"sort,omitempty"`
	// price
	Price *money.Money `protobuf:"bytes,9,opt,name=price,proto3" json:"price,omitempty"`
	// service price unit
	ServicePriceUnit v11.ServicePriceUnit `protobuf:"varint,10,opt,name=service_price_unit,json=servicePriceUnit,proto3,enum=moego.models.offering.v1.ServicePriceUnit" json:"service_price_unit,omitempty"`
	// 万分位税率
	TaxRate int64 `protobuf:"varint,11,opt,name=tax_rate,json=taxRate,proto3" json:"tax_rate,omitempty"`
	// duration
	Duration *durationpb.Duration `protobuf:"bytes,12,opt,name=duration,proto3" json:"duration,omitempty"`
	// max duration
	MaxDuration *durationpb.Duration `protobuf:"bytes,13,opt,name=max_duration,json=maxDuration,proto3" json:"max_duration,omitempty"`
	// limitation
	Limitation *v1.Service_Limitation `protobuf:"bytes,14,opt,name=limitation,proto3" json:"limitation,omitempty"`
	// service type
	ServiceType v11.ServiceType `protobuf:"varint,15,opt,name=service_type,json=serviceType,proto3,enum=moego.models.offering.v1.ServiceType" json:"service_type,omitempty"`
	// images
	Images []string `protobuf:"bytes,16,rep,name=images,proto3" json:"images,omitempty"`
	// require_dedicated_staff, only for add-on
	RequireDedicatedStaff bool `protobuf:"varint,17,opt,name=require_dedicated_staff,json=requireDedicatedStaff,proto3" json:"require_dedicated_staff,omitempty"`
	// auto rule
	AutoRule *v1.Service_AutoRule `protobuf:"bytes,18,opt,name=auto_rule,json=autoRule,proto3" json:"auto_rule,omitempty"`
}

func (x *CreateServiceParams) Reset() {
	*x = CreateServiceParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateServiceParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateServiceParams) ProtoMessage() {}

func (x *CreateServiceParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateServiceParams.ProtoReflect.Descriptor instead.
func (*CreateServiceParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_price_book_v1_price_book_api_proto_rawDescGZIP(), []int{18}
}

func (x *CreateServiceParams) GetPriceBook() *v1.PriceBook {
	if x != nil {
		return x.PriceBook
	}
	return nil
}

func (x *CreateServiceParams) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateServiceParams) GetServiceItemType() v11.ServiceItemType {
	if x != nil {
		return x.ServiceItemType
	}
	return v11.ServiceItemType(0)
}

func (x *CreateServiceParams) GetCategory() *v1.ServiceCategory {
	if x != nil {
		return x.Category
	}
	return nil
}

func (x *CreateServiceParams) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CreateServiceParams) GetInactive() bool {
	if x != nil {
		return x.Inactive
	}
	return false
}

func (x *CreateServiceParams) GetColor() string {
	if x != nil {
		return x.Color
	}
	return ""
}

func (x *CreateServiceParams) GetSort() int64 {
	if x != nil {
		return x.Sort
	}
	return 0
}

func (x *CreateServiceParams) GetPrice() *money.Money {
	if x != nil {
		return x.Price
	}
	return nil
}

func (x *CreateServiceParams) GetServicePriceUnit() v11.ServicePriceUnit {
	if x != nil {
		return x.ServicePriceUnit
	}
	return v11.ServicePriceUnit(0)
}

func (x *CreateServiceParams) GetTaxRate() int64 {
	if x != nil {
		return x.TaxRate
	}
	return 0
}

func (x *CreateServiceParams) GetDuration() *durationpb.Duration {
	if x != nil {
		return x.Duration
	}
	return nil
}

func (x *CreateServiceParams) GetMaxDuration() *durationpb.Duration {
	if x != nil {
		return x.MaxDuration
	}
	return nil
}

func (x *CreateServiceParams) GetLimitation() *v1.Service_Limitation {
	if x != nil {
		return x.Limitation
	}
	return nil
}

func (x *CreateServiceParams) GetServiceType() v11.ServiceType {
	if x != nil {
		return x.ServiceType
	}
	return v11.ServiceType(0)
}

func (x *CreateServiceParams) GetImages() []string {
	if x != nil {
		return x.Images
	}
	return nil
}

func (x *CreateServiceParams) GetRequireDedicatedStaff() bool {
	if x != nil {
		return x.RequireDedicatedStaff
	}
	return false
}

func (x *CreateServiceParams) GetAutoRule() *v1.Service_AutoRule {
	if x != nil {
		return x.AutoRule
	}
	return nil
}

// CreateServiceResult
type CreateServiceResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service
	Service *v1.Service `protobuf:"bytes,1,opt,name=service,proto3" json:"service,omitempty"`
}

func (x *CreateServiceResult) Reset() {
	*x = CreateServiceResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateServiceResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateServiceResult) ProtoMessage() {}

func (x *CreateServiceResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateServiceResult.ProtoReflect.Descriptor instead.
func (*CreateServiceResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_price_book_v1_price_book_api_proto_rawDescGZIP(), []int{19}
}

func (x *CreateServiceResult) GetService() *v1.Service {
	if x != nil {
		return x.Service
	}
	return nil
}

// GetServiceParams
type GetServiceParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *GetServiceParams) Reset() {
	*x = GetServiceParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetServiceParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetServiceParams) ProtoMessage() {}

func (x *GetServiceParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetServiceParams.ProtoReflect.Descriptor instead.
func (*GetServiceParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_price_book_v1_price_book_api_proto_rawDescGZIP(), []int{20}
}

func (x *GetServiceParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// GetServiceResult
type GetServiceResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service
	Service *v1.Service `protobuf:"bytes,1,opt,name=service,proto3" json:"service,omitempty"`
}

func (x *GetServiceResult) Reset() {
	*x = GetServiceResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetServiceResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetServiceResult) ProtoMessage() {}

func (x *GetServiceResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetServiceResult.ProtoReflect.Descriptor instead.
func (*GetServiceResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_price_book_v1_price_book_api_proto_rawDescGZIP(), []int{21}
}

func (x *GetServiceResult) GetService() *v1.Service {
	if x != nil {
		return x.Service
	}
	return nil
}

// ListServicesParams
type ListServicesParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// page
	Pagination *v2.PaginationRequest `protobuf:"bytes,1,opt,name=pagination,proto3" json:"pagination,omitempty"`
	// filter
	Filter *v12.ListServicesRequest_Filter `protobuf:"bytes,2,opt,name=filter,proto3" json:"filter,omitempty"`
}

func (x *ListServicesParams) Reset() {
	*x = ListServicesParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListServicesParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListServicesParams) ProtoMessage() {}

func (x *ListServicesParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListServicesParams.ProtoReflect.Descriptor instead.
func (*ListServicesParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_price_book_v1_price_book_api_proto_rawDescGZIP(), []int{22}
}

func (x *ListServicesParams) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListServicesParams) GetFilter() *v12.ListServicesRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

// ListServicesResult
type ListServicesResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// page
	Pagination *v2.PaginationResponse `protobuf:"bytes,1,opt,name=pagination,proto3" json:"pagination,omitempty"`
	// services
	Services []*v1.Service `protobuf:"bytes,2,rep,name=services,proto3" json:"services,omitempty"`
}

func (x *ListServicesResult) Reset() {
	*x = ListServicesResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListServicesResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListServicesResult) ProtoMessage() {}

func (x *ListServicesResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListServicesResult.ProtoReflect.Descriptor instead.
func (*ListServicesResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_price_book_v1_price_book_api_proto_rawDescGZIP(), []int{23}
}

func (x *ListServicesResult) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListServicesResult) GetServices() []*v1.Service {
	if x != nil {
		return x.Services
	}
	return nil
}

// UpdateServiceParams
type UpdateServiceParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// name
	Name *string `protobuf:"bytes,2,opt,name=name,proto3,oneof" json:"name,omitempty"`
	// category id
	ServiceCategory *v1.ServiceCategory `protobuf:"bytes,3,opt,name=service_category,json=serviceCategory,proto3" json:"service_category,omitempty"`
	// description
	Description *string `protobuf:"bytes,4,opt,name=description,proto3,oneof" json:"description,omitempty"`
	// inactive
	Inactive bool `protobuf:"varint,5,opt,name=inactive,proto3" json:"inactive,omitempty"`
	// color
	Color *string `protobuf:"bytes,6,opt,name=color,proto3,oneof" json:"color,omitempty"`
	// price
	Price *money.Money `protobuf:"bytes,7,opt,name=price,proto3" json:"price,omitempty"`
	// service price unit
	ServicePriceUnit v11.ServicePriceUnit `protobuf:"varint,8,opt,name=service_price_unit,json=servicePriceUnit,proto3,enum=moego.models.offering.v1.ServicePriceUnit" json:"service_price_unit,omitempty"`
	// 万分位税率
	TaxRate *int64 `protobuf:"varint,9,opt,name=tax_rate,json=taxRate,proto3,oneof" json:"tax_rate,omitempty"`
	// duration
	Duration *durationpb.Duration `protobuf:"bytes,10,opt,name=duration,proto3" json:"duration,omitempty"`
	// max duration
	MaxDuration *durationpb.Duration `protobuf:"bytes,11,opt,name=max_duration,json=maxDuration,proto3" json:"max_duration,omitempty"`
	// limitation
	Limitation *v1.Service_Limitation `protobuf:"bytes,12,opt,name=limitation,proto3" json:"limitation,omitempty"`
	// images
	Images []string `protobuf:"bytes,13,rep,name=images,proto3" json:"images,omitempty"`
	// require_dedicated_staff, only for add-on
	RequireDedicatedStaff *bool `protobuf:"varint,14,opt,name=require_dedicated_staff,json=requireDedicatedStaff,proto3,oneof" json:"require_dedicated_staff,omitempty"`
	// auto rule
	AutoRule *v1.Service_AutoRule `protobuf:"bytes,15,opt,name=auto_rule,json=autoRule,proto3" json:"auto_rule,omitempty"`
}

func (x *UpdateServiceParams) Reset() {
	*x = UpdateServiceParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateServiceParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateServiceParams) ProtoMessage() {}

func (x *UpdateServiceParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateServiceParams.ProtoReflect.Descriptor instead.
func (*UpdateServiceParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_price_book_v1_price_book_api_proto_rawDescGZIP(), []int{24}
}

func (x *UpdateServiceParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateServiceParams) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *UpdateServiceParams) GetServiceCategory() *v1.ServiceCategory {
	if x != nil {
		return x.ServiceCategory
	}
	return nil
}

func (x *UpdateServiceParams) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *UpdateServiceParams) GetInactive() bool {
	if x != nil {
		return x.Inactive
	}
	return false
}

func (x *UpdateServiceParams) GetColor() string {
	if x != nil && x.Color != nil {
		return *x.Color
	}
	return ""
}

func (x *UpdateServiceParams) GetPrice() *money.Money {
	if x != nil {
		return x.Price
	}
	return nil
}

func (x *UpdateServiceParams) GetServicePriceUnit() v11.ServicePriceUnit {
	if x != nil {
		return x.ServicePriceUnit
	}
	return v11.ServicePriceUnit(0)
}

func (x *UpdateServiceParams) GetTaxRate() int64 {
	if x != nil && x.TaxRate != nil {
		return *x.TaxRate
	}
	return 0
}

func (x *UpdateServiceParams) GetDuration() *durationpb.Duration {
	if x != nil {
		return x.Duration
	}
	return nil
}

func (x *UpdateServiceParams) GetMaxDuration() *durationpb.Duration {
	if x != nil {
		return x.MaxDuration
	}
	return nil
}

func (x *UpdateServiceParams) GetLimitation() *v1.Service_Limitation {
	if x != nil {
		return x.Limitation
	}
	return nil
}

func (x *UpdateServiceParams) GetImages() []string {
	if x != nil {
		return x.Images
	}
	return nil
}

func (x *UpdateServiceParams) GetRequireDedicatedStaff() bool {
	if x != nil && x.RequireDedicatedStaff != nil {
		return *x.RequireDedicatedStaff
	}
	return false
}

func (x *UpdateServiceParams) GetAutoRule() *v1.Service_AutoRule {
	if x != nil {
		return x.AutoRule
	}
	return nil
}

// UpdateServiceResult
type UpdateServiceResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service
	Service *v1.Service `protobuf:"bytes,1,opt,name=service,proto3" json:"service,omitempty"`
}

func (x *UpdateServiceResult) Reset() {
	*x = UpdateServiceResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateServiceResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateServiceResult) ProtoMessage() {}

func (x *UpdateServiceResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateServiceResult.ProtoReflect.Descriptor instead.
func (*UpdateServiceResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_price_book_v1_price_book_api_proto_rawDescGZIP(), []int{25}
}

func (x *UpdateServiceResult) GetService() *v1.Service {
	if x != nil {
		return x.Service
	}
	return nil
}

// DeleteServiceParams
type DeleteServiceParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *DeleteServiceParams) Reset() {
	*x = DeleteServiceParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteServiceParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteServiceParams) ProtoMessage() {}

func (x *DeleteServiceParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteServiceParams.ProtoReflect.Descriptor instead.
func (*DeleteServiceParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_price_book_v1_price_book_api_proto_rawDescGZIP(), []int{26}
}

func (x *DeleteServiceParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// DeleteServiceResult
type DeleteServiceResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteServiceResult) Reset() {
	*x = DeleteServiceResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteServiceResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteServiceResult) ProtoMessage() {}

func (x *DeleteServiceResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteServiceResult.ProtoReflect.Descriptor instead.
func (*DeleteServiceResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_price_book_v1_price_book_api_proto_rawDescGZIP(), []int{27}
}

// SortServicesParams
type SortServicesParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// sorted services
	ServiceCategorySorts []*v12.SortServicesRequest_ServiceCategorySort `protobuf:"bytes,1,rep,name=service_category_sorts,json=serviceCategorySorts,proto3" json:"service_category_sorts,omitempty"`
}

func (x *SortServicesParams) Reset() {
	*x = SortServicesParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SortServicesParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SortServicesParams) ProtoMessage() {}

func (x *SortServicesParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SortServicesParams.ProtoReflect.Descriptor instead.
func (*SortServicesParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_price_book_v1_price_book_api_proto_rawDescGZIP(), []int{28}
}

func (x *SortServicesParams) GetServiceCategorySorts() []*v12.SortServicesRequest_ServiceCategorySort {
	if x != nil {
		return x.ServiceCategorySorts
	}
	return nil
}

// SortServicesResult
type SortServicesResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SortServicesResult) Reset() {
	*x = SortServicesResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SortServicesResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SortServicesResult) ProtoMessage() {}

func (x *SortServicesResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SortServicesResult.ProtoReflect.Descriptor instead.
func (*SortServicesResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_price_book_v1_price_book_api_proto_rawDescGZIP(), []int{29}
}

// ListServiceChangeHistoriesParams
type ListServiceChangeHistoriesParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// page
	Pagination *v2.PaginationRequest `protobuf:"bytes,1,opt,name=pagination,proto3" json:"pagination,omitempty"`
	// filter
	Filter *v12.ListServiceChangeHistoriesRequest_Filter `protobuf:"bytes,2,opt,name=filter,proto3" json:"filter,omitempty"`
	// order by
	OrderBy *v12.ListServiceChangeHistoriesRequest_OrderBy `protobuf:"bytes,3,opt,name=order_by,json=orderBy,proto3,oneof" json:"order_by,omitempty"`
}

func (x *ListServiceChangeHistoriesParams) Reset() {
	*x = ListServiceChangeHistoriesParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListServiceChangeHistoriesParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListServiceChangeHistoriesParams) ProtoMessage() {}

func (x *ListServiceChangeHistoriesParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListServiceChangeHistoriesParams.ProtoReflect.Descriptor instead.
func (*ListServiceChangeHistoriesParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_price_book_v1_price_book_api_proto_rawDescGZIP(), []int{30}
}

func (x *ListServiceChangeHistoriesParams) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListServiceChangeHistoriesParams) GetFilter() *v12.ListServiceChangeHistoriesRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ListServiceChangeHistoriesParams) GetOrderBy() *v12.ListServiceChangeHistoriesRequest_OrderBy {
	if x != nil {
		return x.OrderBy
	}
	return nil
}

// ListServiceChangeHistoriesResult
type ListServiceChangeHistoriesResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// page
	Pagination *v2.PaginationResponse `protobuf:"bytes,1,opt,name=pagination,proto3" json:"pagination,omitempty"`
	// service change history
	ServiceChangeHistories []*v1.ServiceChangeHistory `protobuf:"bytes,2,rep,name=service_change_histories,json=serviceChangeHistories,proto3" json:"service_change_histories,omitempty"`
}

func (x *ListServiceChangeHistoriesResult) Reset() {
	*x = ListServiceChangeHistoriesResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListServiceChangeHistoriesResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListServiceChangeHistoriesResult) ProtoMessage() {}

func (x *ListServiceChangeHistoriesResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListServiceChangeHistoriesResult.ProtoReflect.Descriptor instead.
func (*ListServiceChangeHistoriesResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_price_book_v1_price_book_api_proto_rawDescGZIP(), []int{31}
}

func (x *ListServiceChangeHistoriesResult) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListServiceChangeHistoriesResult) GetServiceChangeHistories() []*v1.ServiceChangeHistory {
	if x != nil {
		return x.ServiceChangeHistories
	}
	return nil
}

// ListServiceChangesParams
type ListServiceChangesParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// page
	Pagination *v2.PaginationRequest `protobuf:"bytes,1,opt,name=pagination,proto3" json:"pagination,omitempty"`
	// filter
	Filter *v12.ListServiceChangesRequest_Filter `protobuf:"bytes,2,opt,name=filter,proto3" json:"filter,omitempty"`
}

func (x *ListServiceChangesParams) Reset() {
	*x = ListServiceChangesParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListServiceChangesParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListServiceChangesParams) ProtoMessage() {}

func (x *ListServiceChangesParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListServiceChangesParams.ProtoReflect.Descriptor instead.
func (*ListServiceChangesParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_price_book_v1_price_book_api_proto_rawDescGZIP(), []int{32}
}

func (x *ListServiceChangesParams) GetPagination() *v2.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListServiceChangesParams) GetFilter() *v12.ListServiceChangesRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

// ListServiceChangesResult
type ListServiceChangesResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// page
	Pagination *v2.PaginationResponse `protobuf:"bytes,1,opt,name=pagination,proto3" json:"pagination,omitempty"`
	// service changes
	ServiceChanges []*v1.ServiceChange `protobuf:"bytes,2,rep,name=service_changes,json=serviceChanges,proto3" json:"service_changes,omitempty"`
}

func (x *ListServiceChangesResult) Reset() {
	*x = ListServiceChangesResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListServiceChangesResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListServiceChangesResult) ProtoMessage() {}

func (x *ListServiceChangesResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListServiceChangesResult.ProtoReflect.Descriptor instead.
func (*ListServiceChangesResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_price_book_v1_price_book_api_proto_rawDescGZIP(), []int{33}
}

func (x *ListServiceChangesResult) GetPagination() *v2.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListServiceChangesResult) GetServiceChanges() []*v1.ServiceChange {
	if x != nil {
		return x.ServiceChanges
	}
	return nil
}

// PushServiceChangesParams
type PushServiceChangesParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service ids
	ServiceIds []int64 `protobuf:"varint,1,rep,packed,name=service_ids,json=serviceIds,proto3" json:"service_ids,omitempty"`
	// targets
	Targets []*v1.TenantObject `protobuf:"bytes,2,rep,name=targets,proto3" json:"targets,omitempty"`
	// effective date
	EffectiveDate *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=effective_date,json=effectiveDate,proto3" json:"effective_date,omitempty"`
	// apply to booked services
	ApplyToBookedServices bool `protobuf:"varint,4,opt,name=apply_to_booked_services,json=applyToBookedServices,proto3" json:"apply_to_booked_services,omitempty"`
}

func (x *PushServiceChangesParams) Reset() {
	*x = PushServiceChangesParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PushServiceChangesParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushServiceChangesParams) ProtoMessage() {}

func (x *PushServiceChangesParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushServiceChangesParams.ProtoReflect.Descriptor instead.
func (*PushServiceChangesParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_price_book_v1_price_book_api_proto_rawDescGZIP(), []int{34}
}

func (x *PushServiceChangesParams) GetServiceIds() []int64 {
	if x != nil {
		return x.ServiceIds
	}
	return nil
}

func (x *PushServiceChangesParams) GetTargets() []*v1.TenantObject {
	if x != nil {
		return x.Targets
	}
	return nil
}

func (x *PushServiceChangesParams) GetEffectiveDate() *timestamppb.Timestamp {
	if x != nil {
		return x.EffectiveDate
	}
	return nil
}

func (x *PushServiceChangesParams) GetApplyToBookedServices() bool {
	if x != nil {
		return x.ApplyToBookedServices
	}
	return false
}

// PushServiceChangesResult
type PushServiceChangesResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// success company ids
	SuccessCompanyIds []int64 `protobuf:"varint,1,rep,packed,name=success_company_ids,json=successCompanyIds,proto3" json:"success_company_ids,omitempty"`
	// failed company ids
	FailedCompanyIds []int64 `protobuf:"varint,2,rep,packed,name=failed_company_ids,json=failedCompanyIds,proto3" json:"failed_company_ids,omitempty"`
}

func (x *PushServiceChangesResult) Reset() {
	*x = PushServiceChangesResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PushServiceChangesResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushServiceChangesResult) ProtoMessage() {}

func (x *PushServiceChangesResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushServiceChangesResult.ProtoReflect.Descriptor instead.
func (*PushServiceChangesResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_price_book_v1_price_book_api_proto_rawDescGZIP(), []int{35}
}

func (x *PushServiceChangesResult) GetSuccessCompanyIds() []int64 {
	if x != nil {
		return x.SuccessCompanyIds
	}
	return nil
}

func (x *PushServiceChangesResult) GetFailedCompanyIds() []int64 {
	if x != nil {
		return x.FailedCompanyIds
	}
	return nil
}

// create evaluation params
type CreateEvaluationParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// evaluation
	EvaluationDef *v1.CreateEvaluationDef `protobuf:"bytes,1,opt,name=evaluation_def,json=evaluationDef,proto3" json:"evaluation_def,omitempty"`
}

func (x *CreateEvaluationParams) Reset() {
	*x = CreateEvaluationParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateEvaluationParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateEvaluationParams) ProtoMessage() {}

func (x *CreateEvaluationParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateEvaluationParams.ProtoReflect.Descriptor instead.
func (*CreateEvaluationParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_price_book_v1_price_book_api_proto_rawDescGZIP(), []int{36}
}

func (x *CreateEvaluationParams) GetEvaluationDef() *v1.CreateEvaluationDef {
	if x != nil {
		return x.EvaluationDef
	}
	return nil
}

// CreateEvaluationResult
type CreateEvaluationResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// evaluation
	Evaluation *v1.Evaluation `protobuf:"bytes,1,opt,name=evaluation,proto3" json:"evaluation,omitempty"`
}

func (x *CreateEvaluationResult) Reset() {
	*x = CreateEvaluationResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateEvaluationResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateEvaluationResult) ProtoMessage() {}

func (x *CreateEvaluationResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateEvaluationResult.ProtoReflect.Descriptor instead.
func (*CreateEvaluationResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_price_book_v1_price_book_api_proto_rawDescGZIP(), []int{37}
}

func (x *CreateEvaluationResult) GetEvaluation() *v1.Evaluation {
	if x != nil {
		return x.Evaluation
	}
	return nil
}

// UpdateEvaluationParams
type UpdateEvaluationParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// evaluation
	EvaluationDef *v1.UpdateEvaluationDef `protobuf:"bytes,2,opt,name=evaluation_def,json=evaluationDef,proto3" json:"evaluation_def,omitempty"`
}

func (x *UpdateEvaluationParams) Reset() {
	*x = UpdateEvaluationParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateEvaluationParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateEvaluationParams) ProtoMessage() {}

func (x *UpdateEvaluationParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateEvaluationParams.ProtoReflect.Descriptor instead.
func (*UpdateEvaluationParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_price_book_v1_price_book_api_proto_rawDescGZIP(), []int{38}
}

func (x *UpdateEvaluationParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateEvaluationParams) GetEvaluationDef() *v1.UpdateEvaluationDef {
	if x != nil {
		return x.EvaluationDef
	}
	return nil
}

// UpdateEvaluationResult
type UpdateEvaluationResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// evaluation
	Evaluation *v1.Evaluation `protobuf:"bytes,1,opt,name=evaluation,proto3" json:"evaluation,omitempty"`
}

func (x *UpdateEvaluationResult) Reset() {
	*x = UpdateEvaluationResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateEvaluationResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateEvaluationResult) ProtoMessage() {}

func (x *UpdateEvaluationResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateEvaluationResult.ProtoReflect.Descriptor instead.
func (*UpdateEvaluationResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_price_book_v1_price_book_api_proto_rawDescGZIP(), []int{39}
}

func (x *UpdateEvaluationResult) GetEvaluation() *v1.Evaluation {
	if x != nil {
		return x.Evaluation
	}
	return nil
}

// ListEvaluationsParams
type ListEvaluationsParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// filter
	Filter *v12.ListEvaluationsRequest_Filter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
}

func (x *ListEvaluationsParams) Reset() {
	*x = ListEvaluationsParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListEvaluationsParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListEvaluationsParams) ProtoMessage() {}

func (x *ListEvaluationsParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListEvaluationsParams.ProtoReflect.Descriptor instead.
func (*ListEvaluationsParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_price_book_v1_price_book_api_proto_rawDescGZIP(), []int{40}
}

func (x *ListEvaluationsParams) GetFilter() *v12.ListEvaluationsRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

// ListEvaluationsResult
type ListEvaluationsResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// evaluations
	Evaluations []*v1.Evaluation `protobuf:"bytes,1,rep,name=evaluations,proto3" json:"evaluations,omitempty"`
}

func (x *ListEvaluationsResult) Reset() {
	*x = ListEvaluationsResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListEvaluationsResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListEvaluationsResult) ProtoMessage() {}

func (x *ListEvaluationsResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListEvaluationsResult.ProtoReflect.Descriptor instead.
func (*ListEvaluationsResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_price_book_v1_price_book_api_proto_rawDescGZIP(), []int{41}
}

func (x *ListEvaluationsResult) GetEvaluations() []*v1.Evaluation {
	if x != nil {
		return x.Evaluations
	}
	return nil
}

// PushEvaluationChangesParams
type PushEvaluationChangesParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Evaluation ids
	EvaluationIds []int64 `protobuf:"varint,1,rep,packed,name=evaluation_ids,json=evaluationIds,proto3" json:"evaluation_ids,omitempty"`
	// targets
	Targets []*v1.TenantObject `protobuf:"bytes,2,rep,name=targets,proto3" json:"targets,omitempty"`
}

func (x *PushEvaluationChangesParams) Reset() {
	*x = PushEvaluationChangesParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PushEvaluationChangesParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushEvaluationChangesParams) ProtoMessage() {}

func (x *PushEvaluationChangesParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushEvaluationChangesParams.ProtoReflect.Descriptor instead.
func (*PushEvaluationChangesParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_price_book_v1_price_book_api_proto_rawDescGZIP(), []int{42}
}

func (x *PushEvaluationChangesParams) GetEvaluationIds() []int64 {
	if x != nil {
		return x.EvaluationIds
	}
	return nil
}

func (x *PushEvaluationChangesParams) GetTargets() []*v1.TenantObject {
	if x != nil {
		return x.Targets
	}
	return nil
}

// PushEvaluationChangesResult
type PushEvaluationChangesResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// success company ids
	SuccessCompanyIds []int64 `protobuf:"varint,1,rep,packed,name=success_company_ids,json=successCompanyIds,proto3" json:"success_company_ids,omitempty"`
	// failed company ids
	FailedCompanyIds []int64 `protobuf:"varint,2,rep,packed,name=failed_company_ids,json=failedCompanyIds,proto3" json:"failed_company_ids,omitempty"`
}

func (x *PushEvaluationChangesResult) Reset() {
	*x = PushEvaluationChangesResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PushEvaluationChangesResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushEvaluationChangesResult) ProtoMessage() {}

func (x *PushEvaluationChangesResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushEvaluationChangesResult.ProtoReflect.Descriptor instead.
func (*PushEvaluationChangesResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_price_book_v1_price_book_api_proto_rawDescGZIP(), []int{43}
}

func (x *PushEvaluationChangesResult) GetSuccessCompanyIds() []int64 {
	if x != nil {
		return x.SuccessCompanyIds
	}
	return nil
}

func (x *PushEvaluationChangesResult) GetFailedCompanyIds() []int64 {
	if x != nil {
		return x.FailedCompanyIds
	}
	return nil
}

// SortEvaluationsParams
type SortEvaluationsParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// evaluation ids
	Ids []int64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
}

func (x *SortEvaluationsParams) Reset() {
	*x = SortEvaluationsParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SortEvaluationsParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SortEvaluationsParams) ProtoMessage() {}

func (x *SortEvaluationsParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SortEvaluationsParams.ProtoReflect.Descriptor instead.
func (*SortEvaluationsParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_price_book_v1_price_book_api_proto_rawDescGZIP(), []int{44}
}

func (x *SortEvaluationsParams) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

// SortEvaluationsResult
type SortEvaluationsResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SortEvaluationsResult) Reset() {
	*x = SortEvaluationsResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SortEvaluationsResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SortEvaluationsResult) ProtoMessage() {}

func (x *SortEvaluationsResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SortEvaluationsResult.ProtoReflect.Descriptor instead.
func (*SortEvaluationsResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_price_book_v1_price_book_api_proto_rawDescGZIP(), []int{45}
}

// CreatePricingRuleParams
type CreatePricingRuleParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pricing rule
	PricingRuleDef *v1.CreatePricingRuleDef `protobuf:"bytes,1,opt,name=pricing_rule_def,json=pricingRuleDef,proto3" json:"pricing_rule_def,omitempty"`
}

func (x *CreatePricingRuleParams) Reset() {
	*x = CreatePricingRuleParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreatePricingRuleParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePricingRuleParams) ProtoMessage() {}

func (x *CreatePricingRuleParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePricingRuleParams.ProtoReflect.Descriptor instead.
func (*CreatePricingRuleParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_price_book_v1_price_book_api_proto_rawDescGZIP(), []int{46}
}

func (x *CreatePricingRuleParams) GetPricingRuleDef() *v1.CreatePricingRuleDef {
	if x != nil {
		return x.PricingRuleDef
	}
	return nil
}

// CreatePricingRuleResult
type CreatePricingRuleResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pricing rule
	PricingRule *v1.PricingRule `protobuf:"bytes,1,opt,name=pricing_rule,json=pricingRule,proto3" json:"pricing_rule,omitempty"`
}

func (x *CreatePricingRuleResult) Reset() {
	*x = CreatePricingRuleResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreatePricingRuleResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePricingRuleResult) ProtoMessage() {}

func (x *CreatePricingRuleResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePricingRuleResult.ProtoReflect.Descriptor instead.
func (*CreatePricingRuleResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_price_book_v1_price_book_api_proto_rawDescGZIP(), []int{47}
}

func (x *CreatePricingRuleResult) GetPricingRule() *v1.PricingRule {
	if x != nil {
		return x.PricingRule
	}
	return nil
}

// UpdatePricingRuleParams
type UpdatePricingRuleParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// pricing rule
	PricingRuleDef *v1.UpdatePricingRuleDef `protobuf:"bytes,2,opt,name=pricing_rule_def,json=pricingRuleDef,proto3" json:"pricing_rule_def,omitempty"`
}

func (x *UpdatePricingRuleParams) Reset() {
	*x = UpdatePricingRuleParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdatePricingRuleParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePricingRuleParams) ProtoMessage() {}

func (x *UpdatePricingRuleParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePricingRuleParams.ProtoReflect.Descriptor instead.
func (*UpdatePricingRuleParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_price_book_v1_price_book_api_proto_rawDescGZIP(), []int{48}
}

func (x *UpdatePricingRuleParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdatePricingRuleParams) GetPricingRuleDef() *v1.UpdatePricingRuleDef {
	if x != nil {
		return x.PricingRuleDef
	}
	return nil
}

// UpdatePricingRuleResult
type UpdatePricingRuleResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pricing rule
	PricingRule *v1.PricingRule `protobuf:"bytes,1,opt,name=pricing_rule,json=pricingRule,proto3" json:"pricing_rule,omitempty"`
}

func (x *UpdatePricingRuleResult) Reset() {
	*x = UpdatePricingRuleResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdatePricingRuleResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePricingRuleResult) ProtoMessage() {}

func (x *UpdatePricingRuleResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePricingRuleResult.ProtoReflect.Descriptor instead.
func (*UpdatePricingRuleResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_price_book_v1_price_book_api_proto_rawDescGZIP(), []int{49}
}

func (x *UpdatePricingRuleResult) GetPricingRule() *v1.PricingRule {
	if x != nil {
		return x.PricingRule
	}
	return nil
}

// ListPricingRulesParams
type ListPricingRulesParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// filter
	Filter *v12.ListPricingRulesRequest_Filter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
}

func (x *ListPricingRulesParams) Reset() {
	*x = ListPricingRulesParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPricingRulesParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPricingRulesParams) ProtoMessage() {}

func (x *ListPricingRulesParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPricingRulesParams.ProtoReflect.Descriptor instead.
func (*ListPricingRulesParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_price_book_v1_price_book_api_proto_rawDescGZIP(), []int{50}
}

func (x *ListPricingRulesParams) GetFilter() *v12.ListPricingRulesRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

// ListPricingRulesResult
type ListPricingRulesResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pricing rules
	PricingRules []*v1.PricingRule `protobuf:"bytes,1,rep,name=pricing_rules,json=pricingRules,proto3" json:"pricing_rules,omitempty"`
}

func (x *ListPricingRulesResult) Reset() {
	*x = ListPricingRulesResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListPricingRulesResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPricingRulesResult) ProtoMessage() {}

func (x *ListPricingRulesResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPricingRulesResult.ProtoReflect.Descriptor instead.
func (*ListPricingRulesResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_price_book_v1_price_book_api_proto_rawDescGZIP(), []int{51}
}

func (x *ListPricingRulesResult) GetPricingRules() []*v1.PricingRule {
	if x != nil {
		return x.PricingRules
	}
	return nil
}

// SortPricingRulesParams
type SortPricingRulesParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ids
	Ids []int64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
}

func (x *SortPricingRulesParams) Reset() {
	*x = SortPricingRulesParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SortPricingRulesParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SortPricingRulesParams) ProtoMessage() {}

func (x *SortPricingRulesParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SortPricingRulesParams.ProtoReflect.Descriptor instead.
func (*SortPricingRulesParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_price_book_v1_price_book_api_proto_rawDescGZIP(), []int{52}
}

func (x *SortPricingRulesParams) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

// SortPricingRulesResult
type SortPricingRulesResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SortPricingRulesResult) Reset() {
	*x = SortPricingRulesResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SortPricingRulesResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SortPricingRulesResult) ProtoMessage() {}

func (x *SortPricingRulesResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SortPricingRulesResult.ProtoReflect.Descriptor instead.
func (*SortPricingRulesResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_price_book_v1_price_book_api_proto_rawDescGZIP(), []int{53}
}

// PushPricingRuleChangesParams
type PushPricingRuleChangesParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// pricing rule ids
	PricingRuleIds []int64 `protobuf:"varint,1,rep,packed,name=pricing_rule_ids,json=pricingRuleIds,proto3" json:"pricing_rule_ids,omitempty"`
	// targets
	Targets []*v1.TenantObject `protobuf:"bytes,2,rep,name=targets,proto3" json:"targets,omitempty"`
}

func (x *PushPricingRuleChangesParams) Reset() {
	*x = PushPricingRuleChangesParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PushPricingRuleChangesParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushPricingRuleChangesParams) ProtoMessage() {}

func (x *PushPricingRuleChangesParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushPricingRuleChangesParams.ProtoReflect.Descriptor instead.
func (*PushPricingRuleChangesParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_price_book_v1_price_book_api_proto_rawDescGZIP(), []int{54}
}

func (x *PushPricingRuleChangesParams) GetPricingRuleIds() []int64 {
	if x != nil {
		return x.PricingRuleIds
	}
	return nil
}

func (x *PushPricingRuleChangesParams) GetTargets() []*v1.TenantObject {
	if x != nil {
		return x.Targets
	}
	return nil
}

// PushPricingRuleChangesResult
type PushPricingRuleChangesResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// success company ids
	SuccessCompanyIds []int64 `protobuf:"varint,1,rep,packed,name=success_company_ids,json=successCompanyIds,proto3" json:"success_company_ids,omitempty"`
	// failed company ids
	FailedCompanyIds []int64 `protobuf:"varint,2,rep,packed,name=failed_company_ids,json=failedCompanyIds,proto3" json:"failed_company_ids,omitempty"`
}

func (x *PushPricingRuleChangesResult) Reset() {
	*x = PushPricingRuleChangesResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PushPricingRuleChangesResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushPricingRuleChangesResult) ProtoMessage() {}

func (x *PushPricingRuleChangesResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushPricingRuleChangesResult.ProtoReflect.Descriptor instead.
func (*PushPricingRuleChangesResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_price_book_v1_price_book_api_proto_rawDescGZIP(), []int{55}
}

func (x *PushPricingRuleChangesResult) GetSuccessCompanyIds() []int64 {
	if x != nil {
		return x.SuccessCompanyIds
	}
	return nil
}

func (x *PushPricingRuleChangesResult) GetFailedCompanyIds() []int64 {
	if x != nil {
		return x.FailedCompanyIds
	}
	return nil
}

// CreateServiceChargeParams
type CreateServiceChargeParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service charge
	ServiceChargeDef *v1.CreateServiceChargeDef `protobuf:"bytes,1,opt,name=service_charge_def,json=serviceChargeDef,proto3" json:"service_charge_def,omitempty"`
}

func (x *CreateServiceChargeParams) Reset() {
	*x = CreateServiceChargeParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateServiceChargeParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateServiceChargeParams) ProtoMessage() {}

func (x *CreateServiceChargeParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateServiceChargeParams.ProtoReflect.Descriptor instead.
func (*CreateServiceChargeParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_price_book_v1_price_book_api_proto_rawDescGZIP(), []int{56}
}

func (x *CreateServiceChargeParams) GetServiceChargeDef() *v1.CreateServiceChargeDef {
	if x != nil {
		return x.ServiceChargeDef
	}
	return nil
}

// CreateServiceChargeResult
type CreateServiceChargeResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service charge
	ServiceCharge *v1.ServiceCharge `protobuf:"bytes,1,opt,name=service_charge,json=serviceCharge,proto3" json:"service_charge,omitempty"`
}

func (x *CreateServiceChargeResult) Reset() {
	*x = CreateServiceChargeResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateServiceChargeResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateServiceChargeResult) ProtoMessage() {}

func (x *CreateServiceChargeResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateServiceChargeResult.ProtoReflect.Descriptor instead.
func (*CreateServiceChargeResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_price_book_v1_price_book_api_proto_rawDescGZIP(), []int{57}
}

func (x *CreateServiceChargeResult) GetServiceCharge() *v1.ServiceCharge {
	if x != nil {
		return x.ServiceCharge
	}
	return nil
}

// UpdateServiceChargeParams
type UpdateServiceChargeParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// service charge
	ServiceChargeDef *v1.UpdateServiceChargeDef `protobuf:"bytes,2,opt,name=service_charge_def,json=serviceChargeDef,proto3" json:"service_charge_def,omitempty"`
}

func (x *UpdateServiceChargeParams) Reset() {
	*x = UpdateServiceChargeParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateServiceChargeParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateServiceChargeParams) ProtoMessage() {}

func (x *UpdateServiceChargeParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateServiceChargeParams.ProtoReflect.Descriptor instead.
func (*UpdateServiceChargeParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_price_book_v1_price_book_api_proto_rawDescGZIP(), []int{58}
}

func (x *UpdateServiceChargeParams) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateServiceChargeParams) GetServiceChargeDef() *v1.UpdateServiceChargeDef {
	if x != nil {
		return x.ServiceChargeDef
	}
	return nil
}

// UpdateServiceChargeResult
type UpdateServiceChargeResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service charge
	ServiceCharge *v1.ServiceCharge `protobuf:"bytes,1,opt,name=service_charge,json=serviceCharge,proto3" json:"service_charge,omitempty"`
}

func (x *UpdateServiceChargeResult) Reset() {
	*x = UpdateServiceChargeResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateServiceChargeResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateServiceChargeResult) ProtoMessage() {}

func (x *UpdateServiceChargeResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateServiceChargeResult.ProtoReflect.Descriptor instead.
func (*UpdateServiceChargeResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_price_book_v1_price_book_api_proto_rawDescGZIP(), []int{59}
}

func (x *UpdateServiceChargeResult) GetServiceCharge() *v1.ServiceCharge {
	if x != nil {
		return x.ServiceCharge
	}
	return nil
}

// ListServiceChargesParams
type ListServiceChargesParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// filter
	Filter *v12.ListServiceChargesRequest_Filter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
}

func (x *ListServiceChargesParams) Reset() {
	*x = ListServiceChargesParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[60]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListServiceChargesParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListServiceChargesParams) ProtoMessage() {}

func (x *ListServiceChargesParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[60]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListServiceChargesParams.ProtoReflect.Descriptor instead.
func (*ListServiceChargesParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_price_book_v1_price_book_api_proto_rawDescGZIP(), []int{60}
}

func (x *ListServiceChargesParams) GetFilter() *v12.ListServiceChargesRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

// ListServiceChargesResult
type ListServiceChargesResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service charges
	ServiceCharges []*v1.ServiceCharge `protobuf:"bytes,1,rep,name=service_charges,json=serviceCharges,proto3" json:"service_charges,omitempty"`
}

func (x *ListServiceChargesResult) Reset() {
	*x = ListServiceChargesResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[61]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListServiceChargesResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListServiceChargesResult) ProtoMessage() {}

func (x *ListServiceChargesResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[61]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListServiceChargesResult.ProtoReflect.Descriptor instead.
func (*ListServiceChargesResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_price_book_v1_price_book_api_proto_rawDescGZIP(), []int{61}
}

func (x *ListServiceChargesResult) GetServiceCharges() []*v1.ServiceCharge {
	if x != nil {
		return x.ServiceCharges
	}
	return nil
}

// sort service charges params
type SortServiceChargesParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ids
	Ids []int64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
}

func (x *SortServiceChargesParams) Reset() {
	*x = SortServiceChargesParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[62]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SortServiceChargesParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SortServiceChargesParams) ProtoMessage() {}

func (x *SortServiceChargesParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[62]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SortServiceChargesParams.ProtoReflect.Descriptor instead.
func (*SortServiceChargesParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_price_book_v1_price_book_api_proto_rawDescGZIP(), []int{62}
}

func (x *SortServiceChargesParams) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

// SortServiceChargesResult
type SortServiceChargesResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SortServiceChargesResult) Reset() {
	*x = SortServiceChargesResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[63]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SortServiceChargesResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SortServiceChargesResult) ProtoMessage() {}

func (x *SortServiceChargesResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[63]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SortServiceChargesResult.ProtoReflect.Descriptor instead.
func (*SortServiceChargesResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_price_book_v1_price_book_api_proto_rawDescGZIP(), []int{63}
}

// PushServiceChargeChangesParams
type PushServiceChargeChangesParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// service charge ids
	ServiceChargeIds []int64 `protobuf:"varint,1,rep,packed,name=service_charge_ids,json=serviceChargeIds,proto3" json:"service_charge_ids,omitempty"`
	// targets
	Targets []*v1.TenantObject `protobuf:"bytes,2,rep,name=targets,proto3" json:"targets,omitempty"`
}

func (x *PushServiceChargeChangesParams) Reset() {
	*x = PushServiceChargeChangesParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[64]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PushServiceChargeChangesParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushServiceChargeChangesParams) ProtoMessage() {}

func (x *PushServiceChargeChangesParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[64]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushServiceChargeChangesParams.ProtoReflect.Descriptor instead.
func (*PushServiceChargeChangesParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_price_book_v1_price_book_api_proto_rawDescGZIP(), []int{64}
}

func (x *PushServiceChargeChangesParams) GetServiceChargeIds() []int64 {
	if x != nil {
		return x.ServiceChargeIds
	}
	return nil
}

func (x *PushServiceChargeChangesParams) GetTargets() []*v1.TenantObject {
	if x != nil {
		return x.Targets
	}
	return nil
}

// PushServiceChargeChangesResult
type PushServiceChargeChangesResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// success company ids
	SuccessCompanyIds []int64 `protobuf:"varint,1,rep,packed,name=success_company_ids,json=successCompanyIds,proto3" json:"success_company_ids,omitempty"`
	// failed company ids
	FailedCompanyIds []int64 `protobuf:"varint,2,rep,packed,name=failed_company_ids,json=failedCompanyIds,proto3" json:"failed_company_ids,omitempty"`
}

func (x *PushServiceChargeChangesResult) Reset() {
	*x = PushServiceChargeChangesResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[65]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PushServiceChargeChangesResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushServiceChargeChangesResult) ProtoMessage() {}

func (x *PushServiceChargeChangesResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[65]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushServiceChargeChangesResult.ProtoReflect.Descriptor instead.
func (*PushServiceChargeChangesResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_price_book_v1_price_book_api_proto_rawDescGZIP(), []int{65}
}

func (x *PushServiceChargeChangesResult) GetSuccessCompanyIds() []int64 {
	if x != nil {
		return x.SuccessCompanyIds
	}
	return nil
}

func (x *PushServiceChargeChangesResult) GetFailedCompanyIds() []int64 {
	if x != nil {
		return x.FailedCompanyIds
	}
	return nil
}

var File_moego_enterprise_price_book_v1_price_book_api_proto protoreflect.FileDescriptor

var file_moego_enterprise_price_book_v1_price_book_api_proto_rawDesc = []byte{
	0x0a, 0x33, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x2f, 0x76, 0x31,
	0x2f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x5f, 0x61, 0x70, 0x69, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x62, 0x6f,
	0x6f, 0x6b, 0x2e, 0x76, 0x31, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74,
	0x79, 0x70, 0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x32, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x72, 0x69, 0x63,
	0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x76, 0x31, 0x2f,
	0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x2b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x34, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x72,
	0x69, 0x63, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74,
	0x69, 0x6c, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x16, 0x0a, 0x14, 0x4c, 0x69, 0x73,
	0x74, 0x50, 0x72, 0x69, 0x63, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x22, 0x5e, 0x0a, 0x14, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x72, 0x69, 0x63, 0x65, 0x42, 0x6f,
	0x6f, 0x6b, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x46, 0x0a, 0x0b, 0x70, 0x72, 0x69,
	0x63, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x69, 0x63,
	0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x52, 0x0a, 0x70, 0x72, 0x69, 0x63, 0x65, 0x42, 0x6f, 0x6f, 0x6b,
	0x73, 0x22, 0x36, 0x0a, 0x15, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x72, 0x69, 0x63, 0x65,
	0x42, 0x6f, 0x6f, 0x6b, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x1d, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10,
	0x01, 0x18, 0x64, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x5d, 0x0a, 0x15, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x12, 0x44, 0x0a, 0x0a, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x52, 0x09, 0x70,
	0x72, 0x69, 0x63, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x22, 0x5d, 0x0a, 0x15, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x22, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10,
	0x01, 0x18, 0x64, 0x48, 0x00, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x42, 0x07,
	0x0a, 0x05, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x5d, 0x0a, 0x15, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x44, 0x0a, 0x0a, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x52, 0x09, 0x70, 0x72, 0x69,
	0x63, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x22, 0x30, 0x0a, 0x15, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x50, 0x72, 0x69, 0x63, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12,
	0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x22, 0x17, 0x0a, 0x15, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x22, 0x52, 0x0a, 0x18, 0x44, 0x75, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x65, 0x50, 0x72,
	0x69, 0x63, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x17, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x64, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x60, 0x0a, 0x18, 0x44, 0x75, 0x70, 0x6c, 0x69, 0x63, 0x61,
	0x74, 0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x44, 0x0a, 0x0a, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x52, 0x09, 0x70, 0x72,
	0x69, 0x63, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x22, 0x95, 0x02, 0x0a, 0x1b, 0x53, 0x61, 0x76, 0x65,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65,
	0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x4b, 0x0a, 0x0a, 0x63, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x69, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x0a, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x69, 0x65, 0x73, 0x12, 0x52, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x20, 0x00, 0x52, 0x0b, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x55, 0x0a, 0x11, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0f,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x22,
	0x1d, 0x0a, 0x1b, 0x53, 0x61, 0x76, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0xba,
	0x01, 0x0a, 0x1b, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x41,
	0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73,
	0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x58, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x40, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x22, 0x79, 0x0a, 0x1b, 0x4c,
	0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x5a, 0x0a, 0x12, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x52, 0x11, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x22, 0x15, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65,
	0x74, 0x42, 0x72, 0x65, 0x65, 0x64, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x22, 0x5a, 0x0a,
	0x13, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x42, 0x72, 0x65, 0x65, 0x64, 0x73, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x43, 0x0a, 0x0a, 0x70, 0x65, 0x74, 0x5f, 0x62, 0x72, 0x65, 0x65,
	0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x42, 0x72, 0x65, 0x65, 0x64, 0x52, 0x09,
	0x70, 0x65, 0x74, 0x42, 0x72, 0x65, 0x65, 0x64, 0x73, 0x22, 0x14, 0x0a, 0x12, 0x4c, 0x69, 0x73,
	0x74, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x22,
	0x56, 0x0a, 0x12, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x73, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x40, 0x0a, 0x09, 0x70, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x70,
	0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x73, 0x22, 0xca, 0x07, 0x0a, 0x13, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12,
	0x44, 0x0a, 0x0a, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x50, 0x72, 0x69, 0x63, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x52, 0x09, 0x70, 0x72, 0x69, 0x63,
	0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x55, 0x0a, 0x11, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x47, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52,
	0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x69,
	0x6e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69,
	0x6e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x6c, 0x6f, 0x72,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x12, 0x0a,
	0x04, 0x73, 0x6f, 0x72, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x73, 0x6f, 0x72,
	0x74, 0x12, 0x28, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d,
	0x6f, 0x6e, 0x65, 0x79, 0x52, 0x05, 0x70, 0x72, 0x69, 0x63, 0x65, 0x12, 0x58, 0x0a, 0x12, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x75, 0x6e, 0x69,
	0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x55,
	0x6e, 0x69, 0x74, 0x52, 0x10, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x72, 0x69, 0x63,
	0x65, 0x55, 0x6e, 0x69, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x74, 0x61, 0x78, 0x5f, 0x72, 0x61, 0x74,
	0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x74, 0x61, 0x78, 0x52, 0x61, 0x74, 0x65,
	0x12, 0x35, 0x0a, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x08, 0x64,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3c, 0x0a, 0x0c, 0x6d, 0x61, 0x78, 0x5f, 0x64,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x6d, 0x61, 0x78, 0x44, 0x75, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4e, 0x0a, 0x0a, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x4c,
	0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x6c, 0x69, 0x6d, 0x69, 0x74,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x52, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x20, 0x00, 0x52, 0x0b, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x69, 0x6d, 0x61,
	0x67, 0x65, 0x73, 0x18, 0x10, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x69, 0x6d, 0x61, 0x67, 0x65,
	0x73, 0x12, 0x36, 0x0a, 0x17, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x5f, 0x64, 0x65, 0x64,
	0x69, 0x63, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x18, 0x11, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x15, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x44, 0x65, 0x64, 0x69, 0x63,
	0x61, 0x74, 0x65, 0x64, 0x53, 0x74, 0x61, 0x66, 0x66, 0x12, 0x49, 0x0a, 0x09, 0x61, 0x75, 0x74,
	0x6f, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x41, 0x75, 0x74, 0x6f, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x08, 0x61, 0x75, 0x74, 0x6f,
	0x52, 0x75, 0x6c, 0x65, 0x22, 0x54, 0x0a, 0x13, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x3d, 0x0a, 0x07, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x52, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x22, 0x2b, 0x0a, 0x10, 0x47, 0x65,
	0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x17,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x22, 0x51, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x3d, 0x0a, 0x07, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x52, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x22, 0xa8, 0x01, 0x0a, 0x12, 0x4c,
	0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x12, 0x41, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74,
	0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4f, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x06, 0x66,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x22, 0x99, 0x01, 0x0a, 0x12, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x42, 0x0a, 0x0a,
	0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76,
	0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x3f, 0x0a, 0x08, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x23, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x08, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x73, 0x22, 0xc9, 0x06, 0x0a, 0x13, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x17, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x88,
	0x01, 0x01, 0x12, 0x56, 0x0a, 0x10, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x63, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x25, 0x0a, 0x0b, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x48,
	0x01, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01,
	0x01, 0x12, 0x1a, 0x0a, 0x08, 0x69, 0x6e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x6e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x12, 0x19, 0x0a,
	0x05, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x48, 0x02, 0x52, 0x05,
	0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x88, 0x01, 0x01, 0x12, 0x28, 0x0a, 0x05, 0x70, 0x72, 0x69, 0x63,
	0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x05, 0x70, 0x72, 0x69,
	0x63, 0x65, 0x12, 0x58, 0x0a, 0x12, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x70, 0x72,
	0x69, 0x63, 0x65, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2a,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x55, 0x6e, 0x69, 0x74, 0x52, 0x10, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x55, 0x6e, 0x69, 0x74, 0x12, 0x1e, 0x0a, 0x08,
	0x74, 0x61, 0x78, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x48, 0x03,
	0x52, 0x07, 0x74, 0x61, 0x78, 0x52, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x35, 0x0a, 0x08,
	0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x3c, 0x0a, 0x0c, 0x6d, 0x61, 0x78, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x6d, 0x61, 0x78, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x4e, 0x0a, 0x0a, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x4c, 0x69, 0x6d, 0x69, 0x74,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x16, 0x0a, 0x06, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x18, 0x0d, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x06, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x12, 0x3b, 0x0a, 0x17, 0x72, 0x65, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x5f, 0x64, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x73,
	0x74, 0x61, 0x66, 0x66, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x08, 0x48, 0x04, 0x52, 0x15, 0x72, 0x65,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x44, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x65, 0x64, 0x53, 0x74,
	0x61, 0x66, 0x66, 0x88, 0x01, 0x01, 0x12, 0x49, 0x0a, 0x09, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x72,
	0x75, 0x6c, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x41,
	0x75, 0x74, 0x6f, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x08, 0x61, 0x75, 0x74, 0x6f, 0x52, 0x75, 0x6c,
	0x65, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x63,
	0x6f, 0x6c, 0x6f, 0x72, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x74, 0x61, 0x78, 0x5f, 0x72, 0x61, 0x74,
	0x65, 0x42, 0x1a, 0x0a, 0x18, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x5f, 0x64, 0x65,
	0x64, 0x69, 0x63, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x66, 0x66, 0x22, 0x54, 0x0a,
	0x13, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x3d, 0x0a, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x07, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x22, 0x25, 0x0a, 0x13, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0x15, 0x0a, 0x13, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x22, 0x90, 0x01, 0x0a, 0x12, 0x53, 0x6f, 0x72, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x7a, 0x0a, 0x16, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x73, 0x6f, 0x72,
	0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x44, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f, 0x72, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x53, 0x6f, 0x72, 0x74, 0x52, 0x14,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x53,
	0x6f, 0x72, 0x74, 0x73, 0x22, 0x14, 0x0a, 0x12, 0x53, 0x6f, 0x72, 0x74, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0xb9, 0x02, 0x0a, 0x20, 0x4c,
	0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12,
	0x41, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c,
	0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x5d, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x45, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x12, 0x66, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x62, 0x79, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x46, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x48, 0x00, 0x52, 0x07, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x88, 0x01, 0x01, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x5f, 0x62, 0x79, 0x22, 0xd2, 0x01, 0x0a, 0x20, 0x4c, 0x69, 0x73, 0x74, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x48, 0x69, 0x73, 0x74,
	0x6f, 0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x42, 0x0a, 0x0a, 0x70,
	0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32,
	0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x6a, 0x0a, 0x18, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x63, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x5f, 0x68, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x48, 0x69, 0x73, 0x74,
	0x6f, 0x72, 0x79, 0x52, 0x16, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x22, 0xb4, 0x01, 0x0a, 0x18,
	0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x41, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52,
	0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x55, 0x0a, 0x06, 0x66,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x22, 0xb2, 0x01, 0x0a, 0x18, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x42, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c,
	0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x52, 0x0a, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x63,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x0e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x22, 0xfb, 0x01, 0x0a, 0x18, 0x50, 0x75, 0x73, 0x68,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x49, 0x64, 0x73, 0x12, 0x42, 0x0a, 0x07, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74,
	0x52, 0x07, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x73, 0x12, 0x41, 0x0a, 0x0e, 0x65, 0x66, 0x66,
	0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0d, 0x65,
	0x66, 0x66, 0x65, 0x63, 0x74, 0x69, 0x76, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x37, 0x0a, 0x18,
	0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x74, 0x6f, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x65, 0x64, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x15,
	0x61, 0x70, 0x70, 0x6c, 0x79, 0x54, 0x6f, 0x42, 0x6f, 0x6f, 0x6b, 0x65, 0x64, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x73, 0x22, 0x78, 0x0a, 0x18, 0x50, 0x75, 0x73, 0x68, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x2e, 0x0a, 0x13, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x6f, 0x6d,
	0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x11,
	0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64,
	0x73, 0x12, 0x2c, 0x0a, 0x12, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x5f, 0x63, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x52, 0x10, 0x66,
	0x61, 0x69, 0x6c, 0x65, 0x64, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x73, 0x22,
	0x70, 0x0a, 0x16, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x56, 0x0a, 0x0e, 0x65, 0x76, 0x61,
	0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x66, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44,
	0x65, 0x66, 0x52, 0x0d, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65,
	0x66, 0x22, 0x60, 0x0a, 0x16, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x46, 0x0a, 0x0a, 0x65,
	0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x26, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x76, 0x61,
	0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x22, 0x89, 0x01, 0x0a, 0x16, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x76,
	0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x17,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x56, 0x0a, 0x0e, 0x65, 0x76, 0x61, 0x6c, 0x75,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x66, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x66,
	0x52, 0x0d, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x66, 0x22,
	0x60, 0x0a, 0x16, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x46, 0x0a, 0x0a, 0x65, 0x76, 0x61,
	0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x76, 0x61, 0x6c, 0x75,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x22, 0x6b, 0x0a, 0x15, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x52, 0x0a, 0x06, 0x66, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x76, 0x61,
	0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x22, 0x61,
	0x0a, 0x15, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x48, 0x0a, 0x0b, 0x65, 0x76, 0x61, 0x6c, 0x75,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x22, 0x88, 0x01, 0x0a, 0x1b, 0x50, 0x75, 0x73, 0x68, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x12, 0x25, 0x0a, 0x0e, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0d, 0x65, 0x76, 0x61, 0x6c, 0x75,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x73, 0x12, 0x42, 0x0a, 0x07, 0x74, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x4f, 0x62, 0x6a,
	0x65, 0x63, 0x74, 0x52, 0x07, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x73, 0x22, 0x7b, 0x0a, 0x1b,
	0x50, 0x75, 0x73, 0x68, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x2e, 0x0a, 0x13, 0x73,
	0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69,
	0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x11, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x73, 0x12, 0x2c, 0x0a, 0x12, 0x66,
	0x61, 0x69, 0x6c, 0x65, 0x64, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x52, 0x10, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x43,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64, 0x73, 0x22, 0x33, 0x0a, 0x15, 0x53, 0x6f, 0x72,
	0x74, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x12, 0x1a, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x08, 0x01, 0x52, 0x03, 0x69, 0x64, 0x73, 0x22, 0x17,
	0x0a, 0x15, 0x53, 0x6f, 0x72, 0x74, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x75, 0x0a, 0x17, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x12, 0x5a, 0x0a, 0x10, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x75,
	0x6c, 0x65, 0x5f, 0x64, 0x65, 0x66, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x44, 0x65, 0x66, 0x52, 0x0e,
	0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x44, 0x65, 0x66, 0x22, 0x65,
	0x0a, 0x17, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52,
	0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x4a, 0x0a, 0x0c, 0x70, 0x72, 0x69,
	0x63, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x69,
	0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x0b, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e,
	0x67, 0x52, 0x75, 0x6c, 0x65, 0x22, 0x8e, 0x01, 0x0a, 0x17, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x12, 0x17, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x5a, 0x0a, 0x10, 0x70, 0x72,
	0x69, 0x63, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x64, 0x65, 0x66, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52,
	0x75, 0x6c, 0x65, 0x44, 0x65, 0x66, 0x52, 0x0e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52,
	0x75, 0x6c, 0x65, 0x44, 0x65, 0x66, 0x22, 0x65, 0x0a, 0x17, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x4a, 0x0a, 0x0c, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x75, 0x6c,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65,
	0x52, 0x0b, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x22, 0x6d, 0x0a,
	0x16, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65,
	0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x53, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e,
	0x67, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x22, 0x66, 0x0a, 0x16,
	0x4c, 0x69, 0x73, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x73,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x4c, 0x0a, 0x0d, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e,
	0x67, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69,
	0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x0c, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52,
	0x75, 0x6c, 0x65, 0x73, 0x22, 0x34, 0x0a, 0x16, 0x53, 0x6f, 0x72, 0x74, 0x50, 0x72, 0x69, 0x63,
	0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x1a,
	0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x42, 0x08, 0xfa, 0x42, 0x05,
	0x92, 0x01, 0x02, 0x08, 0x01, 0x52, 0x03, 0x69, 0x64, 0x73, 0x22, 0x18, 0x0a, 0x16, 0x53, 0x6f,
	0x72, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x22, 0x8c, 0x01, 0x0a, 0x1c, 0x50, 0x75, 0x73, 0x68, 0x50, 0x72, 0x69,
	0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x28, 0x0a, 0x10, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67,
	0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52,
	0x0e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x49, 0x64, 0x73, 0x12,
	0x42, 0x0a, 0x07, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65,
	0x6e, 0x61, 0x6e, 0x74, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x07, 0x74, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x73, 0x22, 0x7c, 0x0a, 0x1c, 0x50, 0x75, 0x73, 0x68, 0x50, 0x72, 0x69, 0x63, 0x69,
	0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x2e, 0x0a, 0x13, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x63,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03,
	0x52, 0x11, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x49, 0x64, 0x73, 0x12, 0x2c, 0x0a, 0x12, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x5f, 0x63, 0x6f,
	0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x52,
	0x10, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64,
	0x73, 0x22, 0x7d, 0x0a, 0x19, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x60,
	0x0a, 0x12, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65,
	0x5f, 0x64, 0x65, 0x66, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x44, 0x65, 0x66, 0x52, 0x10,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x44, 0x65, 0x66,
	0x22, 0x6d, 0x0a, 0x19, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x50, 0x0a,
	0x0e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65,
	0x52, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x22,
	0x96, 0x01, 0x0a, 0x19, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x17, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x60, 0x0a, 0x12, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x64, 0x65, 0x66, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61,
	0x72, 0x67, 0x65, 0x44, 0x65, 0x66, 0x52, 0x10, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43,
	0x68, 0x61, 0x72, 0x67, 0x65, 0x44, 0x65, 0x66, 0x22, 0x6d, 0x0a, 0x19, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x50, 0x0a, 0x0e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x52, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x22, 0x71, 0x0a, 0x18, 0x4c, 0x69, 0x73, 0x74, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x12, 0x55, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61,
	0x72, 0x67, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x22, 0x6e, 0x0a, 0x18, 0x4c, 0x69,
	0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x52, 0x0a, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x52, 0x0e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x22, 0x36, 0x0a, 0x18, 0x53, 0x6f,
	0x72, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x1a, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x03, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x08, 0x01, 0x52, 0x03, 0x69,
	0x64, 0x73, 0x22, 0x1a, 0x0a, 0x18, 0x53, 0x6f, 0x72, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x92,
	0x01, 0x0a, 0x1e, 0x50, 0x75, 0x73, 0x68, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68,
	0x61, 0x72, 0x67, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x12, 0x2c, 0x0a, 0x12, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x63, 0x68, 0x61,
	0x72, 0x67, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x10, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x49, 0x64, 0x73, 0x12,
	0x42, 0x0a, 0x07, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65,
	0x6e, 0x61, 0x6e, 0x74, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x07, 0x74, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x73, 0x22, 0x7e, 0x0a, 0x1e, 0x50, 0x75, 0x73, 0x68, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x2e, 0x0a, 0x13, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x03, 0x52, 0x11, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x49, 0x64, 0x73, 0x12, 0x2c, 0x0a, 0x12, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x5f,
	0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x03, 0x52, 0x10, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x49, 0x64, 0x73, 0x32, 0x8b, 0x23, 0x0a, 0x10, 0x50, 0x72, 0x69, 0x63, 0x65, 0x42, 0x6f, 0x6f,
	0x6b, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x7c, 0x0a, 0x0e, 0x4c, 0x69, 0x73, 0x74,
	0x50, 0x72, 0x69, 0x63, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x73, 0x12, 0x34, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x70, 0x72,
	0x69, 0x63, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x50, 0x72, 0x69, 0x63, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x1a, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x76,
	0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x72, 0x69, 0x63, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x73,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x7f, 0x0a, 0x0f, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x50, 0x72, 0x69, 0x63, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x12, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x69,
	0x63, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x1a, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x42, 0x6f, 0x6f,
	0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x7f, 0x0a, 0x0f, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x12, 0x35, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x70, 0x72,
	0x69, 0x63, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x1a, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x2e,
	0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x42, 0x6f,
	0x6f, 0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x7f, 0x0a, 0x0f, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x12, 0x35, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x70,
	0x72, 0x69, 0x63, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x1a, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b,
	0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x42,
	0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x88, 0x01, 0x0a, 0x12, 0x44, 0x75,
	0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x42, 0x6f, 0x6f, 0x6b,
	0x12, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x76,
	0x31, 0x2e, 0x44, 0x75, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x65, 0x50, 0x72, 0x69, 0x63, 0x65,
	0x42, 0x6f, 0x6f, 0x6b, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x38, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x70, 0x72,
	0x69, 0x63, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x75, 0x70, 0x6c,
	0x69, 0x63, 0x61, 0x74, 0x65, 0x50, 0x72, 0x69, 0x63, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x93, 0x01, 0x0a, 0x15, 0x53, 0x61, 0x76, 0x65, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x12, 0x3b,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73,
	0x65, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x61, 0x76, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x69, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x3b, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x70,
	0x72, 0x69, 0x63, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x61, 0x76,
	0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69,
	0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x00, 0x12, 0x93, 0x01, 0x0a, 0x15, 0x4c,
	0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x69, 0x65, 0x73, 0x12, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x62, 0x6f,
	0x6f, 0x6b, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x1a, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x00,
	0x12, 0x7b, 0x0a, 0x0d, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x42, 0x72, 0x65, 0x65, 0x64,
	0x73, 0x12, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x42, 0x72, 0x65, 0x65, 0x64, 0x73,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f,
	0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x42,
	0x72, 0x65, 0x65, 0x64, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x00, 0x12, 0x78, 0x0a,
	0x0c, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x32, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x2e, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x1a, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x73, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x00, 0x12, 0x7b, 0x0a, 0x0d, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x69, 0x63,
	0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x33, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x2e, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x22, 0x00, 0x12, 0x72, 0x0a, 0x0a, 0x47, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x12, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x1a, 0x30, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x62, 0x6f,
	0x6f, 0x6b, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x00, 0x12, 0x78, 0x0a, 0x0c, 0x4c, 0x69, 0x73, 0x74,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x12, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x69, 0x63,
	0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x32, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e,
	0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x22, 0x00, 0x12, 0x7b, 0x0a, 0x0d, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x12, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x62, 0x6f, 0x6f,
	0x6b, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x69, 0x63,
	0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x00, 0x12,
	0x7b, 0x0a, 0x0d, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x12, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x76,
	0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x00, 0x12, 0x78, 0x0a, 0x0c,
	0x53, 0x6f, 0x72, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x12, 0x32, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e,
	0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f,
	0x72, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x1a, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x6f, 0x72, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x22, 0x00, 0x12, 0xa2, 0x01, 0x0a, 0x1a, 0x4c, 0x69, 0x73, 0x74, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x48, 0x69, 0x73, 0x74,
	0x6f, 0x72, 0x69, 0x65, 0x73, 0x12, 0x40, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x69, 0x65,
	0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x40, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72,
	0x69, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x00, 0x12, 0x8a, 0x01, 0x0a, 0x12,
	0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x73, 0x12, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x38, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e,
	0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x73,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x00, 0x12, 0x8a, 0x01, 0x0a, 0x12, 0x50, 0x75, 0x73,
	0x68, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x12,
	0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x76, 0x31,
	0x2e, 0x50, 0x75, 0x73, 0x68, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x69,
	0x63, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x75, 0x73, 0x68, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x22, 0x00, 0x12, 0x84, 0x01, 0x0a, 0x10, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x36, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x70, 0x72,
	0x69, 0x63, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x1a, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x00, 0x12, 0x84, 0x01, 0x0a,
	0x10, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x2e,
	0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x69,
	0x63, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x22, 0x00, 0x12, 0x81, 0x01, 0x0a, 0x0f, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x76, 0x61, 0x6c,
	0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x76, 0x61,
	0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x35,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73,
	0x65, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x76, 0x31, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x00, 0x12, 0x81, 0x01, 0x0a, 0x0f, 0x53, 0x6f, 0x72, 0x74,
	0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x35, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x70,
	0x72, 0x69, 0x63, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f, 0x72,
	0x74, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x1a, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f, 0x72, 0x74, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x00, 0x12, 0x93, 0x01, 0x0a, 0x15,
	0x50, 0x75, 0x73, 0x68, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x73, 0x12, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x75, 0x73, 0x68, 0x45, 0x76, 0x61, 0x6c, 0x75,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x1a, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b,
	0x2e, 0x76, 0x31, 0x2e, 0x50, 0x75, 0x73, 0x68, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22,
	0x00, 0x12, 0x85, 0x01, 0x0a, 0x11, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x72, 0x69, 0x63,
	0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50,
	0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x1a, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52,
	0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x85, 0x01, 0x0a, 0x11, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x12,
	0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x76, 0x31,
	0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75,
	0x6c, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x69, 0x63,
	0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x82, 0x01, 0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e,
	0x67, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x12, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f,
	0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x72, 0x69, 0x63,
	0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x36,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73,
	0x65, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x76, 0x31, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x73,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x82, 0x01, 0x0a, 0x10, 0x53, 0x6f, 0x72, 0x74, 0x50,
	0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x12, 0x36, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x70,
	0x72, 0x69, 0x63, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f, 0x72,
	0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x1a, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x62, 0x6f, 0x6f,
	0x6b, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f, 0x72, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67,
	0x52, 0x75, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x94, 0x01, 0x0a, 0x16,
	0x50, 0x75, 0x73, 0x68, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x43,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x12, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f,
	0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x75, 0x73, 0x68, 0x50, 0x72, 0x69, 0x63,
	0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x1a, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x62, 0x6f,
	0x6f, 0x6b, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x75, 0x73, 0x68, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e,
	0x67, 0x52, 0x75, 0x6c, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x12, 0x8b, 0x01, 0x0a, 0x13, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x12, 0x39, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x70, 0x72,
	0x69, 0x63, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x8b, 0x01, 0x0a, 0x13, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x12, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x69, 0x63,
	0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x1a, 0x39, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x62, 0x6f, 0x6f,
	0x6b, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x88,
	0x01, 0x0a, 0x12, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68,
	0x61, 0x72, 0x67, 0x65, 0x73, 0x12, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a,
	0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72,
	0x67, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x88, 0x01, 0x0a, 0x12, 0x53, 0x6f,
	0x72, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73,
	0x12, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x6f, 0x72, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61,
	0x72, 0x67, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x38, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x70, 0x72,
	0x69, 0x63, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6f, 0x72, 0x74,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x9a, 0x01, 0x0a, 0x18, 0x50, 0x75, 0x73, 0x68, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x73, 0x12, 0x3e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x2e,
	0x76, 0x31, 0x2e, 0x50, 0x75, 0x73, 0x68, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68,
	0x61, 0x72, 0x67, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x1a, 0x3e, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x2e,
	0x76, 0x31, 0x2e, 0x50, 0x75, 0x73, 0x68, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x68,
	0x61, 0x72, 0x67, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x42, 0x8e, 0x01, 0x0a, 0x26, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x69, 0x64, 0x6c, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x70,
	0x72, 0x69, 0x63, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x62,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f,
	0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70,
	0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75,
	0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b,
	0x2f, 0x76, 0x31, 0x3b, 0x70, 0x72, 0x69, 0x63, 0x65, 0x62, 0x6f, 0x6f, 0x6b, 0x61, 0x70, 0x69,
	0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_enterprise_price_book_v1_price_book_api_proto_rawDescOnce sync.Once
	file_moego_enterprise_price_book_v1_price_book_api_proto_rawDescData = file_moego_enterprise_price_book_v1_price_book_api_proto_rawDesc
)

func file_moego_enterprise_price_book_v1_price_book_api_proto_rawDescGZIP() []byte {
	file_moego_enterprise_price_book_v1_price_book_api_proto_rawDescOnce.Do(func() {
		file_moego_enterprise_price_book_v1_price_book_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_enterprise_price_book_v1_price_book_api_proto_rawDescData)
	})
	return file_moego_enterprise_price_book_v1_price_book_api_proto_rawDescData
}

var file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes = make([]protoimpl.MessageInfo, 66)
var file_moego_enterprise_price_book_v1_price_book_api_proto_goTypes = []interface{}{
	(*ListPriceBooksParams)(nil),                          // 0: moego.enterprise.price_book.v1.ListPriceBooksParams
	(*ListPriceBooksResult)(nil),                          // 1: moego.enterprise.price_book.v1.ListPriceBooksResult
	(*CreatePriceBookParams)(nil),                         // 2: moego.enterprise.price_book.v1.CreatePriceBookParams
	(*CreatePriceBookResult)(nil),                         // 3: moego.enterprise.price_book.v1.CreatePriceBookResult
	(*UpdatePriceBookParams)(nil),                         // 4: moego.enterprise.price_book.v1.UpdatePriceBookParams
	(*UpdatePriceBookResult)(nil),                         // 5: moego.enterprise.price_book.v1.UpdatePriceBookResult
	(*DeletePriceBookParams)(nil),                         // 6: moego.enterprise.price_book.v1.DeletePriceBookParams
	(*DeletePriceBookResult)(nil),                         // 7: moego.enterprise.price_book.v1.DeletePriceBookResult
	(*DuplicatePriceBookParams)(nil),                      // 8: moego.enterprise.price_book.v1.DuplicatePriceBookParams
	(*DuplicatePriceBookResult)(nil),                      // 9: moego.enterprise.price_book.v1.DuplicatePriceBookResult
	(*SaveServiceCategoriesParams)(nil),                   // 10: moego.enterprise.price_book.v1.SaveServiceCategoriesParams
	(*SaveServiceCategoriesResult)(nil),                   // 11: moego.enterprise.price_book.v1.SaveServiceCategoriesResult
	(*ListServiceCategoriesParams)(nil),                   // 12: moego.enterprise.price_book.v1.ListServiceCategoriesParams
	(*ListServiceCategoriesResult)(nil),                   // 13: moego.enterprise.price_book.v1.ListServiceCategoriesResult
	(*ListPetBreedsParams)(nil),                           // 14: moego.enterprise.price_book.v1.ListPetBreedsParams
	(*ListPetBreedsResult)(nil),                           // 15: moego.enterprise.price_book.v1.ListPetBreedsResult
	(*ListPetTypesParams)(nil),                            // 16: moego.enterprise.price_book.v1.ListPetTypesParams
	(*ListPetTypesResult)(nil),                            // 17: moego.enterprise.price_book.v1.ListPetTypesResult
	(*CreateServiceParams)(nil),                           // 18: moego.enterprise.price_book.v1.CreateServiceParams
	(*CreateServiceResult)(nil),                           // 19: moego.enterprise.price_book.v1.CreateServiceResult
	(*GetServiceParams)(nil),                              // 20: moego.enterprise.price_book.v1.GetServiceParams
	(*GetServiceResult)(nil),                              // 21: moego.enterprise.price_book.v1.GetServiceResult
	(*ListServicesParams)(nil),                            // 22: moego.enterprise.price_book.v1.ListServicesParams
	(*ListServicesResult)(nil),                            // 23: moego.enterprise.price_book.v1.ListServicesResult
	(*UpdateServiceParams)(nil),                           // 24: moego.enterprise.price_book.v1.UpdateServiceParams
	(*UpdateServiceResult)(nil),                           // 25: moego.enterprise.price_book.v1.UpdateServiceResult
	(*DeleteServiceParams)(nil),                           // 26: moego.enterprise.price_book.v1.DeleteServiceParams
	(*DeleteServiceResult)(nil),                           // 27: moego.enterprise.price_book.v1.DeleteServiceResult
	(*SortServicesParams)(nil),                            // 28: moego.enterprise.price_book.v1.SortServicesParams
	(*SortServicesResult)(nil),                            // 29: moego.enterprise.price_book.v1.SortServicesResult
	(*ListServiceChangeHistoriesParams)(nil),              // 30: moego.enterprise.price_book.v1.ListServiceChangeHistoriesParams
	(*ListServiceChangeHistoriesResult)(nil),              // 31: moego.enterprise.price_book.v1.ListServiceChangeHistoriesResult
	(*ListServiceChangesParams)(nil),                      // 32: moego.enterprise.price_book.v1.ListServiceChangesParams
	(*ListServiceChangesResult)(nil),                      // 33: moego.enterprise.price_book.v1.ListServiceChangesResult
	(*PushServiceChangesParams)(nil),                      // 34: moego.enterprise.price_book.v1.PushServiceChangesParams
	(*PushServiceChangesResult)(nil),                      // 35: moego.enterprise.price_book.v1.PushServiceChangesResult
	(*CreateEvaluationParams)(nil),                        // 36: moego.enterprise.price_book.v1.CreateEvaluationParams
	(*CreateEvaluationResult)(nil),                        // 37: moego.enterprise.price_book.v1.CreateEvaluationResult
	(*UpdateEvaluationParams)(nil),                        // 38: moego.enterprise.price_book.v1.UpdateEvaluationParams
	(*UpdateEvaluationResult)(nil),                        // 39: moego.enterprise.price_book.v1.UpdateEvaluationResult
	(*ListEvaluationsParams)(nil),                         // 40: moego.enterprise.price_book.v1.ListEvaluationsParams
	(*ListEvaluationsResult)(nil),                         // 41: moego.enterprise.price_book.v1.ListEvaluationsResult
	(*PushEvaluationChangesParams)(nil),                   // 42: moego.enterprise.price_book.v1.PushEvaluationChangesParams
	(*PushEvaluationChangesResult)(nil),                   // 43: moego.enterprise.price_book.v1.PushEvaluationChangesResult
	(*SortEvaluationsParams)(nil),                         // 44: moego.enterprise.price_book.v1.SortEvaluationsParams
	(*SortEvaluationsResult)(nil),                         // 45: moego.enterprise.price_book.v1.SortEvaluationsResult
	(*CreatePricingRuleParams)(nil),                       // 46: moego.enterprise.price_book.v1.CreatePricingRuleParams
	(*CreatePricingRuleResult)(nil),                       // 47: moego.enterprise.price_book.v1.CreatePricingRuleResult
	(*UpdatePricingRuleParams)(nil),                       // 48: moego.enterprise.price_book.v1.UpdatePricingRuleParams
	(*UpdatePricingRuleResult)(nil),                       // 49: moego.enterprise.price_book.v1.UpdatePricingRuleResult
	(*ListPricingRulesParams)(nil),                        // 50: moego.enterprise.price_book.v1.ListPricingRulesParams
	(*ListPricingRulesResult)(nil),                        // 51: moego.enterprise.price_book.v1.ListPricingRulesResult
	(*SortPricingRulesParams)(nil),                        // 52: moego.enterprise.price_book.v1.SortPricingRulesParams
	(*SortPricingRulesResult)(nil),                        // 53: moego.enterprise.price_book.v1.SortPricingRulesResult
	(*PushPricingRuleChangesParams)(nil),                  // 54: moego.enterprise.price_book.v1.PushPricingRuleChangesParams
	(*PushPricingRuleChangesResult)(nil),                  // 55: moego.enterprise.price_book.v1.PushPricingRuleChangesResult
	(*CreateServiceChargeParams)(nil),                     // 56: moego.enterprise.price_book.v1.CreateServiceChargeParams
	(*CreateServiceChargeResult)(nil),                     // 57: moego.enterprise.price_book.v1.CreateServiceChargeResult
	(*UpdateServiceChargeParams)(nil),                     // 58: moego.enterprise.price_book.v1.UpdateServiceChargeParams
	(*UpdateServiceChargeResult)(nil),                     // 59: moego.enterprise.price_book.v1.UpdateServiceChargeResult
	(*ListServiceChargesParams)(nil),                      // 60: moego.enterprise.price_book.v1.ListServiceChargesParams
	(*ListServiceChargesResult)(nil),                      // 61: moego.enterprise.price_book.v1.ListServiceChargesResult
	(*SortServiceChargesParams)(nil),                      // 62: moego.enterprise.price_book.v1.SortServiceChargesParams
	(*SortServiceChargesResult)(nil),                      // 63: moego.enterprise.price_book.v1.SortServiceChargesResult
	(*PushServiceChargeChangesParams)(nil),                // 64: moego.enterprise.price_book.v1.PushServiceChargeChangesParams
	(*PushServiceChargeChangesResult)(nil),                // 65: moego.enterprise.price_book.v1.PushServiceChargeChangesResult
	(*v1.PriceBook)(nil),                                  // 66: moego.models.enterprise.v1.PriceBook
	(*v1.ServiceCategory)(nil),                            // 67: moego.models.enterprise.v1.ServiceCategory
	(v11.ServiceType)(0),                                  // 68: moego.models.offering.v1.ServiceType
	(v11.ServiceItemType)(0),                              // 69: moego.models.offering.v1.ServiceItemType
	(*v2.PaginationRequest)(nil),                          // 70: moego.utils.v2.PaginationRequest
	(*v12.ListServiceCategoriesRequest_Filter)(nil),       // 71: moego.service.enterprise.v1.ListServiceCategoriesRequest.Filter
	(*v1.PetBreed)(nil),                                   // 72: moego.models.enterprise.v1.PetBreed
	(*v1.PetType)(nil),                                    // 73: moego.models.enterprise.v1.PetType
	(*money.Money)(nil),                                   // 74: google.type.Money
	(v11.ServicePriceUnit)(0),                             // 75: moego.models.offering.v1.ServicePriceUnit
	(*durationpb.Duration)(nil),                           // 76: google.protobuf.Duration
	(*v1.Service_Limitation)(nil),                         // 77: moego.models.enterprise.v1.Service.Limitation
	(*v1.Service_AutoRule)(nil),                           // 78: moego.models.enterprise.v1.Service.AutoRule
	(*v1.Service)(nil),                                    // 79: moego.models.enterprise.v1.Service
	(*v12.ListServicesRequest_Filter)(nil),                // 80: moego.service.enterprise.v1.ListServicesRequest.Filter
	(*v2.PaginationResponse)(nil),                         // 81: moego.utils.v2.PaginationResponse
	(*v12.SortServicesRequest_ServiceCategorySort)(nil),   // 82: moego.service.enterprise.v1.SortServicesRequest.ServiceCategorySort
	(*v12.ListServiceChangeHistoriesRequest_Filter)(nil),  // 83: moego.service.enterprise.v1.ListServiceChangeHistoriesRequest.Filter
	(*v12.ListServiceChangeHistoriesRequest_OrderBy)(nil), // 84: moego.service.enterprise.v1.ListServiceChangeHistoriesRequest.OrderBy
	(*v1.ServiceChangeHistory)(nil),                       // 85: moego.models.enterprise.v1.ServiceChangeHistory
	(*v12.ListServiceChangesRequest_Filter)(nil),          // 86: moego.service.enterprise.v1.ListServiceChangesRequest.Filter
	(*v1.ServiceChange)(nil),                              // 87: moego.models.enterprise.v1.ServiceChange
	(*v1.TenantObject)(nil),                               // 88: moego.models.enterprise.v1.TenantObject
	(*timestamppb.Timestamp)(nil),                         // 89: google.protobuf.Timestamp
	(*v1.CreateEvaluationDef)(nil),                        // 90: moego.models.enterprise.v1.CreateEvaluationDef
	(*v1.Evaluation)(nil),                                 // 91: moego.models.enterprise.v1.Evaluation
	(*v1.UpdateEvaluationDef)(nil),                        // 92: moego.models.enterprise.v1.UpdateEvaluationDef
	(*v12.ListEvaluationsRequest_Filter)(nil),             // 93: moego.service.enterprise.v1.ListEvaluationsRequest.Filter
	(*v1.CreatePricingRuleDef)(nil),                       // 94: moego.models.enterprise.v1.CreatePricingRuleDef
	(*v1.PricingRule)(nil),                                // 95: moego.models.enterprise.v1.PricingRule
	(*v1.UpdatePricingRuleDef)(nil),                       // 96: moego.models.enterprise.v1.UpdatePricingRuleDef
	(*v12.ListPricingRulesRequest_Filter)(nil),            // 97: moego.service.enterprise.v1.ListPricingRulesRequest.Filter
	(*v1.CreateServiceChargeDef)(nil),                     // 98: moego.models.enterprise.v1.CreateServiceChargeDef
	(*v1.ServiceCharge)(nil),                              // 99: moego.models.enterprise.v1.ServiceCharge
	(*v1.UpdateServiceChargeDef)(nil),                     // 100: moego.models.enterprise.v1.UpdateServiceChargeDef
	(*v12.ListServiceChargesRequest_Filter)(nil),          // 101: moego.service.enterprise.v1.ListServiceChargesRequest.Filter
}
var file_moego_enterprise_price_book_v1_price_book_api_proto_depIdxs = []int32{
	66,  // 0: moego.enterprise.price_book.v1.ListPriceBooksResult.price_books:type_name -> moego.models.enterprise.v1.PriceBook
	66,  // 1: moego.enterprise.price_book.v1.CreatePriceBookResult.price_book:type_name -> moego.models.enterprise.v1.PriceBook
	66,  // 2: moego.enterprise.price_book.v1.UpdatePriceBookResult.price_book:type_name -> moego.models.enterprise.v1.PriceBook
	66,  // 3: moego.enterprise.price_book.v1.DuplicatePriceBookResult.price_book:type_name -> moego.models.enterprise.v1.PriceBook
	67,  // 4: moego.enterprise.price_book.v1.SaveServiceCategoriesParams.categories:type_name -> moego.models.enterprise.v1.ServiceCategory
	68,  // 5: moego.enterprise.price_book.v1.SaveServiceCategoriesParams.service_type:type_name -> moego.models.offering.v1.ServiceType
	69,  // 6: moego.enterprise.price_book.v1.SaveServiceCategoriesParams.service_item_type:type_name -> moego.models.offering.v1.ServiceItemType
	70,  // 7: moego.enterprise.price_book.v1.ListServiceCategoriesParams.pagination:type_name -> moego.utils.v2.PaginationRequest
	71,  // 8: moego.enterprise.price_book.v1.ListServiceCategoriesParams.filter:type_name -> moego.service.enterprise.v1.ListServiceCategoriesRequest.Filter
	67,  // 9: moego.enterprise.price_book.v1.ListServiceCategoriesResult.service_categories:type_name -> moego.models.enterprise.v1.ServiceCategory
	72,  // 10: moego.enterprise.price_book.v1.ListPetBreedsResult.pet_breeds:type_name -> moego.models.enterprise.v1.PetBreed
	73,  // 11: moego.enterprise.price_book.v1.ListPetTypesResult.pet_types:type_name -> moego.models.enterprise.v1.PetType
	66,  // 12: moego.enterprise.price_book.v1.CreateServiceParams.price_book:type_name -> moego.models.enterprise.v1.PriceBook
	69,  // 13: moego.enterprise.price_book.v1.CreateServiceParams.service_item_type:type_name -> moego.models.offering.v1.ServiceItemType
	67,  // 14: moego.enterprise.price_book.v1.CreateServiceParams.category:type_name -> moego.models.enterprise.v1.ServiceCategory
	74,  // 15: moego.enterprise.price_book.v1.CreateServiceParams.price:type_name -> google.type.Money
	75,  // 16: moego.enterprise.price_book.v1.CreateServiceParams.service_price_unit:type_name -> moego.models.offering.v1.ServicePriceUnit
	76,  // 17: moego.enterprise.price_book.v1.CreateServiceParams.duration:type_name -> google.protobuf.Duration
	76,  // 18: moego.enterprise.price_book.v1.CreateServiceParams.max_duration:type_name -> google.protobuf.Duration
	77,  // 19: moego.enterprise.price_book.v1.CreateServiceParams.limitation:type_name -> moego.models.enterprise.v1.Service.Limitation
	68,  // 20: moego.enterprise.price_book.v1.CreateServiceParams.service_type:type_name -> moego.models.offering.v1.ServiceType
	78,  // 21: moego.enterprise.price_book.v1.CreateServiceParams.auto_rule:type_name -> moego.models.enterprise.v1.Service.AutoRule
	79,  // 22: moego.enterprise.price_book.v1.CreateServiceResult.service:type_name -> moego.models.enterprise.v1.Service
	79,  // 23: moego.enterprise.price_book.v1.GetServiceResult.service:type_name -> moego.models.enterprise.v1.Service
	70,  // 24: moego.enterprise.price_book.v1.ListServicesParams.pagination:type_name -> moego.utils.v2.PaginationRequest
	80,  // 25: moego.enterprise.price_book.v1.ListServicesParams.filter:type_name -> moego.service.enterprise.v1.ListServicesRequest.Filter
	81,  // 26: moego.enterprise.price_book.v1.ListServicesResult.pagination:type_name -> moego.utils.v2.PaginationResponse
	79,  // 27: moego.enterprise.price_book.v1.ListServicesResult.services:type_name -> moego.models.enterprise.v1.Service
	67,  // 28: moego.enterprise.price_book.v1.UpdateServiceParams.service_category:type_name -> moego.models.enterprise.v1.ServiceCategory
	74,  // 29: moego.enterprise.price_book.v1.UpdateServiceParams.price:type_name -> google.type.Money
	75,  // 30: moego.enterprise.price_book.v1.UpdateServiceParams.service_price_unit:type_name -> moego.models.offering.v1.ServicePriceUnit
	76,  // 31: moego.enterprise.price_book.v1.UpdateServiceParams.duration:type_name -> google.protobuf.Duration
	76,  // 32: moego.enterprise.price_book.v1.UpdateServiceParams.max_duration:type_name -> google.protobuf.Duration
	77,  // 33: moego.enterprise.price_book.v1.UpdateServiceParams.limitation:type_name -> moego.models.enterprise.v1.Service.Limitation
	78,  // 34: moego.enterprise.price_book.v1.UpdateServiceParams.auto_rule:type_name -> moego.models.enterprise.v1.Service.AutoRule
	79,  // 35: moego.enterprise.price_book.v1.UpdateServiceResult.service:type_name -> moego.models.enterprise.v1.Service
	82,  // 36: moego.enterprise.price_book.v1.SortServicesParams.service_category_sorts:type_name -> moego.service.enterprise.v1.SortServicesRequest.ServiceCategorySort
	70,  // 37: moego.enterprise.price_book.v1.ListServiceChangeHistoriesParams.pagination:type_name -> moego.utils.v2.PaginationRequest
	83,  // 38: moego.enterprise.price_book.v1.ListServiceChangeHistoriesParams.filter:type_name -> moego.service.enterprise.v1.ListServiceChangeHistoriesRequest.Filter
	84,  // 39: moego.enterprise.price_book.v1.ListServiceChangeHistoriesParams.order_by:type_name -> moego.service.enterprise.v1.ListServiceChangeHistoriesRequest.OrderBy
	81,  // 40: moego.enterprise.price_book.v1.ListServiceChangeHistoriesResult.pagination:type_name -> moego.utils.v2.PaginationResponse
	85,  // 41: moego.enterprise.price_book.v1.ListServiceChangeHistoriesResult.service_change_histories:type_name -> moego.models.enterprise.v1.ServiceChangeHistory
	70,  // 42: moego.enterprise.price_book.v1.ListServiceChangesParams.pagination:type_name -> moego.utils.v2.PaginationRequest
	86,  // 43: moego.enterprise.price_book.v1.ListServiceChangesParams.filter:type_name -> moego.service.enterprise.v1.ListServiceChangesRequest.Filter
	81,  // 44: moego.enterprise.price_book.v1.ListServiceChangesResult.pagination:type_name -> moego.utils.v2.PaginationResponse
	87,  // 45: moego.enterprise.price_book.v1.ListServiceChangesResult.service_changes:type_name -> moego.models.enterprise.v1.ServiceChange
	88,  // 46: moego.enterprise.price_book.v1.PushServiceChangesParams.targets:type_name -> moego.models.enterprise.v1.TenantObject
	89,  // 47: moego.enterprise.price_book.v1.PushServiceChangesParams.effective_date:type_name -> google.protobuf.Timestamp
	90,  // 48: moego.enterprise.price_book.v1.CreateEvaluationParams.evaluation_def:type_name -> moego.models.enterprise.v1.CreateEvaluationDef
	91,  // 49: moego.enterprise.price_book.v1.CreateEvaluationResult.evaluation:type_name -> moego.models.enterprise.v1.Evaluation
	92,  // 50: moego.enterprise.price_book.v1.UpdateEvaluationParams.evaluation_def:type_name -> moego.models.enterprise.v1.UpdateEvaluationDef
	91,  // 51: moego.enterprise.price_book.v1.UpdateEvaluationResult.evaluation:type_name -> moego.models.enterprise.v1.Evaluation
	93,  // 52: moego.enterprise.price_book.v1.ListEvaluationsParams.filter:type_name -> moego.service.enterprise.v1.ListEvaluationsRequest.Filter
	91,  // 53: moego.enterprise.price_book.v1.ListEvaluationsResult.evaluations:type_name -> moego.models.enterprise.v1.Evaluation
	88,  // 54: moego.enterprise.price_book.v1.PushEvaluationChangesParams.targets:type_name -> moego.models.enterprise.v1.TenantObject
	94,  // 55: moego.enterprise.price_book.v1.CreatePricingRuleParams.pricing_rule_def:type_name -> moego.models.enterprise.v1.CreatePricingRuleDef
	95,  // 56: moego.enterprise.price_book.v1.CreatePricingRuleResult.pricing_rule:type_name -> moego.models.enterprise.v1.PricingRule
	96,  // 57: moego.enterprise.price_book.v1.UpdatePricingRuleParams.pricing_rule_def:type_name -> moego.models.enterprise.v1.UpdatePricingRuleDef
	95,  // 58: moego.enterprise.price_book.v1.UpdatePricingRuleResult.pricing_rule:type_name -> moego.models.enterprise.v1.PricingRule
	97,  // 59: moego.enterprise.price_book.v1.ListPricingRulesParams.filter:type_name -> moego.service.enterprise.v1.ListPricingRulesRequest.Filter
	95,  // 60: moego.enterprise.price_book.v1.ListPricingRulesResult.pricing_rules:type_name -> moego.models.enterprise.v1.PricingRule
	88,  // 61: moego.enterprise.price_book.v1.PushPricingRuleChangesParams.targets:type_name -> moego.models.enterprise.v1.TenantObject
	98,  // 62: moego.enterprise.price_book.v1.CreateServiceChargeParams.service_charge_def:type_name -> moego.models.enterprise.v1.CreateServiceChargeDef
	99,  // 63: moego.enterprise.price_book.v1.CreateServiceChargeResult.service_charge:type_name -> moego.models.enterprise.v1.ServiceCharge
	100, // 64: moego.enterprise.price_book.v1.UpdateServiceChargeParams.service_charge_def:type_name -> moego.models.enterprise.v1.UpdateServiceChargeDef
	99,  // 65: moego.enterprise.price_book.v1.UpdateServiceChargeResult.service_charge:type_name -> moego.models.enterprise.v1.ServiceCharge
	101, // 66: moego.enterprise.price_book.v1.ListServiceChargesParams.filter:type_name -> moego.service.enterprise.v1.ListServiceChargesRequest.Filter
	99,  // 67: moego.enterprise.price_book.v1.ListServiceChargesResult.service_charges:type_name -> moego.models.enterprise.v1.ServiceCharge
	88,  // 68: moego.enterprise.price_book.v1.PushServiceChargeChangesParams.targets:type_name -> moego.models.enterprise.v1.TenantObject
	0,   // 69: moego.enterprise.price_book.v1.PriceBookService.ListPriceBooks:input_type -> moego.enterprise.price_book.v1.ListPriceBooksParams
	2,   // 70: moego.enterprise.price_book.v1.PriceBookService.CreatePriceBook:input_type -> moego.enterprise.price_book.v1.CreatePriceBookParams
	4,   // 71: moego.enterprise.price_book.v1.PriceBookService.UpdatePriceBook:input_type -> moego.enterprise.price_book.v1.UpdatePriceBookParams
	6,   // 72: moego.enterprise.price_book.v1.PriceBookService.DeletePriceBook:input_type -> moego.enterprise.price_book.v1.DeletePriceBookParams
	8,   // 73: moego.enterprise.price_book.v1.PriceBookService.DuplicatePriceBook:input_type -> moego.enterprise.price_book.v1.DuplicatePriceBookParams
	10,  // 74: moego.enterprise.price_book.v1.PriceBookService.SaveServiceCategories:input_type -> moego.enterprise.price_book.v1.SaveServiceCategoriesParams
	12,  // 75: moego.enterprise.price_book.v1.PriceBookService.ListServiceCategories:input_type -> moego.enterprise.price_book.v1.ListServiceCategoriesParams
	14,  // 76: moego.enterprise.price_book.v1.PriceBookService.ListPetBreeds:input_type -> moego.enterprise.price_book.v1.ListPetBreedsParams
	16,  // 77: moego.enterprise.price_book.v1.PriceBookService.ListPetTypes:input_type -> moego.enterprise.price_book.v1.ListPetTypesParams
	18,  // 78: moego.enterprise.price_book.v1.PriceBookService.CreateService:input_type -> moego.enterprise.price_book.v1.CreateServiceParams
	20,  // 79: moego.enterprise.price_book.v1.PriceBookService.GetService:input_type -> moego.enterprise.price_book.v1.GetServiceParams
	22,  // 80: moego.enterprise.price_book.v1.PriceBookService.ListServices:input_type -> moego.enterprise.price_book.v1.ListServicesParams
	24,  // 81: moego.enterprise.price_book.v1.PriceBookService.UpdateService:input_type -> moego.enterprise.price_book.v1.UpdateServiceParams
	26,  // 82: moego.enterprise.price_book.v1.PriceBookService.DeleteService:input_type -> moego.enterprise.price_book.v1.DeleteServiceParams
	28,  // 83: moego.enterprise.price_book.v1.PriceBookService.SortServices:input_type -> moego.enterprise.price_book.v1.SortServicesParams
	30,  // 84: moego.enterprise.price_book.v1.PriceBookService.ListServiceChangeHistories:input_type -> moego.enterprise.price_book.v1.ListServiceChangeHistoriesParams
	32,  // 85: moego.enterprise.price_book.v1.PriceBookService.ListServiceChanges:input_type -> moego.enterprise.price_book.v1.ListServiceChangesParams
	34,  // 86: moego.enterprise.price_book.v1.PriceBookService.PushServiceChanges:input_type -> moego.enterprise.price_book.v1.PushServiceChangesParams
	36,  // 87: moego.enterprise.price_book.v1.PriceBookService.CreateEvaluation:input_type -> moego.enterprise.price_book.v1.CreateEvaluationParams
	38,  // 88: moego.enterprise.price_book.v1.PriceBookService.UpdateEvaluation:input_type -> moego.enterprise.price_book.v1.UpdateEvaluationParams
	40,  // 89: moego.enterprise.price_book.v1.PriceBookService.ListEvaluations:input_type -> moego.enterprise.price_book.v1.ListEvaluationsParams
	44,  // 90: moego.enterprise.price_book.v1.PriceBookService.SortEvaluations:input_type -> moego.enterprise.price_book.v1.SortEvaluationsParams
	42,  // 91: moego.enterprise.price_book.v1.PriceBookService.PushEvaluationChanges:input_type -> moego.enterprise.price_book.v1.PushEvaluationChangesParams
	46,  // 92: moego.enterprise.price_book.v1.PriceBookService.CreatePricingRule:input_type -> moego.enterprise.price_book.v1.CreatePricingRuleParams
	48,  // 93: moego.enterprise.price_book.v1.PriceBookService.UpdatePricingRule:input_type -> moego.enterprise.price_book.v1.UpdatePricingRuleParams
	50,  // 94: moego.enterprise.price_book.v1.PriceBookService.ListPricingRules:input_type -> moego.enterprise.price_book.v1.ListPricingRulesParams
	52,  // 95: moego.enterprise.price_book.v1.PriceBookService.SortPricingRules:input_type -> moego.enterprise.price_book.v1.SortPricingRulesParams
	54,  // 96: moego.enterprise.price_book.v1.PriceBookService.PushPricingRuleChanges:input_type -> moego.enterprise.price_book.v1.PushPricingRuleChangesParams
	56,  // 97: moego.enterprise.price_book.v1.PriceBookService.CreateServiceCharge:input_type -> moego.enterprise.price_book.v1.CreateServiceChargeParams
	58,  // 98: moego.enterprise.price_book.v1.PriceBookService.UpdateServiceCharge:input_type -> moego.enterprise.price_book.v1.UpdateServiceChargeParams
	60,  // 99: moego.enterprise.price_book.v1.PriceBookService.ListServiceCharges:input_type -> moego.enterprise.price_book.v1.ListServiceChargesParams
	62,  // 100: moego.enterprise.price_book.v1.PriceBookService.SortServiceCharges:input_type -> moego.enterprise.price_book.v1.SortServiceChargesParams
	64,  // 101: moego.enterprise.price_book.v1.PriceBookService.PushServiceChargeChanges:input_type -> moego.enterprise.price_book.v1.PushServiceChargeChangesParams
	1,   // 102: moego.enterprise.price_book.v1.PriceBookService.ListPriceBooks:output_type -> moego.enterprise.price_book.v1.ListPriceBooksResult
	3,   // 103: moego.enterprise.price_book.v1.PriceBookService.CreatePriceBook:output_type -> moego.enterprise.price_book.v1.CreatePriceBookResult
	5,   // 104: moego.enterprise.price_book.v1.PriceBookService.UpdatePriceBook:output_type -> moego.enterprise.price_book.v1.UpdatePriceBookResult
	7,   // 105: moego.enterprise.price_book.v1.PriceBookService.DeletePriceBook:output_type -> moego.enterprise.price_book.v1.DeletePriceBookResult
	9,   // 106: moego.enterprise.price_book.v1.PriceBookService.DuplicatePriceBook:output_type -> moego.enterprise.price_book.v1.DuplicatePriceBookResult
	11,  // 107: moego.enterprise.price_book.v1.PriceBookService.SaveServiceCategories:output_type -> moego.enterprise.price_book.v1.SaveServiceCategoriesResult
	13,  // 108: moego.enterprise.price_book.v1.PriceBookService.ListServiceCategories:output_type -> moego.enterprise.price_book.v1.ListServiceCategoriesResult
	15,  // 109: moego.enterprise.price_book.v1.PriceBookService.ListPetBreeds:output_type -> moego.enterprise.price_book.v1.ListPetBreedsResult
	17,  // 110: moego.enterprise.price_book.v1.PriceBookService.ListPetTypes:output_type -> moego.enterprise.price_book.v1.ListPetTypesResult
	19,  // 111: moego.enterprise.price_book.v1.PriceBookService.CreateService:output_type -> moego.enterprise.price_book.v1.CreateServiceResult
	21,  // 112: moego.enterprise.price_book.v1.PriceBookService.GetService:output_type -> moego.enterprise.price_book.v1.GetServiceResult
	23,  // 113: moego.enterprise.price_book.v1.PriceBookService.ListServices:output_type -> moego.enterprise.price_book.v1.ListServicesResult
	25,  // 114: moego.enterprise.price_book.v1.PriceBookService.UpdateService:output_type -> moego.enterprise.price_book.v1.UpdateServiceResult
	27,  // 115: moego.enterprise.price_book.v1.PriceBookService.DeleteService:output_type -> moego.enterprise.price_book.v1.DeleteServiceResult
	29,  // 116: moego.enterprise.price_book.v1.PriceBookService.SortServices:output_type -> moego.enterprise.price_book.v1.SortServicesResult
	31,  // 117: moego.enterprise.price_book.v1.PriceBookService.ListServiceChangeHistories:output_type -> moego.enterprise.price_book.v1.ListServiceChangeHistoriesResult
	33,  // 118: moego.enterprise.price_book.v1.PriceBookService.ListServiceChanges:output_type -> moego.enterprise.price_book.v1.ListServiceChangesResult
	35,  // 119: moego.enterprise.price_book.v1.PriceBookService.PushServiceChanges:output_type -> moego.enterprise.price_book.v1.PushServiceChangesResult
	37,  // 120: moego.enterprise.price_book.v1.PriceBookService.CreateEvaluation:output_type -> moego.enterprise.price_book.v1.CreateEvaluationResult
	39,  // 121: moego.enterprise.price_book.v1.PriceBookService.UpdateEvaluation:output_type -> moego.enterprise.price_book.v1.UpdateEvaluationResult
	41,  // 122: moego.enterprise.price_book.v1.PriceBookService.ListEvaluations:output_type -> moego.enterprise.price_book.v1.ListEvaluationsResult
	45,  // 123: moego.enterprise.price_book.v1.PriceBookService.SortEvaluations:output_type -> moego.enterprise.price_book.v1.SortEvaluationsResult
	43,  // 124: moego.enterprise.price_book.v1.PriceBookService.PushEvaluationChanges:output_type -> moego.enterprise.price_book.v1.PushEvaluationChangesResult
	47,  // 125: moego.enterprise.price_book.v1.PriceBookService.CreatePricingRule:output_type -> moego.enterprise.price_book.v1.CreatePricingRuleResult
	49,  // 126: moego.enterprise.price_book.v1.PriceBookService.UpdatePricingRule:output_type -> moego.enterprise.price_book.v1.UpdatePricingRuleResult
	51,  // 127: moego.enterprise.price_book.v1.PriceBookService.ListPricingRules:output_type -> moego.enterprise.price_book.v1.ListPricingRulesResult
	53,  // 128: moego.enterprise.price_book.v1.PriceBookService.SortPricingRules:output_type -> moego.enterprise.price_book.v1.SortPricingRulesResult
	55,  // 129: moego.enterprise.price_book.v1.PriceBookService.PushPricingRuleChanges:output_type -> moego.enterprise.price_book.v1.PushPricingRuleChangesResult
	57,  // 130: moego.enterprise.price_book.v1.PriceBookService.CreateServiceCharge:output_type -> moego.enterprise.price_book.v1.CreateServiceChargeResult
	59,  // 131: moego.enterprise.price_book.v1.PriceBookService.UpdateServiceCharge:output_type -> moego.enterprise.price_book.v1.UpdateServiceChargeResult
	61,  // 132: moego.enterprise.price_book.v1.PriceBookService.ListServiceCharges:output_type -> moego.enterprise.price_book.v1.ListServiceChargesResult
	63,  // 133: moego.enterprise.price_book.v1.PriceBookService.SortServiceCharges:output_type -> moego.enterprise.price_book.v1.SortServiceChargesResult
	65,  // 134: moego.enterprise.price_book.v1.PriceBookService.PushServiceChargeChanges:output_type -> moego.enterprise.price_book.v1.PushServiceChargeChangesResult
	102, // [102:135] is the sub-list for method output_type
	69,  // [69:102] is the sub-list for method input_type
	69,  // [69:69] is the sub-list for extension type_name
	69,  // [69:69] is the sub-list for extension extendee
	0,   // [0:69] is the sub-list for field type_name
}

func init() { file_moego_enterprise_price_book_v1_price_book_api_proto_init() }
func file_moego_enterprise_price_book_v1_price_book_api_proto_init() {
	if File_moego_enterprise_price_book_v1_price_book_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPriceBooksParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPriceBooksResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreatePriceBookParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreatePriceBookResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdatePriceBookParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdatePriceBookResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeletePriceBookParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeletePriceBookResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DuplicatePriceBookParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DuplicatePriceBookResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SaveServiceCategoriesParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SaveServiceCategoriesResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListServiceCategoriesParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListServiceCategoriesResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPetBreedsParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPetBreedsResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPetTypesParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPetTypesResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateServiceParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateServiceResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetServiceParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetServiceResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListServicesParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListServicesResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateServiceParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateServiceResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteServiceParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteServiceResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SortServicesParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SortServicesResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListServiceChangeHistoriesParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListServiceChangeHistoriesResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListServiceChangesParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListServiceChangesResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PushServiceChangesParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PushServiceChangesResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateEvaluationParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateEvaluationResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateEvaluationParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateEvaluationResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListEvaluationsParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListEvaluationsResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PushEvaluationChangesParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PushEvaluationChangesResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SortEvaluationsParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SortEvaluationsResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreatePricingRuleParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreatePricingRuleResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdatePricingRuleParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdatePricingRuleResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPricingRulesParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListPricingRulesResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SortPricingRulesParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SortPricingRulesResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PushPricingRuleChangesParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[55].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PushPricingRuleChangesResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateServiceChargeParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[57].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateServiceChargeResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[58].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateServiceChargeParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[59].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateServiceChargeResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[60].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListServiceChargesParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[61].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListServiceChargesResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[62].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SortServiceChargesParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[63].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SortServiceChargesResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[64].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PushServiceChargeChangesParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[65].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PushServiceChargeChangesResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[4].OneofWrappers = []interface{}{}
	file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[24].OneofWrappers = []interface{}{}
	file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes[30].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_enterprise_price_book_v1_price_book_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   66,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_enterprise_price_book_v1_price_book_api_proto_goTypes,
		DependencyIndexes: file_moego_enterprise_price_book_v1_price_book_api_proto_depIdxs,
		MessageInfos:      file_moego_enterprise_price_book_v1_price_book_api_proto_msgTypes,
	}.Build()
	File_moego_enterprise_price_book_v1_price_book_api_proto = out.File
	file_moego_enterprise_price_book_v1_price_book_api_proto_rawDesc = nil
	file_moego_enterprise_price_book_v1_price_book_api_proto_goTypes = nil
	file_moego_enterprise_price_book_v1_price_book_api_proto_depIdxs = nil
}
