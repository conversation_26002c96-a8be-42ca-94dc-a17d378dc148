// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/enterprise/automation/v1/workflow_api.proto

package automationapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// WorkflowServiceClient is the client API for WorkflowService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type WorkflowServiceClient interface {
	// GetWorkflowConfig
	GetWorkflowConfig(ctx context.Context, in *GetWorkflowConfigParams, opts ...grpc.CallOption) (*GetWorkflowConfigResult, error)
	// CreateWorkflow
	CreateWorkflow(ctx context.Context, in *CreateWorkflowParams, opts ...grpc.CallOption) (*CreateWorkflowResult, error)
	// UpdateWorkflowContent
	UpdateWorkflowContent(ctx context.Context, in *UpdateWorkflowContentParams, opts ...grpc.CallOption) (*UpdateWorkflowContentResult, error)
	// UpdateWorkflowInfo
	UpdateWorkflowInfo(ctx context.Context, in *UpdateWorkflowInfoParams, opts ...grpc.CallOption) (*UpdateWorkflowInfoResult, error)
	// ListEnterpriseWorkflows
	ListEnterpriseWorkflows(ctx context.Context, in *ListEnterpriseWorkflowsParams, opts ...grpc.CallOption) (*ListEnterpriseWorkflowsResult, error)
	// GetWorkflowInfo
	GetWorkflowInfo(ctx context.Context, in *GetWorkflowInfoParams, opts ...grpc.CallOption) (*GetWorkflowInfoResult, error)
	// opt:FilterCustomer
	FilterCustomer(ctx context.Context, in *FilterCustomerParams, opts ...grpc.CallOption) (*FilterCustomerResult, error)
	// PushWorkflows
	PushWorkflows(ctx context.Context, in *PushWorkflowsParams, opts ...grpc.CallOption) (*PushWorkflowsResult, error)
}

type workflowServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewWorkflowServiceClient(cc grpc.ClientConnInterface) WorkflowServiceClient {
	return &workflowServiceClient{cc}
}

func (c *workflowServiceClient) GetWorkflowConfig(ctx context.Context, in *GetWorkflowConfigParams, opts ...grpc.CallOption) (*GetWorkflowConfigResult, error) {
	out := new(GetWorkflowConfigResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.automation.v1.WorkflowService/GetWorkflowConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *workflowServiceClient) CreateWorkflow(ctx context.Context, in *CreateWorkflowParams, opts ...grpc.CallOption) (*CreateWorkflowResult, error) {
	out := new(CreateWorkflowResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.automation.v1.WorkflowService/CreateWorkflow", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *workflowServiceClient) UpdateWorkflowContent(ctx context.Context, in *UpdateWorkflowContentParams, opts ...grpc.CallOption) (*UpdateWorkflowContentResult, error) {
	out := new(UpdateWorkflowContentResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.automation.v1.WorkflowService/UpdateWorkflowContent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *workflowServiceClient) UpdateWorkflowInfo(ctx context.Context, in *UpdateWorkflowInfoParams, opts ...grpc.CallOption) (*UpdateWorkflowInfoResult, error) {
	out := new(UpdateWorkflowInfoResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.automation.v1.WorkflowService/UpdateWorkflowInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *workflowServiceClient) ListEnterpriseWorkflows(ctx context.Context, in *ListEnterpriseWorkflowsParams, opts ...grpc.CallOption) (*ListEnterpriseWorkflowsResult, error) {
	out := new(ListEnterpriseWorkflowsResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.automation.v1.WorkflowService/ListEnterpriseWorkflows", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *workflowServiceClient) GetWorkflowInfo(ctx context.Context, in *GetWorkflowInfoParams, opts ...grpc.CallOption) (*GetWorkflowInfoResult, error) {
	out := new(GetWorkflowInfoResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.automation.v1.WorkflowService/GetWorkflowInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *workflowServiceClient) FilterCustomer(ctx context.Context, in *FilterCustomerParams, opts ...grpc.CallOption) (*FilterCustomerResult, error) {
	out := new(FilterCustomerResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.automation.v1.WorkflowService/FilterCustomer", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *workflowServiceClient) PushWorkflows(ctx context.Context, in *PushWorkflowsParams, opts ...grpc.CallOption) (*PushWorkflowsResult, error) {
	out := new(PushWorkflowsResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.automation.v1.WorkflowService/PushWorkflows", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// WorkflowServiceServer is the server API for WorkflowService service.
// All implementations must embed UnimplementedWorkflowServiceServer
// for forward compatibility
type WorkflowServiceServer interface {
	// GetWorkflowConfig
	GetWorkflowConfig(context.Context, *GetWorkflowConfigParams) (*GetWorkflowConfigResult, error)
	// CreateWorkflow
	CreateWorkflow(context.Context, *CreateWorkflowParams) (*CreateWorkflowResult, error)
	// UpdateWorkflowContent
	UpdateWorkflowContent(context.Context, *UpdateWorkflowContentParams) (*UpdateWorkflowContentResult, error)
	// UpdateWorkflowInfo
	UpdateWorkflowInfo(context.Context, *UpdateWorkflowInfoParams) (*UpdateWorkflowInfoResult, error)
	// ListEnterpriseWorkflows
	ListEnterpriseWorkflows(context.Context, *ListEnterpriseWorkflowsParams) (*ListEnterpriseWorkflowsResult, error)
	// GetWorkflowInfo
	GetWorkflowInfo(context.Context, *GetWorkflowInfoParams) (*GetWorkflowInfoResult, error)
	// opt:FilterCustomer
	FilterCustomer(context.Context, *FilterCustomerParams) (*FilterCustomerResult, error)
	// PushWorkflows
	PushWorkflows(context.Context, *PushWorkflowsParams) (*PushWorkflowsResult, error)
	mustEmbedUnimplementedWorkflowServiceServer()
}

// UnimplementedWorkflowServiceServer must be embedded to have forward compatible implementations.
type UnimplementedWorkflowServiceServer struct {
}

func (UnimplementedWorkflowServiceServer) GetWorkflowConfig(context.Context, *GetWorkflowConfigParams) (*GetWorkflowConfigResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWorkflowConfig not implemented")
}
func (UnimplementedWorkflowServiceServer) CreateWorkflow(context.Context, *CreateWorkflowParams) (*CreateWorkflowResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateWorkflow not implemented")
}
func (UnimplementedWorkflowServiceServer) UpdateWorkflowContent(context.Context, *UpdateWorkflowContentParams) (*UpdateWorkflowContentResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateWorkflowContent not implemented")
}
func (UnimplementedWorkflowServiceServer) UpdateWorkflowInfo(context.Context, *UpdateWorkflowInfoParams) (*UpdateWorkflowInfoResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateWorkflowInfo not implemented")
}
func (UnimplementedWorkflowServiceServer) ListEnterpriseWorkflows(context.Context, *ListEnterpriseWorkflowsParams) (*ListEnterpriseWorkflowsResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListEnterpriseWorkflows not implemented")
}
func (UnimplementedWorkflowServiceServer) GetWorkflowInfo(context.Context, *GetWorkflowInfoParams) (*GetWorkflowInfoResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWorkflowInfo not implemented")
}
func (UnimplementedWorkflowServiceServer) FilterCustomer(context.Context, *FilterCustomerParams) (*FilterCustomerResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FilterCustomer not implemented")
}
func (UnimplementedWorkflowServiceServer) PushWorkflows(context.Context, *PushWorkflowsParams) (*PushWorkflowsResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PushWorkflows not implemented")
}
func (UnimplementedWorkflowServiceServer) mustEmbedUnimplementedWorkflowServiceServer() {}

// UnsafeWorkflowServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to WorkflowServiceServer will
// result in compilation errors.
type UnsafeWorkflowServiceServer interface {
	mustEmbedUnimplementedWorkflowServiceServer()
}

func RegisterWorkflowServiceServer(s grpc.ServiceRegistrar, srv WorkflowServiceServer) {
	s.RegisterService(&WorkflowService_ServiceDesc, srv)
}

func _WorkflowService_GetWorkflowConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWorkflowConfigParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkflowServiceServer).GetWorkflowConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.automation.v1.WorkflowService/GetWorkflowConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkflowServiceServer).GetWorkflowConfig(ctx, req.(*GetWorkflowConfigParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _WorkflowService_CreateWorkflow_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateWorkflowParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkflowServiceServer).CreateWorkflow(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.automation.v1.WorkflowService/CreateWorkflow",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkflowServiceServer).CreateWorkflow(ctx, req.(*CreateWorkflowParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _WorkflowService_UpdateWorkflowContent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateWorkflowContentParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkflowServiceServer).UpdateWorkflowContent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.automation.v1.WorkflowService/UpdateWorkflowContent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkflowServiceServer).UpdateWorkflowContent(ctx, req.(*UpdateWorkflowContentParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _WorkflowService_UpdateWorkflowInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateWorkflowInfoParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkflowServiceServer).UpdateWorkflowInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.automation.v1.WorkflowService/UpdateWorkflowInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkflowServiceServer).UpdateWorkflowInfo(ctx, req.(*UpdateWorkflowInfoParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _WorkflowService_ListEnterpriseWorkflows_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListEnterpriseWorkflowsParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkflowServiceServer).ListEnterpriseWorkflows(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.automation.v1.WorkflowService/ListEnterpriseWorkflows",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkflowServiceServer).ListEnterpriseWorkflows(ctx, req.(*ListEnterpriseWorkflowsParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _WorkflowService_GetWorkflowInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWorkflowInfoParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkflowServiceServer).GetWorkflowInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.automation.v1.WorkflowService/GetWorkflowInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkflowServiceServer).GetWorkflowInfo(ctx, req.(*GetWorkflowInfoParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _WorkflowService_FilterCustomer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FilterCustomerParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkflowServiceServer).FilterCustomer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.automation.v1.WorkflowService/FilterCustomer",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkflowServiceServer).FilterCustomer(ctx, req.(*FilterCustomerParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _WorkflowService_PushWorkflows_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PushWorkflowsParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkflowServiceServer).PushWorkflows(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.automation.v1.WorkflowService/PushWorkflows",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkflowServiceServer).PushWorkflows(ctx, req.(*PushWorkflowsParams))
	}
	return interceptor(ctx, in, info, handler)
}

// WorkflowService_ServiceDesc is the grpc.ServiceDesc for WorkflowService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var WorkflowService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.enterprise.automation.v1.WorkflowService",
	HandlerType: (*WorkflowServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetWorkflowConfig",
			Handler:    _WorkflowService_GetWorkflowConfig_Handler,
		},
		{
			MethodName: "CreateWorkflow",
			Handler:    _WorkflowService_CreateWorkflow_Handler,
		},
		{
			MethodName: "UpdateWorkflowContent",
			Handler:    _WorkflowService_UpdateWorkflowContent_Handler,
		},
		{
			MethodName: "UpdateWorkflowInfo",
			Handler:    _WorkflowService_UpdateWorkflowInfo_Handler,
		},
		{
			MethodName: "ListEnterpriseWorkflows",
			Handler:    _WorkflowService_ListEnterpriseWorkflows_Handler,
		},
		{
			MethodName: "GetWorkflowInfo",
			Handler:    _WorkflowService_GetWorkflowInfo_Handler,
		},
		{
			MethodName: "FilterCustomer",
			Handler:    _WorkflowService_FilterCustomer_Handler,
		},
		{
			MethodName: "PushWorkflows",
			Handler:    _WorkflowService_PushWorkflows_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/enterprise/automation/v1/workflow_api.proto",
}
