// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/enterprise/automation/v1/workflow_api.proto

package automationapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/automation/v1"
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1"
	v12 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/enterprise/v1"
	v2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/reporting/v2"
	v21 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// GetWorkflowConfigParams
type GetWorkflowConfigParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetWorkflowConfigParams) Reset() {
	*x = GetWorkflowConfigParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_automation_v1_workflow_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWorkflowConfigParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWorkflowConfigParams) ProtoMessage() {}

func (x *GetWorkflowConfigParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_automation_v1_workflow_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWorkflowConfigParams.ProtoReflect.Descriptor instead.
func (*GetWorkflowConfigParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_automation_v1_workflow_api_proto_rawDescGZIP(), []int{0}
}

// GetWorkflowConfigResult
type GetWorkflowConfigResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// WorkflowConfig List
	WorkflowConfigs []*v1.WorkflowConfig `protobuf:"bytes,1,rep,name=workflow_configs,json=workflowConfigs,proto3" json:"workflow_configs,omitempty"`
	// Common Filters
	FilterGroups []*v2.FilterGroup `protobuf:"bytes,2,rep,name=filter_groups,json=filterGroups,proto3" json:"filter_groups,omitempty"`
}

func (x *GetWorkflowConfigResult) Reset() {
	*x = GetWorkflowConfigResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_automation_v1_workflow_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWorkflowConfigResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWorkflowConfigResult) ProtoMessage() {}

func (x *GetWorkflowConfigResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_automation_v1_workflow_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWorkflowConfigResult.ProtoReflect.Descriptor instead.
func (*GetWorkflowConfigResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_automation_v1_workflow_api_proto_rawDescGZIP(), []int{1}
}

func (x *GetWorkflowConfigResult) GetWorkflowConfigs() []*v1.WorkflowConfig {
	if x != nil {
		return x.WorkflowConfigs
	}
	return nil
}

func (x *GetWorkflowConfigResult) GetFilterGroups() []*v2.FilterGroup {
	if x != nil {
		return x.FilterGroups
	}
	return nil
}

// CreateWorkflowParams
type CreateWorkflowParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Workflow to be created
	Workflow *v1.CreateWorkflowDef `protobuf:"bytes,1,opt,name=workflow,proto3" json:"workflow,omitempty"`
}

func (x *CreateWorkflowParams) Reset() {
	*x = CreateWorkflowParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_automation_v1_workflow_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateWorkflowParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateWorkflowParams) ProtoMessage() {}

func (x *CreateWorkflowParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_automation_v1_workflow_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateWorkflowParams.ProtoReflect.Descriptor instead.
func (*CreateWorkflowParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_automation_v1_workflow_api_proto_rawDescGZIP(), []int{2}
}

func (x *CreateWorkflowParams) GetWorkflow() *v1.CreateWorkflowDef {
	if x != nil {
		return x.Workflow
	}
	return nil
}

// CreateWorkflowResult
type CreateWorkflowResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Workflow
	Workflow *v1.Workflow `protobuf:"bytes,1,opt,name=workflow,proto3" json:"workflow,omitempty"`
}

func (x *CreateWorkflowResult) Reset() {
	*x = CreateWorkflowResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_automation_v1_workflow_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateWorkflowResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateWorkflowResult) ProtoMessage() {}

func (x *CreateWorkflowResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_automation_v1_workflow_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateWorkflowResult.ProtoReflect.Descriptor instead.
func (*CreateWorkflowResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_automation_v1_workflow_api_proto_rawDescGZIP(), []int{3}
}

func (x *CreateWorkflowResult) GetWorkflow() *v1.Workflow {
	if x != nil {
		return x.Workflow
	}
	return nil
}

// UpdateWorkflowContentParams
type UpdateWorkflowContentParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Workflow ID
	WorkflowId int64 `protobuf:"varint,1,opt,name=workflow_id,json=workflowId,proto3" json:"workflow_id,omitempty"`
	// Steps to update
	Steps []*v1.CreateStepDef `protobuf:"bytes,2,rep,name=steps,proto3" json:"steps,omitempty"`
}

func (x *UpdateWorkflowContentParams) Reset() {
	*x = UpdateWorkflowContentParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_automation_v1_workflow_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateWorkflowContentParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateWorkflowContentParams) ProtoMessage() {}

func (x *UpdateWorkflowContentParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_automation_v1_workflow_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateWorkflowContentParams.ProtoReflect.Descriptor instead.
func (*UpdateWorkflowContentParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_automation_v1_workflow_api_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateWorkflowContentParams) GetWorkflowId() int64 {
	if x != nil {
		return x.WorkflowId
	}
	return 0
}

func (x *UpdateWorkflowContentParams) GetSteps() []*v1.CreateStepDef {
	if x != nil {
		return x.Steps
	}
	return nil
}

// UpdateWorkflowContentResult
type UpdateWorkflowContentResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Workflow
	Workflow *v1.Workflow `protobuf:"bytes,1,opt,name=workflow,proto3" json:"workflow,omitempty"`
}

func (x *UpdateWorkflowContentResult) Reset() {
	*x = UpdateWorkflowContentResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_automation_v1_workflow_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateWorkflowContentResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateWorkflowContentResult) ProtoMessage() {}

func (x *UpdateWorkflowContentResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_automation_v1_workflow_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateWorkflowContentResult.ProtoReflect.Descriptor instead.
func (*UpdateWorkflowContentResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_automation_v1_workflow_api_proto_rawDescGZIP(), []int{5}
}

func (x *UpdateWorkflowContentResult) GetWorkflow() *v1.Workflow {
	if x != nil {
		return x.Workflow
	}
	return nil
}

// UpdateWorkflowInfoParams
type UpdateWorkflowInfoParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Workflow ID
	WorkflowId int64 `protobuf:"varint,1,opt,name=workflow_id,json=workflowId,proto3" json:"workflow_id,omitempty"`
	// Workflow name
	Name *string `protobuf:"bytes,2,opt,name=name,proto3,oneof" json:"name,omitempty"`
	// Workflow description
	Desc *string `protobuf:"bytes,3,opt,name=desc,proto3,oneof" json:"desc,omitempty"`
	// Workflow status
	Status *v1.Workflow_Status `protobuf:"varint,4,opt,name=status,proto3,enum=moego.models.automation.v1.Workflow_Status,oneof" json:"status,omitempty"`
	// Workflow setting
	Setting *v1.WorkflowSetting `protobuf:"bytes,5,opt,name=setting,proto3,oneof" json:"setting,omitempty"`
	// enterprise apply to
	WorkflowEnterpriseApply *v1.WorkflowEnterpriseApply `protobuf:"bytes,6,opt,name=workflow_enterprise_apply,json=workflowEnterpriseApply,proto3,oneof" json:"workflow_enterprise_apply,omitempty"`
}

func (x *UpdateWorkflowInfoParams) Reset() {
	*x = UpdateWorkflowInfoParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_automation_v1_workflow_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateWorkflowInfoParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateWorkflowInfoParams) ProtoMessage() {}

func (x *UpdateWorkflowInfoParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_automation_v1_workflow_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateWorkflowInfoParams.ProtoReflect.Descriptor instead.
func (*UpdateWorkflowInfoParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_automation_v1_workflow_api_proto_rawDescGZIP(), []int{6}
}

func (x *UpdateWorkflowInfoParams) GetWorkflowId() int64 {
	if x != nil {
		return x.WorkflowId
	}
	return 0
}

func (x *UpdateWorkflowInfoParams) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *UpdateWorkflowInfoParams) GetDesc() string {
	if x != nil && x.Desc != nil {
		return *x.Desc
	}
	return ""
}

func (x *UpdateWorkflowInfoParams) GetStatus() v1.Workflow_Status {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return v1.Workflow_Status(0)
}

func (x *UpdateWorkflowInfoParams) GetSetting() *v1.WorkflowSetting {
	if x != nil {
		return x.Setting
	}
	return nil
}

func (x *UpdateWorkflowInfoParams) GetWorkflowEnterpriseApply() *v1.WorkflowEnterpriseApply {
	if x != nil {
		return x.WorkflowEnterpriseApply
	}
	return nil
}

// UpdateWorkflowInfoResult
type UpdateWorkflowInfoResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Workflow
	Workflow *v1.Workflow `protobuf:"bytes,1,opt,name=workflow,proto3" json:"workflow,omitempty"`
}

func (x *UpdateWorkflowInfoResult) Reset() {
	*x = UpdateWorkflowInfoResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_automation_v1_workflow_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateWorkflowInfoResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateWorkflowInfoResult) ProtoMessage() {}

func (x *UpdateWorkflowInfoResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_automation_v1_workflow_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateWorkflowInfoResult.ProtoReflect.Descriptor instead.
func (*UpdateWorkflowInfoResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_automation_v1_workflow_api_proto_rawDescGZIP(), []int{7}
}

func (x *UpdateWorkflowInfoResult) GetWorkflow() *v1.Workflow {
	if x != nil {
		return x.Workflow
	}
	return nil
}

// ListEnterpriseWorkflowsParams
type ListEnterpriseWorkflowsParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Pagination
	Pagination *v21.PaginationRequest `protobuf:"bytes,1,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
	// Filter
	Filter *ListEnterpriseWorkflowsParams_Filter `protobuf:"bytes,2,opt,name=filter,proto3,oneof" json:"filter,omitempty"`
}

func (x *ListEnterpriseWorkflowsParams) Reset() {
	*x = ListEnterpriseWorkflowsParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_automation_v1_workflow_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListEnterpriseWorkflowsParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListEnterpriseWorkflowsParams) ProtoMessage() {}

func (x *ListEnterpriseWorkflowsParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_automation_v1_workflow_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListEnterpriseWorkflowsParams.ProtoReflect.Descriptor instead.
func (*ListEnterpriseWorkflowsParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_automation_v1_workflow_api_proto_rawDescGZIP(), []int{8}
}

func (x *ListEnterpriseWorkflowsParams) GetPagination() *v21.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListEnterpriseWorkflowsParams) GetFilter() *ListEnterpriseWorkflowsParams_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

// ListEnterpriseWorkflowsResult
type ListEnterpriseWorkflowsResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Workflows
	Workflows []*v1.Workflow `protobuf:"bytes,1,rep,name=workflows,proto3" json:"workflows,omitempty"`
	// Pagination
	Pagination *v21.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
}

func (x *ListEnterpriseWorkflowsResult) Reset() {
	*x = ListEnterpriseWorkflowsResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_automation_v1_workflow_api_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListEnterpriseWorkflowsResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListEnterpriseWorkflowsResult) ProtoMessage() {}

func (x *ListEnterpriseWorkflowsResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_automation_v1_workflow_api_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListEnterpriseWorkflowsResult.ProtoReflect.Descriptor instead.
func (*ListEnterpriseWorkflowsResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_automation_v1_workflow_api_proto_rawDescGZIP(), []int{9}
}

func (x *ListEnterpriseWorkflowsResult) GetWorkflows() []*v1.Workflow {
	if x != nil {
		return x.Workflows
	}
	return nil
}

func (x *ListEnterpriseWorkflowsResult) GetPagination() *v21.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// GetWorkflowInfoParams
type GetWorkflowInfoParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Workflow ID
	WorkflowId int64 `protobuf:"varint,1,opt,name=workflow_id,json=workflowId,proto3" json:"workflow_id,omitempty"`
}

func (x *GetWorkflowInfoParams) Reset() {
	*x = GetWorkflowInfoParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_automation_v1_workflow_api_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWorkflowInfoParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWorkflowInfoParams) ProtoMessage() {}

func (x *GetWorkflowInfoParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_automation_v1_workflow_api_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWorkflowInfoParams.ProtoReflect.Descriptor instead.
func (*GetWorkflowInfoParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_automation_v1_workflow_api_proto_rawDescGZIP(), []int{10}
}

func (x *GetWorkflowInfoParams) GetWorkflowId() int64 {
	if x != nil {
		return x.WorkflowId
	}
	return 0
}

// GetWorkflowInfoResult
type GetWorkflowInfoResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Workflow
	Workflow *v1.Workflow `protobuf:"bytes,1,opt,name=workflow,proto3" json:"workflow,omitempty"`
}

func (x *GetWorkflowInfoResult) Reset() {
	*x = GetWorkflowInfoResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_automation_v1_workflow_api_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWorkflowInfoResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWorkflowInfoResult) ProtoMessage() {}

func (x *GetWorkflowInfoResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_automation_v1_workflow_api_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWorkflowInfoResult.ProtoReflect.Descriptor instead.
func (*GetWorkflowInfoResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_automation_v1_workflow_api_proto_rawDescGZIP(), []int{11}
}

func (x *GetWorkflowInfoResult) GetWorkflow() *v1.Workflow {
	if x != nil {
		return x.Workflow
	}
	return nil
}

// FilterCustomerParams
type FilterCustomerParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Filter requests
	Filters []*v2.FilterRequest `protobuf:"bytes,1,rep,name=filters,proto3" json:"filters,omitempty"`
	// Pagination
	Pagination *v21.PaginationRequest `protobuf:"bytes,2,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
	// Tenants IDs, empty is all tenants
	TenantsIds []int64 `protobuf:"varint,3,rep,packed,name=tenants_ids,json=tenantsIds,proto3" json:"tenants_ids,omitempty"`
	// Customer ID filter
	CustomerId *int64 `protobuf:"varint,10,opt,name=customer_id,json=customerId,proto3,oneof" json:"customer_id,omitempty"`
}

func (x *FilterCustomerParams) Reset() {
	*x = FilterCustomerParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_automation_v1_workflow_api_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FilterCustomerParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FilterCustomerParams) ProtoMessage() {}

func (x *FilterCustomerParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_automation_v1_workflow_api_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FilterCustomerParams.ProtoReflect.Descriptor instead.
func (*FilterCustomerParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_automation_v1_workflow_api_proto_rawDescGZIP(), []int{12}
}

func (x *FilterCustomerParams) GetFilters() []*v2.FilterRequest {
	if x != nil {
		return x.Filters
	}
	return nil
}

func (x *FilterCustomerParams) GetPagination() *v21.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *FilterCustomerParams) GetTenantsIds() []int64 {
	if x != nil {
		return x.TenantsIds
	}
	return nil
}

func (x *FilterCustomerParams) GetCustomerId() int64 {
	if x != nil && x.CustomerId != nil {
		return *x.CustomerId
	}
	return 0
}

// FilterCustomerResult
type FilterCustomerResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Customers
	Customer []*v11.BusinessCustomerInfoModel `protobuf:"bytes,1,rep,name=customer,proto3" json:"customer,omitempty"`
	// Pagination
	Pagination *v21.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"`
}

func (x *FilterCustomerResult) Reset() {
	*x = FilterCustomerResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_automation_v1_workflow_api_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FilterCustomerResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FilterCustomerResult) ProtoMessage() {}

func (x *FilterCustomerResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_automation_v1_workflow_api_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FilterCustomerResult.ProtoReflect.Descriptor instead.
func (*FilterCustomerResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_automation_v1_workflow_api_proto_rawDescGZIP(), []int{13}
}

func (x *FilterCustomerResult) GetCustomer() []*v11.BusinessCustomerInfoModel {
	if x != nil {
		return x.Customer
	}
	return nil
}

func (x *FilterCustomerResult) GetPagination() *v21.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// PushWorkflowsParams
type PushWorkflowsParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// workflow ids
	Ids []int64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	// tenant objects
	Targets []*v12.TenantObject `protobuf:"bytes,2,rep,name=targets,proto3" json:"targets,omitempty"`
}

func (x *PushWorkflowsParams) Reset() {
	*x = PushWorkflowsParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_automation_v1_workflow_api_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PushWorkflowsParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushWorkflowsParams) ProtoMessage() {}

func (x *PushWorkflowsParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_automation_v1_workflow_api_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushWorkflowsParams.ProtoReflect.Descriptor instead.
func (*PushWorkflowsParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_automation_v1_workflow_api_proto_rawDescGZIP(), []int{14}
}

func (x *PushWorkflowsParams) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *PushWorkflowsParams) GetTargets() []*v12.TenantObject {
	if x != nil {
		return x.Targets
	}
	return nil
}

// PushWorkflowsResult
type PushWorkflowsResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// success
	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
}

func (x *PushWorkflowsResult) Reset() {
	*x = PushWorkflowsResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_automation_v1_workflow_api_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PushWorkflowsResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushWorkflowsResult) ProtoMessage() {}

func (x *PushWorkflowsResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_automation_v1_workflow_api_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushWorkflowsResult.ProtoReflect.Descriptor instead.
func (*PushWorkflowsResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_automation_v1_workflow_api_proto_rawDescGZIP(), []int{15}
}

func (x *PushWorkflowsResult) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

// Filter
type ListEnterpriseWorkflowsParams_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Workflow name filter
	Name *string `protobuf:"bytes,1,opt,name=name,proto3,oneof" json:"name,omitempty"`
	// Workflow status filter
	Status []v1.Workflow_Status `protobuf:"varint,2,rep,packed,name=status,proto3,enum=moego.models.automation.v1.Workflow_Status" json:"status,omitempty"`
	// Tenant Group IDs filter
	TenantsGroupIds []int64 `protobuf:"varint,3,rep,packed,name=tenants_group_ids,json=tenantsGroupIds,proto3" json:"tenants_group_ids,omitempty"`
	// Tenant IDs filter
	TenantsIds []int64 `protobuf:"varint,4,rep,packed,name=tenants_ids,json=tenantsIds,proto3" json:"tenants_ids,omitempty"`
}

func (x *ListEnterpriseWorkflowsParams_Filter) Reset() {
	*x = ListEnterpriseWorkflowsParams_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_automation_v1_workflow_api_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListEnterpriseWorkflowsParams_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListEnterpriseWorkflowsParams_Filter) ProtoMessage() {}

func (x *ListEnterpriseWorkflowsParams_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_automation_v1_workflow_api_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListEnterpriseWorkflowsParams_Filter.ProtoReflect.Descriptor instead.
func (*ListEnterpriseWorkflowsParams_Filter) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_automation_v1_workflow_api_proto_rawDescGZIP(), []int{8, 0}
}

func (x *ListEnterpriseWorkflowsParams_Filter) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *ListEnterpriseWorkflowsParams_Filter) GetStatus() []v1.Workflow_Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *ListEnterpriseWorkflowsParams_Filter) GetTenantsGroupIds() []int64 {
	if x != nil {
		return x.TenantsGroupIds
	}
	return nil
}

func (x *ListEnterpriseWorkflowsParams_Filter) GetTenantsIds() []int64 {
	if x != nil {
		return x.TenantsIds
	}
	return nil
}

var File_moego_enterprise_automation_v1_workflow_api_proto protoreflect.FileDescriptor

var file_moego_enterprise_automation_v1_workflow_api_proto_rawDesc = []byte{
	0x0a, 0x31, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x2f, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31,
	0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x1e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x1a, 0x29, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2f, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f,
	0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x61, 0x75, 0x74,
	0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x40,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x62, 0x75, 0x73,
	0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2f, 0x76,
	0x31, 0x2f, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x65, 0x6e,
	0x61, 0x6e, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x2c, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2c,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x32, 0x2f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2f, 0x76, 0x32, 0x2f, 0x70, 0x61, 0x67,
	0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x19, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x57, 0x6f, 0x72,
	0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x22, 0xbd, 0x01, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f,
	0x77, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x55, 0x0a,
	0x10, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x52, 0x0f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x73, 0x12, 0x4b, 0x0a, 0x0d, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x5f, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x52, 0x0c, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x73, 0x22, 0x61, 0x0a, 0x14, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x49, 0x0a, 0x08, 0x77, 0x6f, 0x72,
	0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57,
	0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x44, 0x65, 0x66, 0x52, 0x08, 0x77, 0x6f, 0x72, 0x6b,
	0x66, 0x6c, 0x6f, 0x77, 0x22, 0x58, 0x0a, 0x14, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57, 0x6f,
	0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x40, 0x0a, 0x08,
	0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75,
	0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b,
	0x66, 0x6c, 0x6f, 0x77, 0x52, 0x08, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x22, 0x7f,
	0x0a, 0x1b, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77,
	0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x1f, 0x0a,
	0x0b, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0a, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49, 0x64, 0x12, 0x3f,
	0x0a, 0x05, 0x73, 0x74, 0x65, 0x70, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74,
	0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x53, 0x74, 0x65, 0x70, 0x44, 0x65, 0x66, 0x52, 0x05, 0x73, 0x74, 0x65, 0x70, 0x73, 0x22,
	0x5f, 0x0a, 0x1b, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f,
	0x77, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x40,
	0x0a, 0x08, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x6f,
	0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x08, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77,
	0x22, 0xc0, 0x03, 0x0a, 0x18, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x49, 0x6e, 0x66, 0x6f, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x1f, 0x0a,
	0x0b, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0a, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49, 0x64, 0x12, 0x17,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x17, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x88, 0x01, 0x01,
	0x12, 0x48, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x6f,
	0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x48, 0x02, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x88, 0x01, 0x01, 0x12, 0x4a, 0x0a, 0x07, 0x73, 0x65,
	0x74, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f,
	0x77, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x48, 0x03, 0x52, 0x07, 0x73, 0x65, 0x74, 0x74,
	0x69, 0x6e, 0x67, 0x88, 0x01, 0x01, 0x12, 0x74, 0x0a, 0x19, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c,
	0x6f, 0x77, 0x5f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x61, 0x70,
	0x70, 0x6c, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x45,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x48, 0x04,
	0x52, 0x17, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x88, 0x01, 0x01, 0x42, 0x07, 0x0a, 0x05,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x42, 0x09,
	0x0a, 0x07, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x73, 0x65,
	0x74, 0x74, 0x69, 0x6e, 0x67, 0x42, 0x1c, 0x0a, 0x1a, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c,
	0x6f, 0x77, 0x5f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x61, 0x70,
	0x70, 0x6c, 0x79, 0x22, 0x5c, 0x0a, 0x18, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72,
	0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x40, 0x0a, 0x08, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x57,
	0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x08, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f,
	0x77, 0x22, 0xa3, 0x03, 0x0a, 0x1d, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x73, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x12, 0x46, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x0a, 0x70, 0x61,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x61, 0x0a, 0x06, 0x66,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x44, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x61,
	0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x48, 0x01, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x88, 0x01, 0x01, 0x1a, 0xbc,
	0x01, 0x0a, 0x06, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x17, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x88,
	0x01, 0x01, 0x12, 0x43, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2a, 0x0a, 0x11, 0x74, 0x65, 0x6e, 0x61, 0x6e,
	0x74, 0x73, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x03, 0x52, 0x0f, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x73, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x49, 0x64, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x73, 0x5f, 0x69,
	0x64, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0a, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74,
	0x73, 0x49, 0x64, 0x73, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x0d, 0x0a,
	0x0b, 0x5f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x09, 0x0a, 0x07,
	0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x22, 0xbb, 0x01, 0x0a, 0x1d, 0x4c, 0x69, 0x73, 0x74,
	0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c,
	0x6f, 0x77, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x42, 0x0a, 0x09, 0x77, 0x6f, 0x72,
	0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f,
	0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c,
	0x6f, 0x77, 0x52, 0x09, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x73, 0x12, 0x47, 0x0a,
	0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x22, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e,
	0x76, 0x32, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x00, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x70, 0x61, 0x67, 0x69, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x38, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x6b,
	0x66, 0x6c, 0x6f, 0x77, 0x49, 0x6e, 0x66, 0x6f, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x1f,
	0x0a, 0x0b, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0a, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49, 0x64, 0x22,
	0x59, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x40, 0x0a, 0x08, 0x77, 0x6f, 0x72, 0x6b,
	0x66, 0x6c, 0x6f, 0x77, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77,
	0x52, 0x08, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x22, 0x88, 0x02, 0x0a, 0x14, 0x46,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x12, 0x42, 0x0a, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32,
	0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x07,
	0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x12, 0x46, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61, 0x67,
	0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00,
	0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x12,
	0x1f, 0x0a, 0x0b, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x73, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x03, 0x52, 0x0a, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x73, 0x49, 0x64, 0x73,
	0x12, 0x24, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x03, 0x48, 0x01, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x49, 0x64, 0x88, 0x01, 0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x70, 0x61, 0x67, 0x69, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x22, 0xc8, 0x01, 0x0a, 0x14, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x58,
	0x0a, 0x08, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x08,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x12, 0x47, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x73, 0x2e, 0x76, 0x32, 0x2e, 0x50, 0x61,
	0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x48, 0x00, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01,
	0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x22, 0x6b, 0x0a, 0x13, 0x50, 0x75, 0x73, 0x68, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77,
	0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x03, 0x52, 0x03, 0x69, 0x64, 0x73, 0x12, 0x42, 0x0a, 0x07, 0x74, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x4f, 0x62,
	0x6a, 0x65, 0x63, 0x74, 0x52, 0x07, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x73, 0x22, 0x2f, 0x0a,
	0x13, 0x50, 0x75, 0x73, 0x68, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x73, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x32, 0xca,
	0x08, 0x0a, 0x0f, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x12, 0x85, 0x01, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c,
	0x6f, 0x77, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x61, 0x75, 0x74, 0x6f,
	0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x57, 0x6f, 0x72,
	0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x1a, 0x37, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x7c, 0x0a, 0x0e, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x12, 0x34, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e,
	0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x1a, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c,
	0x6f, 0x77, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x91, 0x01, 0x0a, 0x15, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x43, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x12, 0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c,
	0x6f, 0x77, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a,
	0x3b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31,
	0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x43,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x88, 0x01, 0x0a,
	0x12, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x49, 0x6e, 0x66, 0x6f, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x38, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x97, 0x01, 0x0a, 0x17, 0x4c, 0x69, 0x73, 0x74,
	0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c,
	0x6f, 0x77, 0x73, 0x12, 0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x73, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x1a, 0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x7f, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f,
	0x77, 0x49, 0x6e, 0x66, 0x6f, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x35, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x61,
	0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x12, 0x7c, 0x0a, 0x0e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x43, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x12, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x43, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x34, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x61, 0x75,
	0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x79, 0x0a, 0x0d, 0x50, 0x75, 0x73, 0x68, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77,
	0x73, 0x12, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x50, 0x75, 0x73, 0x68, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x73,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x75, 0x73, 0x68, 0x57, 0x6f, 0x72, 0x6b,
	0x66, 0x6c, 0x6f, 0x77, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x42, 0x8f, 0x01, 0x0a, 0x26,
	0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x63, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72,
	0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69,
	0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f,
	0x61, 0x75, 0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x3b, 0x61, 0x75,
	0x74, 0x6f, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x70, 0x69, 0x70, 0x62, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_enterprise_automation_v1_workflow_api_proto_rawDescOnce sync.Once
	file_moego_enterprise_automation_v1_workflow_api_proto_rawDescData = file_moego_enterprise_automation_v1_workflow_api_proto_rawDesc
)

func file_moego_enterprise_automation_v1_workflow_api_proto_rawDescGZIP() []byte {
	file_moego_enterprise_automation_v1_workflow_api_proto_rawDescOnce.Do(func() {
		file_moego_enterprise_automation_v1_workflow_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_enterprise_automation_v1_workflow_api_proto_rawDescData)
	})
	return file_moego_enterprise_automation_v1_workflow_api_proto_rawDescData
}

var file_moego_enterprise_automation_v1_workflow_api_proto_msgTypes = make([]protoimpl.MessageInfo, 17)
var file_moego_enterprise_automation_v1_workflow_api_proto_goTypes = []interface{}{
	(*GetWorkflowConfigParams)(nil),              // 0: moego.enterprise.automation.v1.GetWorkflowConfigParams
	(*GetWorkflowConfigResult)(nil),              // 1: moego.enterprise.automation.v1.GetWorkflowConfigResult
	(*CreateWorkflowParams)(nil),                 // 2: moego.enterprise.automation.v1.CreateWorkflowParams
	(*CreateWorkflowResult)(nil),                 // 3: moego.enterprise.automation.v1.CreateWorkflowResult
	(*UpdateWorkflowContentParams)(nil),          // 4: moego.enterprise.automation.v1.UpdateWorkflowContentParams
	(*UpdateWorkflowContentResult)(nil),          // 5: moego.enterprise.automation.v1.UpdateWorkflowContentResult
	(*UpdateWorkflowInfoParams)(nil),             // 6: moego.enterprise.automation.v1.UpdateWorkflowInfoParams
	(*UpdateWorkflowInfoResult)(nil),             // 7: moego.enterprise.automation.v1.UpdateWorkflowInfoResult
	(*ListEnterpriseWorkflowsParams)(nil),        // 8: moego.enterprise.automation.v1.ListEnterpriseWorkflowsParams
	(*ListEnterpriseWorkflowsResult)(nil),        // 9: moego.enterprise.automation.v1.ListEnterpriseWorkflowsResult
	(*GetWorkflowInfoParams)(nil),                // 10: moego.enterprise.automation.v1.GetWorkflowInfoParams
	(*GetWorkflowInfoResult)(nil),                // 11: moego.enterprise.automation.v1.GetWorkflowInfoResult
	(*FilterCustomerParams)(nil),                 // 12: moego.enterprise.automation.v1.FilterCustomerParams
	(*FilterCustomerResult)(nil),                 // 13: moego.enterprise.automation.v1.FilterCustomerResult
	(*PushWorkflowsParams)(nil),                  // 14: moego.enterprise.automation.v1.PushWorkflowsParams
	(*PushWorkflowsResult)(nil),                  // 15: moego.enterprise.automation.v1.PushWorkflowsResult
	(*ListEnterpriseWorkflowsParams_Filter)(nil), // 16: moego.enterprise.automation.v1.ListEnterpriseWorkflowsParams.Filter
	(*v1.WorkflowConfig)(nil),                    // 17: moego.models.automation.v1.WorkflowConfig
	(*v2.FilterGroup)(nil),                       // 18: moego.models.reporting.v2.FilterGroup
	(*v1.CreateWorkflowDef)(nil),                 // 19: moego.models.automation.v1.CreateWorkflowDef
	(*v1.Workflow)(nil),                          // 20: moego.models.automation.v1.Workflow
	(*v1.CreateStepDef)(nil),                     // 21: moego.models.automation.v1.CreateStepDef
	(v1.Workflow_Status)(0),                      // 22: moego.models.automation.v1.Workflow.Status
	(*v1.WorkflowSetting)(nil),                   // 23: moego.models.automation.v1.WorkflowSetting
	(*v1.WorkflowEnterpriseApply)(nil),           // 24: moego.models.automation.v1.WorkflowEnterpriseApply
	(*v21.PaginationRequest)(nil),                // 25: moego.utils.v2.PaginationRequest
	(*v21.PaginationResponse)(nil),               // 26: moego.utils.v2.PaginationResponse
	(*v2.FilterRequest)(nil),                     // 27: moego.models.reporting.v2.FilterRequest
	(*v11.BusinessCustomerInfoModel)(nil),        // 28: moego.models.business_customer.v1.BusinessCustomerInfoModel
	(*v12.TenantObject)(nil),                     // 29: moego.models.enterprise.v1.TenantObject
}
var file_moego_enterprise_automation_v1_workflow_api_proto_depIdxs = []int32{
	17, // 0: moego.enterprise.automation.v1.GetWorkflowConfigResult.workflow_configs:type_name -> moego.models.automation.v1.WorkflowConfig
	18, // 1: moego.enterprise.automation.v1.GetWorkflowConfigResult.filter_groups:type_name -> moego.models.reporting.v2.FilterGroup
	19, // 2: moego.enterprise.automation.v1.CreateWorkflowParams.workflow:type_name -> moego.models.automation.v1.CreateWorkflowDef
	20, // 3: moego.enterprise.automation.v1.CreateWorkflowResult.workflow:type_name -> moego.models.automation.v1.Workflow
	21, // 4: moego.enterprise.automation.v1.UpdateWorkflowContentParams.steps:type_name -> moego.models.automation.v1.CreateStepDef
	20, // 5: moego.enterprise.automation.v1.UpdateWorkflowContentResult.workflow:type_name -> moego.models.automation.v1.Workflow
	22, // 6: moego.enterprise.automation.v1.UpdateWorkflowInfoParams.status:type_name -> moego.models.automation.v1.Workflow.Status
	23, // 7: moego.enterprise.automation.v1.UpdateWorkflowInfoParams.setting:type_name -> moego.models.automation.v1.WorkflowSetting
	24, // 8: moego.enterprise.automation.v1.UpdateWorkflowInfoParams.workflow_enterprise_apply:type_name -> moego.models.automation.v1.WorkflowEnterpriseApply
	20, // 9: moego.enterprise.automation.v1.UpdateWorkflowInfoResult.workflow:type_name -> moego.models.automation.v1.Workflow
	25, // 10: moego.enterprise.automation.v1.ListEnterpriseWorkflowsParams.pagination:type_name -> moego.utils.v2.PaginationRequest
	16, // 11: moego.enterprise.automation.v1.ListEnterpriseWorkflowsParams.filter:type_name -> moego.enterprise.automation.v1.ListEnterpriseWorkflowsParams.Filter
	20, // 12: moego.enterprise.automation.v1.ListEnterpriseWorkflowsResult.workflows:type_name -> moego.models.automation.v1.Workflow
	26, // 13: moego.enterprise.automation.v1.ListEnterpriseWorkflowsResult.pagination:type_name -> moego.utils.v2.PaginationResponse
	20, // 14: moego.enterprise.automation.v1.GetWorkflowInfoResult.workflow:type_name -> moego.models.automation.v1.Workflow
	27, // 15: moego.enterprise.automation.v1.FilterCustomerParams.filters:type_name -> moego.models.reporting.v2.FilterRequest
	25, // 16: moego.enterprise.automation.v1.FilterCustomerParams.pagination:type_name -> moego.utils.v2.PaginationRequest
	28, // 17: moego.enterprise.automation.v1.FilterCustomerResult.customer:type_name -> moego.models.business_customer.v1.BusinessCustomerInfoModel
	26, // 18: moego.enterprise.automation.v1.FilterCustomerResult.pagination:type_name -> moego.utils.v2.PaginationResponse
	29, // 19: moego.enterprise.automation.v1.PushWorkflowsParams.targets:type_name -> moego.models.enterprise.v1.TenantObject
	22, // 20: moego.enterprise.automation.v1.ListEnterpriseWorkflowsParams.Filter.status:type_name -> moego.models.automation.v1.Workflow.Status
	0,  // 21: moego.enterprise.automation.v1.WorkflowService.GetWorkflowConfig:input_type -> moego.enterprise.automation.v1.GetWorkflowConfigParams
	2,  // 22: moego.enterprise.automation.v1.WorkflowService.CreateWorkflow:input_type -> moego.enterprise.automation.v1.CreateWorkflowParams
	4,  // 23: moego.enterprise.automation.v1.WorkflowService.UpdateWorkflowContent:input_type -> moego.enterprise.automation.v1.UpdateWorkflowContentParams
	6,  // 24: moego.enterprise.automation.v1.WorkflowService.UpdateWorkflowInfo:input_type -> moego.enterprise.automation.v1.UpdateWorkflowInfoParams
	8,  // 25: moego.enterprise.automation.v1.WorkflowService.ListEnterpriseWorkflows:input_type -> moego.enterprise.automation.v1.ListEnterpriseWorkflowsParams
	10, // 26: moego.enterprise.automation.v1.WorkflowService.GetWorkflowInfo:input_type -> moego.enterprise.automation.v1.GetWorkflowInfoParams
	12, // 27: moego.enterprise.automation.v1.WorkflowService.FilterCustomer:input_type -> moego.enterprise.automation.v1.FilterCustomerParams
	14, // 28: moego.enterprise.automation.v1.WorkflowService.PushWorkflows:input_type -> moego.enterprise.automation.v1.PushWorkflowsParams
	1,  // 29: moego.enterprise.automation.v1.WorkflowService.GetWorkflowConfig:output_type -> moego.enterprise.automation.v1.GetWorkflowConfigResult
	3,  // 30: moego.enterprise.automation.v1.WorkflowService.CreateWorkflow:output_type -> moego.enterprise.automation.v1.CreateWorkflowResult
	5,  // 31: moego.enterprise.automation.v1.WorkflowService.UpdateWorkflowContent:output_type -> moego.enterprise.automation.v1.UpdateWorkflowContentResult
	7,  // 32: moego.enterprise.automation.v1.WorkflowService.UpdateWorkflowInfo:output_type -> moego.enterprise.automation.v1.UpdateWorkflowInfoResult
	9,  // 33: moego.enterprise.automation.v1.WorkflowService.ListEnterpriseWorkflows:output_type -> moego.enterprise.automation.v1.ListEnterpriseWorkflowsResult
	11, // 34: moego.enterprise.automation.v1.WorkflowService.GetWorkflowInfo:output_type -> moego.enterprise.automation.v1.GetWorkflowInfoResult
	13, // 35: moego.enterprise.automation.v1.WorkflowService.FilterCustomer:output_type -> moego.enterprise.automation.v1.FilterCustomerResult
	15, // 36: moego.enterprise.automation.v1.WorkflowService.PushWorkflows:output_type -> moego.enterprise.automation.v1.PushWorkflowsResult
	29, // [29:37] is the sub-list for method output_type
	21, // [21:29] is the sub-list for method input_type
	21, // [21:21] is the sub-list for extension type_name
	21, // [21:21] is the sub-list for extension extendee
	0,  // [0:21] is the sub-list for field type_name
}

func init() { file_moego_enterprise_automation_v1_workflow_api_proto_init() }
func file_moego_enterprise_automation_v1_workflow_api_proto_init() {
	if File_moego_enterprise_automation_v1_workflow_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_enterprise_automation_v1_workflow_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetWorkflowConfigParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_automation_v1_workflow_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetWorkflowConfigResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_automation_v1_workflow_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateWorkflowParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_automation_v1_workflow_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateWorkflowResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_automation_v1_workflow_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateWorkflowContentParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_automation_v1_workflow_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateWorkflowContentResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_automation_v1_workflow_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateWorkflowInfoParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_automation_v1_workflow_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateWorkflowInfoResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_automation_v1_workflow_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListEnterpriseWorkflowsParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_automation_v1_workflow_api_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListEnterpriseWorkflowsResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_automation_v1_workflow_api_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetWorkflowInfoParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_automation_v1_workflow_api_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetWorkflowInfoResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_automation_v1_workflow_api_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FilterCustomerParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_automation_v1_workflow_api_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FilterCustomerResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_automation_v1_workflow_api_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PushWorkflowsParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_automation_v1_workflow_api_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PushWorkflowsResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_automation_v1_workflow_api_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListEnterpriseWorkflowsParams_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_enterprise_automation_v1_workflow_api_proto_msgTypes[6].OneofWrappers = []interface{}{}
	file_moego_enterprise_automation_v1_workflow_api_proto_msgTypes[8].OneofWrappers = []interface{}{}
	file_moego_enterprise_automation_v1_workflow_api_proto_msgTypes[9].OneofWrappers = []interface{}{}
	file_moego_enterprise_automation_v1_workflow_api_proto_msgTypes[12].OneofWrappers = []interface{}{}
	file_moego_enterprise_automation_v1_workflow_api_proto_msgTypes[13].OneofWrappers = []interface{}{}
	file_moego_enterprise_automation_v1_workflow_api_proto_msgTypes[16].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_enterprise_automation_v1_workflow_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   17,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_enterprise_automation_v1_workflow_api_proto_goTypes,
		DependencyIndexes: file_moego_enterprise_automation_v1_workflow_api_proto_depIdxs,
		MessageInfos:      file_moego_enterprise_automation_v1_workflow_api_proto_msgTypes,
	}.Build()
	File_moego_enterprise_automation_v1_workflow_api_proto = out.File
	file_moego_enterprise_automation_v1_workflow_api_proto_rawDesc = nil
	file_moego_enterprise_automation_v1_workflow_api_proto_goTypes = nil
	file_moego_enterprise_automation_v1_workflow_api_proto_depIdxs = nil
}
