// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/enterprise/enterprise/v1/enterprise_api.proto

package enterpriseapipb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// EnterpriseServiceClient is the client API for EnterpriseService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type EnterpriseServiceClient interface {
	// CreateEnterprise
	CreateEnterprise(ctx context.Context, in *CreateEnterpriseParams, opts ...grpc.CallOption) (*CreateEnterpriseResult, error)
	// UpdateEnterprise
	UpdateEnterprise(ctx context.Context, in *UpdateEnterpriseParams, opts ...grpc.CallOption) (*UpdateEnterpriseResult, error)
	// GetEnterprise
	GetEnterprise(ctx context.Context, in *GetEnterpriseParams, opts ...grpc.CallOption) (*GetEnterpriseResult, error)
	// list tenant templates
	ListTenantTemplates(ctx context.Context, in *ListTenantTemplateParams, opts ...grpc.CallOption) (*ListTenantTemplateResult, error)
	// list tenant group
	ListTenantGroup(ctx context.Context, in *ListTenantGroupParams, opts ...grpc.CallOption) (*ListTenantGroupResult, error)
	// GetOptions
	GetOption(ctx context.Context, in *GetOptionParams, opts ...grpc.CallOption) (*GetOptionResult, error)
	// SyncFranchisee
	SyncFranchisee(ctx context.Context, in *SyncFranchiseeParams, opts ...grpc.CallOption) (*SyncFranchiseeResult, error)
	// GetEnterprisePreferenceSetting
	GetEnterprisePreferenceSetting(ctx context.Context, in *GetEnterprisePreferenceSettingParams, opts ...grpc.CallOption) (*GetEnterprisePreferenceSettingResult, error)
}

type enterpriseServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewEnterpriseServiceClient(cc grpc.ClientConnInterface) EnterpriseServiceClient {
	return &enterpriseServiceClient{cc}
}

func (c *enterpriseServiceClient) CreateEnterprise(ctx context.Context, in *CreateEnterpriseParams, opts ...grpc.CallOption) (*CreateEnterpriseResult, error) {
	out := new(CreateEnterpriseResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.enterprise.v1.EnterpriseService/CreateEnterprise", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *enterpriseServiceClient) UpdateEnterprise(ctx context.Context, in *UpdateEnterpriseParams, opts ...grpc.CallOption) (*UpdateEnterpriseResult, error) {
	out := new(UpdateEnterpriseResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.enterprise.v1.EnterpriseService/UpdateEnterprise", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *enterpriseServiceClient) GetEnterprise(ctx context.Context, in *GetEnterpriseParams, opts ...grpc.CallOption) (*GetEnterpriseResult, error) {
	out := new(GetEnterpriseResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.enterprise.v1.EnterpriseService/GetEnterprise", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *enterpriseServiceClient) ListTenantTemplates(ctx context.Context, in *ListTenantTemplateParams, opts ...grpc.CallOption) (*ListTenantTemplateResult, error) {
	out := new(ListTenantTemplateResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.enterprise.v1.EnterpriseService/ListTenantTemplates", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *enterpriseServiceClient) ListTenantGroup(ctx context.Context, in *ListTenantGroupParams, opts ...grpc.CallOption) (*ListTenantGroupResult, error) {
	out := new(ListTenantGroupResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.enterprise.v1.EnterpriseService/ListTenantGroup", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *enterpriseServiceClient) GetOption(ctx context.Context, in *GetOptionParams, opts ...grpc.CallOption) (*GetOptionResult, error) {
	out := new(GetOptionResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.enterprise.v1.EnterpriseService/GetOption", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *enterpriseServiceClient) SyncFranchisee(ctx context.Context, in *SyncFranchiseeParams, opts ...grpc.CallOption) (*SyncFranchiseeResult, error) {
	out := new(SyncFranchiseeResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.enterprise.v1.EnterpriseService/SyncFranchisee", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *enterpriseServiceClient) GetEnterprisePreferenceSetting(ctx context.Context, in *GetEnterprisePreferenceSettingParams, opts ...grpc.CallOption) (*GetEnterprisePreferenceSettingResult, error) {
	out := new(GetEnterprisePreferenceSettingResult)
	err := c.cc.Invoke(ctx, "/moego.enterprise.enterprise.v1.EnterpriseService/GetEnterprisePreferenceSetting", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// EnterpriseServiceServer is the server API for EnterpriseService service.
// All implementations must embed UnimplementedEnterpriseServiceServer
// for forward compatibility
type EnterpriseServiceServer interface {
	// CreateEnterprise
	CreateEnterprise(context.Context, *CreateEnterpriseParams) (*CreateEnterpriseResult, error)
	// UpdateEnterprise
	UpdateEnterprise(context.Context, *UpdateEnterpriseParams) (*UpdateEnterpriseResult, error)
	// GetEnterprise
	GetEnterprise(context.Context, *GetEnterpriseParams) (*GetEnterpriseResult, error)
	// list tenant templates
	ListTenantTemplates(context.Context, *ListTenantTemplateParams) (*ListTenantTemplateResult, error)
	// list tenant group
	ListTenantGroup(context.Context, *ListTenantGroupParams) (*ListTenantGroupResult, error)
	// GetOptions
	GetOption(context.Context, *GetOptionParams) (*GetOptionResult, error)
	// SyncFranchisee
	SyncFranchisee(context.Context, *SyncFranchiseeParams) (*SyncFranchiseeResult, error)
	// GetEnterprisePreferenceSetting
	GetEnterprisePreferenceSetting(context.Context, *GetEnterprisePreferenceSettingParams) (*GetEnterprisePreferenceSettingResult, error)
	mustEmbedUnimplementedEnterpriseServiceServer()
}

// UnimplementedEnterpriseServiceServer must be embedded to have forward compatible implementations.
type UnimplementedEnterpriseServiceServer struct {
}

func (UnimplementedEnterpriseServiceServer) CreateEnterprise(context.Context, *CreateEnterpriseParams) (*CreateEnterpriseResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateEnterprise not implemented")
}
func (UnimplementedEnterpriseServiceServer) UpdateEnterprise(context.Context, *UpdateEnterpriseParams) (*UpdateEnterpriseResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateEnterprise not implemented")
}
func (UnimplementedEnterpriseServiceServer) GetEnterprise(context.Context, *GetEnterpriseParams) (*GetEnterpriseResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetEnterprise not implemented")
}
func (UnimplementedEnterpriseServiceServer) ListTenantTemplates(context.Context, *ListTenantTemplateParams) (*ListTenantTemplateResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListTenantTemplates not implemented")
}
func (UnimplementedEnterpriseServiceServer) ListTenantGroup(context.Context, *ListTenantGroupParams) (*ListTenantGroupResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListTenantGroup not implemented")
}
func (UnimplementedEnterpriseServiceServer) GetOption(context.Context, *GetOptionParams) (*GetOptionResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOption not implemented")
}
func (UnimplementedEnterpriseServiceServer) SyncFranchisee(context.Context, *SyncFranchiseeParams) (*SyncFranchiseeResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SyncFranchisee not implemented")
}
func (UnimplementedEnterpriseServiceServer) GetEnterprisePreferenceSetting(context.Context, *GetEnterprisePreferenceSettingParams) (*GetEnterprisePreferenceSettingResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetEnterprisePreferenceSetting not implemented")
}
func (UnimplementedEnterpriseServiceServer) mustEmbedUnimplementedEnterpriseServiceServer() {}

// UnsafeEnterpriseServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to EnterpriseServiceServer will
// result in compilation errors.
type UnsafeEnterpriseServiceServer interface {
	mustEmbedUnimplementedEnterpriseServiceServer()
}

func RegisterEnterpriseServiceServer(s grpc.ServiceRegistrar, srv EnterpriseServiceServer) {
	s.RegisterService(&EnterpriseService_ServiceDesc, srv)
}

func _EnterpriseService_CreateEnterprise_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateEnterpriseParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EnterpriseServiceServer).CreateEnterprise(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.enterprise.v1.EnterpriseService/CreateEnterprise",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EnterpriseServiceServer).CreateEnterprise(ctx, req.(*CreateEnterpriseParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _EnterpriseService_UpdateEnterprise_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateEnterpriseParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EnterpriseServiceServer).UpdateEnterprise(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.enterprise.v1.EnterpriseService/UpdateEnterprise",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EnterpriseServiceServer).UpdateEnterprise(ctx, req.(*UpdateEnterpriseParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _EnterpriseService_GetEnterprise_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEnterpriseParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EnterpriseServiceServer).GetEnterprise(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.enterprise.v1.EnterpriseService/GetEnterprise",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EnterpriseServiceServer).GetEnterprise(ctx, req.(*GetEnterpriseParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _EnterpriseService_ListTenantTemplates_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListTenantTemplateParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EnterpriseServiceServer).ListTenantTemplates(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.enterprise.v1.EnterpriseService/ListTenantTemplates",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EnterpriseServiceServer).ListTenantTemplates(ctx, req.(*ListTenantTemplateParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _EnterpriseService_ListTenantGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListTenantGroupParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EnterpriseServiceServer).ListTenantGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.enterprise.v1.EnterpriseService/ListTenantGroup",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EnterpriseServiceServer).ListTenantGroup(ctx, req.(*ListTenantGroupParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _EnterpriseService_GetOption_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOptionParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EnterpriseServiceServer).GetOption(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.enterprise.v1.EnterpriseService/GetOption",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EnterpriseServiceServer).GetOption(ctx, req.(*GetOptionParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _EnterpriseService_SyncFranchisee_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SyncFranchiseeParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EnterpriseServiceServer).SyncFranchisee(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.enterprise.v1.EnterpriseService/SyncFranchisee",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EnterpriseServiceServer).SyncFranchisee(ctx, req.(*SyncFranchiseeParams))
	}
	return interceptor(ctx, in, info, handler)
}

func _EnterpriseService_GetEnterprisePreferenceSetting_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEnterprisePreferenceSettingParams)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EnterpriseServiceServer).GetEnterprisePreferenceSetting(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.enterprise.enterprise.v1.EnterpriseService/GetEnterprisePreferenceSetting",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EnterpriseServiceServer).GetEnterprisePreferenceSetting(ctx, req.(*GetEnterprisePreferenceSettingParams))
	}
	return interceptor(ctx, in, info, handler)
}

// EnterpriseService_ServiceDesc is the grpc.ServiceDesc for EnterpriseService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var EnterpriseService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.enterprise.enterprise.v1.EnterpriseService",
	HandlerType: (*EnterpriseServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateEnterprise",
			Handler:    _EnterpriseService_CreateEnterprise_Handler,
		},
		{
			MethodName: "UpdateEnterprise",
			Handler:    _EnterpriseService_UpdateEnterprise_Handler,
		},
		{
			MethodName: "GetEnterprise",
			Handler:    _EnterpriseService_GetEnterprise_Handler,
		},
		{
			MethodName: "ListTenantTemplates",
			Handler:    _EnterpriseService_ListTenantTemplates_Handler,
		},
		{
			MethodName: "ListTenantGroup",
			Handler:    _EnterpriseService_ListTenantGroup_Handler,
		},
		{
			MethodName: "GetOption",
			Handler:    _EnterpriseService_GetOption_Handler,
		},
		{
			MethodName: "SyncFranchisee",
			Handler:    _EnterpriseService_SyncFranchisee_Handler,
		},
		{
			MethodName: "GetEnterprisePreferenceSetting",
			Handler:    _EnterpriseService_GetEnterprisePreferenceSetting_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/enterprise/enterprise/v1/enterprise_api.proto",
}
