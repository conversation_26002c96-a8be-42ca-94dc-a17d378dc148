// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/enterprise/enterprise/v1/enterprise_api.proto

package enterpriseapipb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/enterprise/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// GetEnterpriseParams
type GetEnterpriseParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id
	Id *int64 `protobuf:"varint,1,opt,name=id,proto3,oneof" json:"id,omitempty"`
	// need tenant template
	NeedTenantTemplate *bool `protobuf:"varint,2,opt,name=need_tenant_template,json=needTenantTemplate,proto3,oneof" json:"need_tenant_template,omitempty"`
}

func (x *GetEnterpriseParams) Reset() {
	*x = GetEnterpriseParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_enterprise_v1_enterprise_api_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEnterpriseParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEnterpriseParams) ProtoMessage() {}

func (x *GetEnterpriseParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_enterprise_v1_enterprise_api_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEnterpriseParams.ProtoReflect.Descriptor instead.
func (*GetEnterpriseParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_enterprise_v1_enterprise_api_proto_rawDescGZIP(), []int{0}
}

func (x *GetEnterpriseParams) GetId() int64 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *GetEnterpriseParams) GetNeedTenantTemplate() bool {
	if x != nil && x.NeedTenantTemplate != nil {
		return *x.NeedTenantTemplate
	}
	return false
}

// GetEnterpriseResult
type GetEnterpriseResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// enterprise
	Enterprise *v1.EnterpriseModel `protobuf:"bytes,1,opt,name=enterprise,proto3" json:"enterprise,omitempty"`
	// tenant template
	TenantTemplate []*v1.TenantTemplateModel `protobuf:"bytes,2,rep,name=tenant_template,json=tenantTemplate,proto3" json:"tenant_template,omitempty"`
}

func (x *GetEnterpriseResult) Reset() {
	*x = GetEnterpriseResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_enterprise_v1_enterprise_api_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEnterpriseResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEnterpriseResult) ProtoMessage() {}

func (x *GetEnterpriseResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_enterprise_v1_enterprise_api_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEnterpriseResult.ProtoReflect.Descriptor instead.
func (*GetEnterpriseResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_enterprise_v1_enterprise_api_proto_rawDescGZIP(), []int{1}
}

func (x *GetEnterpriseResult) GetEnterprise() *v1.EnterpriseModel {
	if x != nil {
		return x.Enterprise
	}
	return nil
}

func (x *GetEnterpriseResult) GetTenantTemplate() []*v1.TenantTemplateModel {
	if x != nil {
		return x.TenantTemplate
	}
	return nil
}

// CreateEnterpriseParams
type CreateEnterpriseParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// enterprise
	Enterprise *v1.CreateEnterpriseDef `protobuf:"bytes,1,opt,name=enterprise,proto3" json:"enterprise,omitempty"`
	// tenant template
	TenantTemplate *v1.CreateTenantTemplateDef `protobuf:"bytes,2,opt,name=tenant_template,json=tenantTemplate,proto3" json:"tenant_template,omitempty"`
}

func (x *CreateEnterpriseParams) Reset() {
	*x = CreateEnterpriseParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_enterprise_v1_enterprise_api_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateEnterpriseParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateEnterpriseParams) ProtoMessage() {}

func (x *CreateEnterpriseParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_enterprise_v1_enterprise_api_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateEnterpriseParams.ProtoReflect.Descriptor instead.
func (*CreateEnterpriseParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_enterprise_v1_enterprise_api_proto_rawDescGZIP(), []int{2}
}

func (x *CreateEnterpriseParams) GetEnterprise() *v1.CreateEnterpriseDef {
	if x != nil {
		return x.Enterprise
	}
	return nil
}

func (x *CreateEnterpriseParams) GetTenantTemplate() *v1.CreateTenantTemplateDef {
	if x != nil {
		return x.TenantTemplate
	}
	return nil
}

// CreateEnterpriseResult
type CreateEnterpriseResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// enterprise
	Enterprise *v1.EnterpriseModel `protobuf:"bytes,1,opt,name=enterprise,proto3" json:"enterprise,omitempty"`
}

func (x *CreateEnterpriseResult) Reset() {
	*x = CreateEnterpriseResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_enterprise_v1_enterprise_api_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateEnterpriseResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateEnterpriseResult) ProtoMessage() {}

func (x *CreateEnterpriseResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_enterprise_v1_enterprise_api_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateEnterpriseResult.ProtoReflect.Descriptor instead.
func (*CreateEnterpriseResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_enterprise_v1_enterprise_api_proto_rawDescGZIP(), []int{3}
}

func (x *CreateEnterpriseResult) GetEnterprise() *v1.EnterpriseModel {
	if x != nil {
		return x.Enterprise
	}
	return nil
}

// UpdateEnterpriseParams
type UpdateEnterpriseParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// enterprise id
	Id *int64 `protobuf:"varint,1,opt,name=id,proto3,oneof" json:"id,omitempty"`
	// enterprise setting def
	Enterprise *v1.UpdateEnterpriseDef `protobuf:"bytes,2,opt,name=enterprise,proto3,oneof" json:"enterprise,omitempty"`
	// optional
	TenantTemplate *v1.UpdateTenantTemplateDef `protobuf:"bytes,3,opt,name=tenant_template,json=tenantTemplate,proto3,oneof" json:"tenant_template,omitempty"`
}

func (x *UpdateEnterpriseParams) Reset() {
	*x = UpdateEnterpriseParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_enterprise_v1_enterprise_api_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateEnterpriseParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateEnterpriseParams) ProtoMessage() {}

func (x *UpdateEnterpriseParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_enterprise_v1_enterprise_api_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateEnterpriseParams.ProtoReflect.Descriptor instead.
func (*UpdateEnterpriseParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_enterprise_v1_enterprise_api_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateEnterpriseParams) GetId() int64 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *UpdateEnterpriseParams) GetEnterprise() *v1.UpdateEnterpriseDef {
	if x != nil {
		return x.Enterprise
	}
	return nil
}

func (x *UpdateEnterpriseParams) GetTenantTemplate() *v1.UpdateTenantTemplateDef {
	if x != nil {
		return x.TenantTemplate
	}
	return nil
}

// UpdateEnterpriseResult
type UpdateEnterpriseResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// enterprise
	Enterprise *v1.EnterpriseModel `protobuf:"bytes,1,opt,name=enterprise,proto3" json:"enterprise,omitempty"`
	// tenant template
	TenantTemplate *v1.TenantTemplateModel `protobuf:"bytes,2,opt,name=tenant_template,json=tenantTemplate,proto3,oneof" json:"tenant_template,omitempty"`
}

func (x *UpdateEnterpriseResult) Reset() {
	*x = UpdateEnterpriseResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_enterprise_v1_enterprise_api_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateEnterpriseResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateEnterpriseResult) ProtoMessage() {}

func (x *UpdateEnterpriseResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_enterprise_v1_enterprise_api_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateEnterpriseResult.ProtoReflect.Descriptor instead.
func (*UpdateEnterpriseResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_enterprise_v1_enterprise_api_proto_rawDescGZIP(), []int{5}
}

func (x *UpdateEnterpriseResult) GetEnterprise() *v1.EnterpriseModel {
	if x != nil {
		return x.Enterprise
	}
	return nil
}

func (x *UpdateEnterpriseResult) GetTenantTemplate() *v1.TenantTemplateModel {
	if x != nil {
		return x.TenantTemplate
	}
	return nil
}

// list tenant template params
type ListTenantTemplateParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// enterprise id
	EnterpriseId *int64 `protobuf:"varint,1,opt,name=enterprise_id,json=enterpriseId,proto3,oneof" json:"enterprise_id,omitempty"`
	// filter
	Filter *ListTenantTemplateParams_Filter `protobuf:"bytes,2,opt,name=filter,proto3,oneof" json:"filter,omitempty"`
}

func (x *ListTenantTemplateParams) Reset() {
	*x = ListTenantTemplateParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_enterprise_v1_enterprise_api_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListTenantTemplateParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTenantTemplateParams) ProtoMessage() {}

func (x *ListTenantTemplateParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_enterprise_v1_enterprise_api_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTenantTemplateParams.ProtoReflect.Descriptor instead.
func (*ListTenantTemplateParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_enterprise_v1_enterprise_api_proto_rawDescGZIP(), []int{6}
}

func (x *ListTenantTemplateParams) GetEnterpriseId() int64 {
	if x != nil && x.EnterpriseId != nil {
		return *x.EnterpriseId
	}
	return 0
}

func (x *ListTenantTemplateParams) GetFilter() *ListTenantTemplateParams_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

// list tenant templates result
type ListTenantTemplateResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// tenant templates
	TenantTemplates []*v1.TenantTemplateModel `protobuf:"bytes,1,rep,name=tenant_templates,json=tenantTemplates,proto3" json:"tenant_templates,omitempty"`
}

func (x *ListTenantTemplateResult) Reset() {
	*x = ListTenantTemplateResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_enterprise_v1_enterprise_api_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListTenantTemplateResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTenantTemplateResult) ProtoMessage() {}

func (x *ListTenantTemplateResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_enterprise_v1_enterprise_api_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTenantTemplateResult.ProtoReflect.Descriptor instead.
func (*ListTenantTemplateResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_enterprise_v1_enterprise_api_proto_rawDescGZIP(), []int{7}
}

func (x *ListTenantTemplateResult) GetTenantTemplates() []*v1.TenantTemplateModel {
	if x != nil {
		return x.TenantTemplates
	}
	return nil
}

// list tenant group params
type ListTenantGroupParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// enterprise id
	EnterpriseId *int64 `protobuf:"varint,1,opt,name=enterprise_id,json=enterpriseId,proto3,oneof" json:"enterprise_id,omitempty"`
	// filter
	Filter *ListTenantGroupParams_Filter `protobuf:"bytes,2,opt,name=filter,proto3,oneof" json:"filter,omitempty"`
}

func (x *ListTenantGroupParams) Reset() {
	*x = ListTenantGroupParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_enterprise_v1_enterprise_api_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListTenantGroupParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTenantGroupParams) ProtoMessage() {}

func (x *ListTenantGroupParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_enterprise_v1_enterprise_api_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTenantGroupParams.ProtoReflect.Descriptor instead.
func (*ListTenantGroupParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_enterprise_v1_enterprise_api_proto_rawDescGZIP(), []int{8}
}

func (x *ListTenantGroupParams) GetEnterpriseId() int64 {
	if x != nil && x.EnterpriseId != nil {
		return *x.EnterpriseId
	}
	return 0
}

func (x *ListTenantGroupParams) GetFilter() *ListTenantGroupParams_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

// list tenant group result
type ListTenantGroupResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// tenant groups
	TenantGroups []*v1.TenantGroupModel `protobuf:"bytes,1,rep,name=tenant_groups,json=tenantGroups,proto3" json:"tenant_groups,omitempty"`
}

func (x *ListTenantGroupResult) Reset() {
	*x = ListTenantGroupResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_enterprise_v1_enterprise_api_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListTenantGroupResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTenantGroupResult) ProtoMessage() {}

func (x *ListTenantGroupResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_enterprise_v1_enterprise_api_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTenantGroupResult.ProtoReflect.Descriptor instead.
func (*ListTenantGroupResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_enterprise_v1_enterprise_api_proto_rawDescGZIP(), []int{9}
}

func (x *ListTenantGroupResult) GetTenantGroups() []*v1.TenantGroupModel {
	if x != nil {
		return x.TenantGroups
	}
	return nil
}

// get option params
type GetOptionParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetOptionParams) Reset() {
	*x = GetOptionParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_enterprise_v1_enterprise_api_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOptionParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOptionParams) ProtoMessage() {}

func (x *GetOptionParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_enterprise_v1_enterprise_api_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOptionParams.ProtoReflect.Descriptor instead.
func (*GetOptionParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_enterprise_v1_enterprise_api_proto_rawDescGZIP(), []int{10}
}

// get option result
type GetOptionResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// option
	Option *v1.OptionModel `protobuf:"bytes,1,opt,name=option,proto3" json:"option,omitempty"`
}

func (x *GetOptionResult) Reset() {
	*x = GetOptionResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_enterprise_v1_enterprise_api_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOptionResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOptionResult) ProtoMessage() {}

func (x *GetOptionResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_enterprise_v1_enterprise_api_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOptionResult.ProtoReflect.Descriptor instead.
func (*GetOptionResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_enterprise_v1_enterprise_api_proto_rawDescGZIP(), []int{11}
}

func (x *GetOptionResult) GetOption() *v1.OptionModel {
	if x != nil {
		return x.Option
	}
	return nil
}

// sync franchisee params
type SyncFranchiseeParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SyncFranchiseeParams) Reset() {
	*x = SyncFranchiseeParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_enterprise_v1_enterprise_api_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncFranchiseeParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncFranchiseeParams) ProtoMessage() {}

func (x *SyncFranchiseeParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_enterprise_v1_enterprise_api_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncFranchiseeParams.ProtoReflect.Descriptor instead.
func (*SyncFranchiseeParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_enterprise_v1_enterprise_api_proto_rawDescGZIP(), []int{12}
}

// sync franchisee result
type SyncFranchiseeResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SyncFranchiseeResult) Reset() {
	*x = SyncFranchiseeResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_enterprise_v1_enterprise_api_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncFranchiseeResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncFranchiseeResult) ProtoMessage() {}

func (x *SyncFranchiseeResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_enterprise_v1_enterprise_api_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncFranchiseeResult.ProtoReflect.Descriptor instead.
func (*SyncFranchiseeResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_enterprise_v1_enterprise_api_proto_rawDescGZIP(), []int{13}
}

// GetEnterprisePreferenceSettingParams
type GetEnterprisePreferenceSettingParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetEnterprisePreferenceSettingParams) Reset() {
	*x = GetEnterprisePreferenceSettingParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_enterprise_v1_enterprise_api_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEnterprisePreferenceSettingParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEnterprisePreferenceSettingParams) ProtoMessage() {}

func (x *GetEnterprisePreferenceSettingParams) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_enterprise_v1_enterprise_api_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEnterprisePreferenceSettingParams.ProtoReflect.Descriptor instead.
func (*GetEnterprisePreferenceSettingParams) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_enterprise_v1_enterprise_api_proto_rawDescGZIP(), []int{14}
}

// GetEnterprisePreferenceSettingResult
type GetEnterprisePreferenceSettingResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// tenant text mappings
	TenantTextMappings []*GetEnterprisePreferenceSettingResult_TenantTextMapping `protobuf:"bytes,1,rep,name=tenant_text_mappings,json=tenantTextMappings,proto3" json:"tenant_text_mappings,omitempty"`
}

func (x *GetEnterprisePreferenceSettingResult) Reset() {
	*x = GetEnterprisePreferenceSettingResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_enterprise_v1_enterprise_api_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEnterprisePreferenceSettingResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEnterprisePreferenceSettingResult) ProtoMessage() {}

func (x *GetEnterprisePreferenceSettingResult) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_enterprise_v1_enterprise_api_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEnterprisePreferenceSettingResult.ProtoReflect.Descriptor instead.
func (*GetEnterprisePreferenceSettingResult) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_enterprise_v1_enterprise_api_proto_rawDescGZIP(), []int{15}
}

func (x *GetEnterprisePreferenceSettingResult) GetTenantTextMappings() []*GetEnterprisePreferenceSettingResult_TenantTextMapping {
	if x != nil {
		return x.TenantTextMappings
	}
	return nil
}

// filter
type ListTenantTemplateParams_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// tenant id
	TenantId []int64 `protobuf:"varint,1,rep,packed,name=tenant_id,json=tenantId,proto3" json:"tenant_id,omitempty"`
	// status
	Status []v1.TenantTemplateModel_Status `protobuf:"varint,2,rep,packed,name=status,proto3,enum=moego.models.enterprise.v1.TenantTemplateModel_Status" json:"status,omitempty"`
	// type
	Type []v1.TenantTemplateModel_Type `protobuf:"varint,3,rep,packed,name=type,proto3,enum=moego.models.enterprise.v1.TenantTemplateModel_Type" json:"type,omitempty"`
}

func (x *ListTenantTemplateParams_Filter) Reset() {
	*x = ListTenantTemplateParams_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_enterprise_v1_enterprise_api_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListTenantTemplateParams_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTenantTemplateParams_Filter) ProtoMessage() {}

func (x *ListTenantTemplateParams_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_enterprise_v1_enterprise_api_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTenantTemplateParams_Filter.ProtoReflect.Descriptor instead.
func (*ListTenantTemplateParams_Filter) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_enterprise_v1_enterprise_api_proto_rawDescGZIP(), []int{6, 0}
}

func (x *ListTenantTemplateParams_Filter) GetTenantId() []int64 {
	if x != nil {
		return x.TenantId
	}
	return nil
}

func (x *ListTenantTemplateParams_Filter) GetStatus() []v1.TenantTemplateModel_Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *ListTenantTemplateParams_Filter) GetType() []v1.TenantTemplateModel_Type {
	if x != nil {
		return x.Type
	}
	return nil
}

// filter
type ListTenantGroupParams_Filter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// tenant id
	TenantId []int64 `protobuf:"varint,1,rep,packed,name=tenant_id,json=tenantId,proto3" json:"tenant_id,omitempty"`
}

func (x *ListTenantGroupParams_Filter) Reset() {
	*x = ListTenantGroupParams_Filter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_enterprise_v1_enterprise_api_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListTenantGroupParams_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTenantGroupParams_Filter) ProtoMessage() {}

func (x *ListTenantGroupParams_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_enterprise_v1_enterprise_api_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTenantGroupParams_Filter.ProtoReflect.Descriptor instead.
func (*ListTenantGroupParams_Filter) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_enterprise_v1_enterprise_api_proto_rawDescGZIP(), []int{8, 0}
}

func (x *ListTenantGroupParams_Filter) GetTenantId() []int64 {
	if x != nil {
		return x.TenantId
	}
	return nil
}

// tenant text mapping
type GetEnterprisePreferenceSettingResult_TenantTextMapping struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// type
	Type v1.TenantTextType `protobuf:"varint,1,opt,name=type,proto3,enum=moego.models.enterprise.v1.TenantTextType" json:"type,omitempty"`
	// value
	Value string `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *GetEnterprisePreferenceSettingResult_TenantTextMapping) Reset() {
	*x = GetEnterprisePreferenceSettingResult_TenantTextMapping{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_enterprise_enterprise_v1_enterprise_api_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEnterprisePreferenceSettingResult_TenantTextMapping) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEnterprisePreferenceSettingResult_TenantTextMapping) ProtoMessage() {}

func (x *GetEnterprisePreferenceSettingResult_TenantTextMapping) ProtoReflect() protoreflect.Message {
	mi := &file_moego_enterprise_enterprise_v1_enterprise_api_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEnterprisePreferenceSettingResult_TenantTextMapping.ProtoReflect.Descriptor instead.
func (*GetEnterprisePreferenceSettingResult_TenantTextMapping) Descriptor() ([]byte, []int) {
	return file_moego_enterprise_enterprise_v1_enterprise_api_proto_rawDescGZIP(), []int{15, 0}
}

func (x *GetEnterprisePreferenceSettingResult_TenantTextMapping) GetType() v1.TenantTextType {
	if x != nil {
		return x.Type
	}
	return v1.TenantTextType(0)
}

func (x *GetEnterprisePreferenceSettingResult_TenantTextMapping) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

var File_moego_enterprise_enterprise_v1_enterprise_api_proto protoreflect.FileDescriptor

var file_moego_enterprise_enterprise_v1_enterprise_api_proto_rawDesc = []byte{
	0x0a, 0x33, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x2f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x76, 0x31,
	0x2f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x61, 0x70, 0x69, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x2e, 0x76, 0x31, 0x1a, 0x30, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x76,
	0x31, 0x2f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x64, 0x65, 0x66,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x31, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x2f, 0x76, 0x31, 0x2f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x65,
	0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x32, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73,
	0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x34,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x65, 0x6e, 0x61, 0x6e,
	0x74, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x35, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f, 0x76, 0x31,
	0x2f, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x37, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x5f, 0x74,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x8a, 0x01,
	0x0a, 0x13, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x1c, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x02, 0x69, 0x64,
	0x88, 0x01, 0x01, 0x12, 0x35, 0x0a, 0x14, 0x6e, 0x65, 0x65, 0x64, 0x5f, 0x74, 0x65, 0x6e, 0x61,
	0x6e, 0x74, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x08, 0x48, 0x01, 0x52, 0x12, 0x6e, 0x65, 0x65, 0x64, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x54,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x42, 0x05, 0x0a, 0x03, 0x5f, 0x69,
	0x64, 0x42, 0x17, 0x0a, 0x15, 0x5f, 0x6e, 0x65, 0x65, 0x64, 0x5f, 0x74, 0x65, 0x6e, 0x61, 0x6e,
	0x74, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x22, 0xbc, 0x01, 0x0a, 0x13, 0x47,
	0x65, 0x74, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x12, 0x4b, 0x0a, 0x0a, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x52, 0x0a, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x12,
	0x58, 0x0a, 0x0f, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x54, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0e, 0x74, 0x65, 0x6e, 0x61, 0x6e,
	0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x22, 0xc7, 0x01, 0x0a, 0x16, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x12, 0x4f, 0x0a, 0x0a, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x44, 0x65, 0x66, 0x52, 0x0a, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x12, 0x5c, 0x0a, 0x0f, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x5f,
	0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x44, 0x65, 0x66, 0x52, 0x0e, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x22, 0x65, 0x0a, 0x16, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x4b, 0x0a,
	0x0a, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x45,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0a,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x22, 0x99, 0x02, 0x0a, 0x16, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x1c, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00, 0x52, 0x02, 0x69, 0x64,
	0x88, 0x01, 0x01, 0x12, 0x54, 0x0a, 0x0a, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x44, 0x65, 0x66, 0x48, 0x01, 0x52, 0x0a, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x88, 0x01, 0x01, 0x12, 0x61, 0x0a, 0x0f, 0x74, 0x65, 0x6e,
	0x61, 0x6e, 0x74, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x54, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x48, 0x02, 0x52, 0x0e, 0x74, 0x65, 0x6e, 0x61, 0x6e,
	0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x42, 0x05, 0x0a, 0x03,
	0x5f, 0x69, 0x64, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x5f, 0x74, 0x65,
	0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x22, 0xd8, 0x01, 0x0a, 0x16, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x4b, 0x0a, 0x0a, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x52, 0x0a, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x12, 0x5d,
	0x0a, 0x0f, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x48, 0x00, 0x52, 0x0e, 0x74, 0x65, 0x6e, 0x61,
	0x6e, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x42, 0x12, 0x0a,
	0x10, 0x5f, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x22, 0x8a, 0x03, 0x0a, 0x18, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74,
	0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x31,
	0x0a, 0x0d, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x48, 0x00,
	0x52, 0x0c, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x49, 0x64, 0x88, 0x01,
	0x01, 0x12, 0x5c, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x3f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x54, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x2e, 0x46, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x48, 0x01, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x88, 0x01, 0x01, 0x1a,
	0xbf, 0x01, 0x0a, 0x06, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x65,
	0x6e, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x08, 0x74,
	0x65, 0x6e, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x4e, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x48, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x5f, 0x69, 0x64, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x22, 0x76,
	0x0a, 0x18, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x54, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x5a, 0x0a, 0x10, 0x74, 0x65,
	0x6e, 0x61, 0x6e, 0x74, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x0f, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x54, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x22, 0xe9, 0x01, 0x0a, 0x15, 0x4c, 0x69, 0x73, 0x74, 0x54,
	0x65, 0x6e, 0x61, 0x6e, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x12, 0x31, 0x0a, 0x0d, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00,
	0x48, 0x00, 0x52, 0x0c, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x49, 0x64,
	0x88, 0x01, 0x01, 0x12, 0x59, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73,
	0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x48, 0x01, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x88, 0x01, 0x01, 0x1a, 0x25,
	0x0a, 0x06, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x65, 0x6e, 0x61,
	0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x08, 0x74, 0x65, 0x6e,
	0x61, 0x6e, 0x74, 0x49, 0x64, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x66, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x22, 0x6a, 0x0a, 0x15, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x51, 0x0a, 0x0d, 0x74,
	0x65, 0x6e, 0x61, 0x6e, 0x74, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x52, 0x0c, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x22, 0x11,
	0x0a, 0x0f, 0x47, 0x65, 0x74, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x22, 0x52, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x3f, 0x0a, 0x06, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x06, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x16, 0x0a, 0x14, 0x53, 0x79, 0x6e, 0x63, 0x46, 0x72, 0x61,
	0x6e, 0x63, 0x68, 0x69, 0x73, 0x65, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x22, 0x16, 0x0a,
	0x14, 0x53, 0x79, 0x6e, 0x63, 0x46, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x69, 0x73, 0x65, 0x65, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x26, 0x0a, 0x24, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65,
	0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x22, 0x9c, 0x02,
	0x0a, 0x24, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x50,
	0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x88, 0x01, 0x0a, 0x14, 0x74, 0x65, 0x6e, 0x61, 0x6e,
	0x74, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x5f, 0x6d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x56, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x65,
	0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x54, 0x65, 0x6e, 0x61,
	0x6e, 0x74, 0x54, 0x65, 0x78, 0x74, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x52, 0x12, 0x74,
	0x65, 0x6e, 0x61, 0x6e, 0x74, 0x54, 0x65, 0x78, 0x74, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67,
	0x73, 0x1a, 0x69, 0x0a, 0x11, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x54, 0x65, 0x78, 0x74, 0x4d,
	0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x12, 0x3e, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76,
	0x31, 0x2e, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x54, 0x65, 0x78, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x32, 0xc1, 0x08, 0x0a,
	0x11, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x12, 0x82, 0x01, 0x0a, 0x10, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x12, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a,
	0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73,
	0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x82, 0x01, 0x0a, 0x10, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x12, 0x36, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x1a, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x6e, 0x74, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x79, 0x0a, 0x0d,
	0x47, 0x65, 0x74, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x12, 0x33, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x1a, 0x33, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73,
	0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x89, 0x01, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74,
	0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x12,
	0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x54,
	0x65, 0x6e, 0x61, 0x6e, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x7f, 0x0a, 0x0f, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x65, 0x6e, 0x61, 0x6e,
	0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x65, 0x6e, 0x61,
	0x6e, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x35, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x6d, 0x0a, 0x09, 0x47, 0x65, 0x74, 0x4f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x1a, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x7c, 0x0a, 0x0e, 0x53, 0x79, 0x6e, 0x63, 0x46, 0x72, 0x61, 0x6e, 0x63,
	0x68, 0x69, 0x73, 0x65, 0x65, 0x12, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x79, 0x6e, 0x63, 0x46, 0x72, 0x61, 0x6e, 0x63,
	0x68, 0x69, 0x73, 0x65, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x34, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x79, 0x6e,
	0x63, 0x46, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x69, 0x73, 0x65, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0xac, 0x01, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x65, 0x74,
	0x74, 0x69, 0x6e, 0x67, 0x12, 0x44, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69,
	0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72,
	0x69, 0x73, 0x65, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x65, 0x74,
	0x74, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0x44, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x45,
	0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65,
	0x6e, 0x63, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x42, 0x8f, 0x01, 0x0a, 0x26, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69,
	0x64, 0x6c, 0x2e, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x63, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c,
	0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69,
	0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74,
	0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70,
	0x72, 0x69, 0x73, 0x65, 0x2f, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x2f,
	0x76, 0x31, 0x3b, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x61, 0x70, 0x69,
	0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_enterprise_enterprise_v1_enterprise_api_proto_rawDescOnce sync.Once
	file_moego_enterprise_enterprise_v1_enterprise_api_proto_rawDescData = file_moego_enterprise_enterprise_v1_enterprise_api_proto_rawDesc
)

func file_moego_enterprise_enterprise_v1_enterprise_api_proto_rawDescGZIP() []byte {
	file_moego_enterprise_enterprise_v1_enterprise_api_proto_rawDescOnce.Do(func() {
		file_moego_enterprise_enterprise_v1_enterprise_api_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_enterprise_enterprise_v1_enterprise_api_proto_rawDescData)
	})
	return file_moego_enterprise_enterprise_v1_enterprise_api_proto_rawDescData
}

var file_moego_enterprise_enterprise_v1_enterprise_api_proto_msgTypes = make([]protoimpl.MessageInfo, 19)
var file_moego_enterprise_enterprise_v1_enterprise_api_proto_goTypes = []interface{}{
	(*GetEnterpriseParams)(nil),                                    // 0: moego.enterprise.enterprise.v1.GetEnterpriseParams
	(*GetEnterpriseResult)(nil),                                    // 1: moego.enterprise.enterprise.v1.GetEnterpriseResult
	(*CreateEnterpriseParams)(nil),                                 // 2: moego.enterprise.enterprise.v1.CreateEnterpriseParams
	(*CreateEnterpriseResult)(nil),                                 // 3: moego.enterprise.enterprise.v1.CreateEnterpriseResult
	(*UpdateEnterpriseParams)(nil),                                 // 4: moego.enterprise.enterprise.v1.UpdateEnterpriseParams
	(*UpdateEnterpriseResult)(nil),                                 // 5: moego.enterprise.enterprise.v1.UpdateEnterpriseResult
	(*ListTenantTemplateParams)(nil),                               // 6: moego.enterprise.enterprise.v1.ListTenantTemplateParams
	(*ListTenantTemplateResult)(nil),                               // 7: moego.enterprise.enterprise.v1.ListTenantTemplateResult
	(*ListTenantGroupParams)(nil),                                  // 8: moego.enterprise.enterprise.v1.ListTenantGroupParams
	(*ListTenantGroupResult)(nil),                                  // 9: moego.enterprise.enterprise.v1.ListTenantGroupResult
	(*GetOptionParams)(nil),                                        // 10: moego.enterprise.enterprise.v1.GetOptionParams
	(*GetOptionResult)(nil),                                        // 11: moego.enterprise.enterprise.v1.GetOptionResult
	(*SyncFranchiseeParams)(nil),                                   // 12: moego.enterprise.enterprise.v1.SyncFranchiseeParams
	(*SyncFranchiseeResult)(nil),                                   // 13: moego.enterprise.enterprise.v1.SyncFranchiseeResult
	(*GetEnterprisePreferenceSettingParams)(nil),                   // 14: moego.enterprise.enterprise.v1.GetEnterprisePreferenceSettingParams
	(*GetEnterprisePreferenceSettingResult)(nil),                   // 15: moego.enterprise.enterprise.v1.GetEnterprisePreferenceSettingResult
	(*ListTenantTemplateParams_Filter)(nil),                        // 16: moego.enterprise.enterprise.v1.ListTenantTemplateParams.Filter
	(*ListTenantGroupParams_Filter)(nil),                           // 17: moego.enterprise.enterprise.v1.ListTenantGroupParams.Filter
	(*GetEnterprisePreferenceSettingResult_TenantTextMapping)(nil), // 18: moego.enterprise.enterprise.v1.GetEnterprisePreferenceSettingResult.TenantTextMapping
	(*v1.EnterpriseModel)(nil),                                     // 19: moego.models.enterprise.v1.EnterpriseModel
	(*v1.TenantTemplateModel)(nil),                                 // 20: moego.models.enterprise.v1.TenantTemplateModel
	(*v1.CreateEnterpriseDef)(nil),                                 // 21: moego.models.enterprise.v1.CreateEnterpriseDef
	(*v1.CreateTenantTemplateDef)(nil),                             // 22: moego.models.enterprise.v1.CreateTenantTemplateDef
	(*v1.UpdateEnterpriseDef)(nil),                                 // 23: moego.models.enterprise.v1.UpdateEnterpriseDef
	(*v1.UpdateTenantTemplateDef)(nil),                             // 24: moego.models.enterprise.v1.UpdateTenantTemplateDef
	(*v1.TenantGroupModel)(nil),                                    // 25: moego.models.enterprise.v1.TenantGroupModel
	(*v1.OptionModel)(nil),                                         // 26: moego.models.enterprise.v1.OptionModel
	(v1.TenantTemplateModel_Status)(0),                             // 27: moego.models.enterprise.v1.TenantTemplateModel.Status
	(v1.TenantTemplateModel_Type)(0),                               // 28: moego.models.enterprise.v1.TenantTemplateModel.Type
	(v1.TenantTextType)(0),                                         // 29: moego.models.enterprise.v1.TenantTextType
}
var file_moego_enterprise_enterprise_v1_enterprise_api_proto_depIdxs = []int32{
	19, // 0: moego.enterprise.enterprise.v1.GetEnterpriseResult.enterprise:type_name -> moego.models.enterprise.v1.EnterpriseModel
	20, // 1: moego.enterprise.enterprise.v1.GetEnterpriseResult.tenant_template:type_name -> moego.models.enterprise.v1.TenantTemplateModel
	21, // 2: moego.enterprise.enterprise.v1.CreateEnterpriseParams.enterprise:type_name -> moego.models.enterprise.v1.CreateEnterpriseDef
	22, // 3: moego.enterprise.enterprise.v1.CreateEnterpriseParams.tenant_template:type_name -> moego.models.enterprise.v1.CreateTenantTemplateDef
	19, // 4: moego.enterprise.enterprise.v1.CreateEnterpriseResult.enterprise:type_name -> moego.models.enterprise.v1.EnterpriseModel
	23, // 5: moego.enterprise.enterprise.v1.UpdateEnterpriseParams.enterprise:type_name -> moego.models.enterprise.v1.UpdateEnterpriseDef
	24, // 6: moego.enterprise.enterprise.v1.UpdateEnterpriseParams.tenant_template:type_name -> moego.models.enterprise.v1.UpdateTenantTemplateDef
	19, // 7: moego.enterprise.enterprise.v1.UpdateEnterpriseResult.enterprise:type_name -> moego.models.enterprise.v1.EnterpriseModel
	20, // 8: moego.enterprise.enterprise.v1.UpdateEnterpriseResult.tenant_template:type_name -> moego.models.enterprise.v1.TenantTemplateModel
	16, // 9: moego.enterprise.enterprise.v1.ListTenantTemplateParams.filter:type_name -> moego.enterprise.enterprise.v1.ListTenantTemplateParams.Filter
	20, // 10: moego.enterprise.enterprise.v1.ListTenantTemplateResult.tenant_templates:type_name -> moego.models.enterprise.v1.TenantTemplateModel
	17, // 11: moego.enterprise.enterprise.v1.ListTenantGroupParams.filter:type_name -> moego.enterprise.enterprise.v1.ListTenantGroupParams.Filter
	25, // 12: moego.enterprise.enterprise.v1.ListTenantGroupResult.tenant_groups:type_name -> moego.models.enterprise.v1.TenantGroupModel
	26, // 13: moego.enterprise.enterprise.v1.GetOptionResult.option:type_name -> moego.models.enterprise.v1.OptionModel
	18, // 14: moego.enterprise.enterprise.v1.GetEnterprisePreferenceSettingResult.tenant_text_mappings:type_name -> moego.enterprise.enterprise.v1.GetEnterprisePreferenceSettingResult.TenantTextMapping
	27, // 15: moego.enterprise.enterprise.v1.ListTenantTemplateParams.Filter.status:type_name -> moego.models.enterprise.v1.TenantTemplateModel.Status
	28, // 16: moego.enterprise.enterprise.v1.ListTenantTemplateParams.Filter.type:type_name -> moego.models.enterprise.v1.TenantTemplateModel.Type
	29, // 17: moego.enterprise.enterprise.v1.GetEnterprisePreferenceSettingResult.TenantTextMapping.type:type_name -> moego.models.enterprise.v1.TenantTextType
	2,  // 18: moego.enterprise.enterprise.v1.EnterpriseService.CreateEnterprise:input_type -> moego.enterprise.enterprise.v1.CreateEnterpriseParams
	4,  // 19: moego.enterprise.enterprise.v1.EnterpriseService.UpdateEnterprise:input_type -> moego.enterprise.enterprise.v1.UpdateEnterpriseParams
	0,  // 20: moego.enterprise.enterprise.v1.EnterpriseService.GetEnterprise:input_type -> moego.enterprise.enterprise.v1.GetEnterpriseParams
	6,  // 21: moego.enterprise.enterprise.v1.EnterpriseService.ListTenantTemplates:input_type -> moego.enterprise.enterprise.v1.ListTenantTemplateParams
	8,  // 22: moego.enterprise.enterprise.v1.EnterpriseService.ListTenantGroup:input_type -> moego.enterprise.enterprise.v1.ListTenantGroupParams
	10, // 23: moego.enterprise.enterprise.v1.EnterpriseService.GetOption:input_type -> moego.enterprise.enterprise.v1.GetOptionParams
	12, // 24: moego.enterprise.enterprise.v1.EnterpriseService.SyncFranchisee:input_type -> moego.enterprise.enterprise.v1.SyncFranchiseeParams
	14, // 25: moego.enterprise.enterprise.v1.EnterpriseService.GetEnterprisePreferenceSetting:input_type -> moego.enterprise.enterprise.v1.GetEnterprisePreferenceSettingParams
	3,  // 26: moego.enterprise.enterprise.v1.EnterpriseService.CreateEnterprise:output_type -> moego.enterprise.enterprise.v1.CreateEnterpriseResult
	5,  // 27: moego.enterprise.enterprise.v1.EnterpriseService.UpdateEnterprise:output_type -> moego.enterprise.enterprise.v1.UpdateEnterpriseResult
	1,  // 28: moego.enterprise.enterprise.v1.EnterpriseService.GetEnterprise:output_type -> moego.enterprise.enterprise.v1.GetEnterpriseResult
	7,  // 29: moego.enterprise.enterprise.v1.EnterpriseService.ListTenantTemplates:output_type -> moego.enterprise.enterprise.v1.ListTenantTemplateResult
	9,  // 30: moego.enterprise.enterprise.v1.EnterpriseService.ListTenantGroup:output_type -> moego.enterprise.enterprise.v1.ListTenantGroupResult
	11, // 31: moego.enterprise.enterprise.v1.EnterpriseService.GetOption:output_type -> moego.enterprise.enterprise.v1.GetOptionResult
	13, // 32: moego.enterprise.enterprise.v1.EnterpriseService.SyncFranchisee:output_type -> moego.enterprise.enterprise.v1.SyncFranchiseeResult
	15, // 33: moego.enterprise.enterprise.v1.EnterpriseService.GetEnterprisePreferenceSetting:output_type -> moego.enterprise.enterprise.v1.GetEnterprisePreferenceSettingResult
	26, // [26:34] is the sub-list for method output_type
	18, // [18:26] is the sub-list for method input_type
	18, // [18:18] is the sub-list for extension type_name
	18, // [18:18] is the sub-list for extension extendee
	0,  // [0:18] is the sub-list for field type_name
}

func init() { file_moego_enterprise_enterprise_v1_enterprise_api_proto_init() }
func file_moego_enterprise_enterprise_v1_enterprise_api_proto_init() {
	if File_moego_enterprise_enterprise_v1_enterprise_api_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_enterprise_enterprise_v1_enterprise_api_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEnterpriseParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_enterprise_v1_enterprise_api_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEnterpriseResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_enterprise_v1_enterprise_api_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateEnterpriseParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_enterprise_v1_enterprise_api_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateEnterpriseResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_enterprise_v1_enterprise_api_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateEnterpriseParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_enterprise_v1_enterprise_api_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateEnterpriseResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_enterprise_v1_enterprise_api_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListTenantTemplateParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_enterprise_v1_enterprise_api_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListTenantTemplateResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_enterprise_v1_enterprise_api_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListTenantGroupParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_enterprise_v1_enterprise_api_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListTenantGroupResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_enterprise_v1_enterprise_api_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOptionParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_enterprise_v1_enterprise_api_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOptionResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_enterprise_v1_enterprise_api_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SyncFranchiseeParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_enterprise_v1_enterprise_api_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SyncFranchiseeResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_enterprise_v1_enterprise_api_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEnterprisePreferenceSettingParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_enterprise_v1_enterprise_api_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEnterprisePreferenceSettingResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_enterprise_v1_enterprise_api_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListTenantTemplateParams_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_enterprise_v1_enterprise_api_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListTenantGroupParams_Filter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_enterprise_enterprise_v1_enterprise_api_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEnterprisePreferenceSettingResult_TenantTextMapping); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_enterprise_enterprise_v1_enterprise_api_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_enterprise_enterprise_v1_enterprise_api_proto_msgTypes[4].OneofWrappers = []interface{}{}
	file_moego_enterprise_enterprise_v1_enterprise_api_proto_msgTypes[5].OneofWrappers = []interface{}{}
	file_moego_enterprise_enterprise_v1_enterprise_api_proto_msgTypes[6].OneofWrappers = []interface{}{}
	file_moego_enterprise_enterprise_v1_enterprise_api_proto_msgTypes[8].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_enterprise_enterprise_v1_enterprise_api_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   19,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_enterprise_enterprise_v1_enterprise_api_proto_goTypes,
		DependencyIndexes: file_moego_enterprise_enterprise_v1_enterprise_api_proto_depIdxs,
		MessageInfos:      file_moego_enterprise_enterprise_v1_enterprise_api_proto_msgTypes,
	}.Build()
	File_moego_enterprise_enterprise_v1_enterprise_api_proto = out.File
	file_moego_enterprise_enterprise_v1_enterprise_api_proto_rawDesc = nil
	file_moego_enterprise_enterprise_v1_enterprise_api_proto_goTypes = nil
	file_moego_enterprise_enterprise_v1_enterprise_api_proto_depIdxs = nil
}
