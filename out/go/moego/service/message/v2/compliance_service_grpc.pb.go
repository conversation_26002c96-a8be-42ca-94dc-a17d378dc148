// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             (unknown)
// source: moego/service/message/v2/compliance_service.proto

package messagesvcpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// ComplianceServiceClient is the client API for ComplianceService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ComplianceServiceClient interface {
	// 获取客户合规配置
	GetCustomerComplianceConfig(ctx context.Context, in *GetCustomerComplianceConfigRequest, opts ...grpc.CallOption) (*GetCustomerComplianceConfigResponse, error)
	// 设置客户合规设置
	SetCustomerComplianceConfig(ctx context.Context, in *SetCustomerComplianceConfigRequest, opts ...grpc.CallOption) (*SetCustomerComplianceConfigResponse, error)
	// 复制客户合规配置
	CopyCustomerComplianceConfig(ctx context.Context, in *CopyCustomerComplianceConfigRequest, opts ...grpc.CallOption) (*CopyCustomerComplianceConfigResponse, error)
}

type complianceServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewComplianceServiceClient(cc grpc.ClientConnInterface) ComplianceServiceClient {
	return &complianceServiceClient{cc}
}

func (c *complianceServiceClient) GetCustomerComplianceConfig(ctx context.Context, in *GetCustomerComplianceConfigRequest, opts ...grpc.CallOption) (*GetCustomerComplianceConfigResponse, error) {
	out := new(GetCustomerComplianceConfigResponse)
	err := c.cc.Invoke(ctx, "/moego.service.message.v2.ComplianceService/GetCustomerComplianceConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *complianceServiceClient) SetCustomerComplianceConfig(ctx context.Context, in *SetCustomerComplianceConfigRequest, opts ...grpc.CallOption) (*SetCustomerComplianceConfigResponse, error) {
	out := new(SetCustomerComplianceConfigResponse)
	err := c.cc.Invoke(ctx, "/moego.service.message.v2.ComplianceService/SetCustomerComplianceConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *complianceServiceClient) CopyCustomerComplianceConfig(ctx context.Context, in *CopyCustomerComplianceConfigRequest, opts ...grpc.CallOption) (*CopyCustomerComplianceConfigResponse, error) {
	out := new(CopyCustomerComplianceConfigResponse)
	err := c.cc.Invoke(ctx, "/moego.service.message.v2.ComplianceService/CopyCustomerComplianceConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ComplianceServiceServer is the server API for ComplianceService service.
// All implementations must embed UnimplementedComplianceServiceServer
// for forward compatibility
type ComplianceServiceServer interface {
	// 获取客户合规配置
	GetCustomerComplianceConfig(context.Context, *GetCustomerComplianceConfigRequest) (*GetCustomerComplianceConfigResponse, error)
	// 设置客户合规设置
	SetCustomerComplianceConfig(context.Context, *SetCustomerComplianceConfigRequest) (*SetCustomerComplianceConfigResponse, error)
	// 复制客户合规配置
	CopyCustomerComplianceConfig(context.Context, *CopyCustomerComplianceConfigRequest) (*CopyCustomerComplianceConfigResponse, error)
	mustEmbedUnimplementedComplianceServiceServer()
}

// UnimplementedComplianceServiceServer must be embedded to have forward compatible implementations.
type UnimplementedComplianceServiceServer struct {
}

func (UnimplementedComplianceServiceServer) GetCustomerComplianceConfig(context.Context, *GetCustomerComplianceConfigRequest) (*GetCustomerComplianceConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCustomerComplianceConfig not implemented")
}
func (UnimplementedComplianceServiceServer) SetCustomerComplianceConfig(context.Context, *SetCustomerComplianceConfigRequest) (*SetCustomerComplianceConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetCustomerComplianceConfig not implemented")
}
func (UnimplementedComplianceServiceServer) CopyCustomerComplianceConfig(context.Context, *CopyCustomerComplianceConfigRequest) (*CopyCustomerComplianceConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CopyCustomerComplianceConfig not implemented")
}
func (UnimplementedComplianceServiceServer) mustEmbedUnimplementedComplianceServiceServer() {}

// UnsafeComplianceServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ComplianceServiceServer will
// result in compilation errors.
type UnsafeComplianceServiceServer interface {
	mustEmbedUnimplementedComplianceServiceServer()
}

func RegisterComplianceServiceServer(s grpc.ServiceRegistrar, srv ComplianceServiceServer) {
	s.RegisterService(&ComplianceService_ServiceDesc, srv)
}

func _ComplianceService_GetCustomerComplianceConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCustomerComplianceConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ComplianceServiceServer).GetCustomerComplianceConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.message.v2.ComplianceService/GetCustomerComplianceConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ComplianceServiceServer).GetCustomerComplianceConfig(ctx, req.(*GetCustomerComplianceConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ComplianceService_SetCustomerComplianceConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetCustomerComplianceConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ComplianceServiceServer).SetCustomerComplianceConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.message.v2.ComplianceService/SetCustomerComplianceConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ComplianceServiceServer).SetCustomerComplianceConfig(ctx, req.(*SetCustomerComplianceConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ComplianceService_CopyCustomerComplianceConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CopyCustomerComplianceConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ComplianceServiceServer).CopyCustomerComplianceConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/moego.service.message.v2.ComplianceService/CopyCustomerComplianceConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ComplianceServiceServer).CopyCustomerComplianceConfig(ctx, req.(*CopyCustomerComplianceConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ComplianceService_ServiceDesc is the grpc.ServiceDesc for ComplianceService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ComplianceService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "moego.service.message.v2.ComplianceService",
	HandlerType: (*ComplianceServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetCustomerComplianceConfig",
			Handler:    _ComplianceService_GetCustomerComplianceConfig_Handler,
		},
		{
			MethodName: "SetCustomerComplianceConfig",
			Handler:    _ComplianceService_SetCustomerComplianceConfig_Handler,
		},
		{
			MethodName: "CopyCustomerComplianceConfig",
			Handler:    _ComplianceService_CopyCustomerComplianceConfig_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "moego/service/message/v2/compliance_service.proto",
}
