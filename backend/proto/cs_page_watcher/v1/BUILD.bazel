load("@io_bazel_rules_go//go:def.bzl", "go_library")
load("@io_bazel_rules_go//proto:def.bzl", "go_proto_library")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "cs_page_watcher_proto",
    srcs = ["cs_page_watcher.proto"],
    visibility = ["//visibility:public"],
)

go_proto_library(
    name = "cs_page_watcher_go_proto",
    compilers = [
        "@io_bazel_rules_go//proto:go_grpc_v2",
        "@io_bazel_rules_go//proto:go_proto",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/proto/cs_page_watcher/v1",
    proto = ":cs_page_watcher_proto",
    visibility = ["//visibility:public"],
)

go_library(
    name = "cs_page_watcher",
    embed = [":cs_page_watcher_go_proto"],
    importpath = "github.com/MoeGolibrary/moego/backend/proto/cs_page_watcher/v1",
    visibility = ["//visibility:public"],
)
