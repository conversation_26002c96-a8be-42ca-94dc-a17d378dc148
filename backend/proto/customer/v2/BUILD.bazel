load("@io_bazel_rules_go//go:def.bzl", "go_library")
load("@io_bazel_rules_go//proto:def.bzl", "go_proto_library")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "customerpb_proto",
    srcs = [
        "activity.proto",
        "activity_service.proto",
        "common.proto",
        "metadata.proto",
        "metadata_service.proto",
    ],
    visibility = ["//visibility:public"],
    deps = [
        "@com_github_bufbuild_protovalidate//proto/protovalidate/buf/validate:validate_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:struct_proto",
        "@com_google_protobuf//:timestamp_proto",
        "@googleapis//google/api:annotations_proto",
        "@googleapis//google/api:field_behavior_proto",
        "@googleapis//google/type:interval_proto",
        "@googleapis//google/type:latlng_proto",
        "@googleapis//google/type:money_proto",
        "@googleapis//google/type:phone_number_proto",
        "@googleapis//google/type:postal_address_proto",
    ],
)

go_proto_library(
    name = "customerpb_go_proto",
    compilers = [
        "@io_bazel_rules_go//proto:go_grpc_v2",
        "@io_bazel_rules_go//proto:go_proto",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/proto/customer/v2",
    proto = ":customerpb_proto",
    visibility = ["//visibility:public"],
    deps = [
        "@build_buf_gen_go_bufbuild_protovalidate_protocolbuffers_go//buf/validate:go_default_library",
        "@googleapis//google/api:annotations_go_proto",
        "@googleapis//google/api:field_behavior_go_proto",
        "@org_golang_google_genproto//googleapis/type/interval",
        "@org_golang_google_genproto//googleapis/type/latlng",
        "@org_golang_google_genproto//googleapis/type/money",
        "@org_golang_google_genproto//googleapis/type/phone_number",
        "@org_golang_google_genproto//googleapis/type/postaladdress",
    ],
)

go_library(
    name = "customer",
    embed = [":customerpb_go_proto"],
    importpath = "github.com/MoeGolibrary/moego/backend/proto/customer/v2",
    visibility = ["//visibility:public"],
)
