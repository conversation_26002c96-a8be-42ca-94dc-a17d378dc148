load("@io_bazel_rules_go//go:def.bzl", "go_library")
load("@io_bazel_rules_go//proto:def.bzl", "go_proto_library")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "customerportalpb_proto",
    srcs = ["customer_portal_service.proto"],
    visibility = ["//visibility:public"],
)

go_proto_library(
    name = "customerportalpb_go_proto",
    compilers = [
        "@io_bazel_rules_go//proto:go_grpc_v2",
        "@io_bazel_rules_go//proto:go_proto",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/proto/customer_portal/v1",
    proto = ":customerportalpb_proto",
    visibility = ["//visibility:public"],
)

go_library(
    name = "customer_portal",
    embed = [":customerportalpb_go_proto"],
    importpath = "github.com/MoeGolibrary/moego/backend/proto/customer_portal/v1",
    visibility = ["//visibility:public"],
)
