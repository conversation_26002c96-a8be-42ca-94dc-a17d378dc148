// (-- api-linter: core::0133::response-message-name=disabled
//     aip.dev/not-precedent: 平铺使用 --)
// (-- api-linter: core::0133::request-resource-field=disabled
//     aip.dev/not-precedent: 平铺使用 --)
// (-- api-linter: core::0131::response-message-name=disabled
//     aip.dev/not-precedent: 平铺使用 --)
// (-- api-linter:  core::0134::response-message-name=disabled
//     aip.dev/not-precedent: 平铺使用 --)
// (-- api-linter:  core::0134::synonyms=disabled
//     aip.dev/not-precedent: 使用Set表达Upsert语义 --)

syntax = "proto3";

package backend.proto.customer_portal.v1;

option go_package="github.com/MoeGolibrary/moego/backend/proto/customer_portal/v1;customerportalpb"; // 这里的package名不要改
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.customer_portal.v1";

// CustomerPortalService
service CustomerPortalService {
  // SetOnlineBookingScript 设置OB CSS&JS 脚本
  rpc SetOnlineBookingScript(SetOnlineBookingScriptRequest) returns (SetOnlineBookingScriptResponse);
  // GetOnlineBookingScript 获取OB CSS&JS 脚本
  rpc GetOnlineBookingScript(GetOnlineBookingScriptRequest) returns (GetOnlineBookingScriptResponse);
}

// SetOnlineBookingScriptRequest 设置OB CSS&JS 脚本请求
message SetOnlineBookingScriptRequest {
  // companyID
  int64 company_id = 1;
  // businessID
  int64 business_id = 2;
  // staffID
  int64 staff_id = 3;
  // css text
  optional string css = 4;
  // js text
  optional string js = 5;
}

// CreateOnlineBookingScriptResponse 设置OB CSS&JS 脚本响应
message SetOnlineBookingScriptResponse {
}

// GetOnlineBookingScriptRequest 获取OB CSS&JS 脚本请求
message GetOnlineBookingScriptRequest {
  // businessID
  int64 business_id = 1;
}

// GetOnlineBookingScriptResponse 获取OB CSS&JS 脚本响应
message GetOnlineBookingScriptResponse {
  // css text
  string css = 1;
  // js text
  string js = 2;
}


// ErrCode 定义错误码枚举
// 
// 以下两行枚举不要改动，从下面开始累加最后两位数新增错误码，前四位不能改
// 如果单个服务新增错误码超过100个, 建议入土
// 如果是老服务重构的新服务可删除以上错误码继续沿用原有的错误码
// 注意：pb enum 的规范是：统一前缀，全大写，下划线
// 业务代码优先使用通用错误码 /backend/common/proto/rpc/code.proto ，再定义自己的错误码
enum ErrCode {
  // (-- api-linter: core::0126::unspecified=disabled
  //     aip.dev/not-precedent: We need to do this because 
  //     the content of the error code is automatically generated by
  //     the script and is exclusive to each service.
  //     Please do not turn off this linter for the rest of the enum --)
  // 成功
  ERR_CODE_OK = 0;
  // 本服务自动分配的全局唯一的起始错误码
  ERR_CODE_UNSPECIFIED = 113000; 
}