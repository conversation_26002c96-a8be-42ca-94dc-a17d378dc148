syntax = "proto3";

package backend.proto.organization.v1;

option go_package = "github.com/MoeGolibrary/moego/backend/proto/organization/v1;organizationpb"; // 这里的package名不要改
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.organization.v1";

// Organization type.
enum OrganizationType {
  // Unspecified organization type.
  ORGANIZATION_TYPE_UNSPECIFIED = 0;
  // Enterprise.
  ENTERPRISE = 1;
  // Company.
  COMPANY = 2;
  // Business.
  BUSINESS = 3;
}