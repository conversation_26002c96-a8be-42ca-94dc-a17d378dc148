# gazelle:resolve proto buf/validate/validate.proto @com_github_bufbuild_protovalidate//proto/protovalidate/buf/validate:validate_proto
# gazelle:resolve proto go buf/validate/validate.proto @build_buf_gen_go_bufbuild_protovalidate_protocolbuffers_go//buf/validate:go_default_library

# gazelle:resolve proto google/api/annotations.proto @googleapis//google/api:annotations_proto
# gazelle:resolve proto go google/api/annotations.proto @googleapis//google/api:annotations_go_proto

# gazelle:resolve proto google/api/field_behavior.proto @googleapis//google/api:field_behavior_proto
# gazelle:resolve proto go google/api/field_behavior.proto @googleapis//google/api:field_behavior_go_proto

# gazelle:resolve proto google/api/httpbody.proto @googleapis//google/api:httpbody_proto
# gazelle:resolve proto go google/api/httpbody.proto @googleapis//google/api:httpbody_go_proto

# gazelle:resolve proto google/type/decimal.proto @googleapis//google/type:decimal_proto
# gazelle:resolve proto go google/type/decimal.proto @org_golang_google_genproto//googleapis/type/decimal

# gazelle:resolve proto google/type/money.proto @googleapis//google/type:money_proto
# gazelle:resolve proto go google/type/money.proto @org_golang_google_genproto//googleapis/type/money

# gazelle:resolve proto google/type/interval.proto @googleapis//google/type:interval_proto
# gazelle:resolve proto go google/type/interval.proto @org_golang_google_genproto//googleapis/type/interval

# gazelle:resolve proto google/type/latlng.proto @googleapis//google/type:latlng_proto
# gazelle:resolve proto go google/type/latlng.proto @org_golang_google_genproto//googleapis/type/latlng

# gazelle:resolve proto google/type/phone_number.proto @googleapis//google/type:phone_number_proto
# gazelle:resolve proto go google/type/phone_number.proto @org_golang_google_genproto//googleapis/type/phone_number

# gazelle:resolve proto google/type/postal_address.proto @googleapis//google/type:postal_address_proto
# gazelle:resolve proto go google/type/postal_address.proto @org_golang_google_genproto//googleapis/type/postaladdress
