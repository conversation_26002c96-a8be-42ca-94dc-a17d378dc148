syntax = "proto3";

package backend.proto.test_account.v1;

option go_package = "github.com/MoeGolibrary/moego/backend/proto/test_account/v1;testaccountpb";
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.test_account.v1";

import "buf/validate/validate.proto";

// test account service
service TestAccountService {
  // Borrow a test account
  rpc BorrowTestAccount(BorrowTestAccountRequest) returns (BorrowTestAccountResponse);

  // Return a test account via contract_id
  rpc ReturnTestAccount(ReturnTestAccountRequest) returns (ReturnTestAccountResponse);

  // Create a test account
  rpc CreateTestAccount(CreateTestAccountRequest) returns (TestAccount);

  // Release test accounts
  rpc ReleaseTestAccounts(ReleaseTestAccountsRequest) returns (ReleaseTestAccountsResponse);

  // List test accounts
  rpc ListTestAccounts(ListTestAccountsRequest) returns (ListTestAccountsResponse);
}


// TestAccount resource message
message TestAccount {
  // id
  int64 id = 1;
  // email
  string email = 2;
  // password
  string password = 3;
  // onwer
  string owner = 4;
  // disposable
  bool disposable = 5;
  // attributes
  Attributes attributes = 7;
  // occupied
  bool occupied = 8;
}

// attributes of test account
message Attributes {
  // 地区代码, 暂时只支持这些地区: US, CA, GB, AU, CN, 后续再添加其他地区
  optional string region_code = 1 [(buf.validate.field) = { string: { in: ["US", "CA", "GB", "AU", "CN"] } }];

  // 是否启用 boarding daycare
  optional bool enable_boarding_daycare = 2;
  // 是否启用 online booking
  optional bool enable_online_booking = 3;
  // 是否启用 stripe
  optional bool enable_stripe = 6;
  // 是否有 SMS credit
  optional bool has_sms_credit = 7;
  // 是否有 email credit
  optional bool has_email_credit = 8;
}

// BorrowTestAccountRequest
message BorrowTestAccountRequest {
  // borrower (who are you)
  // suggest to use test case/suite name
  string borrower = 1 [
    (buf.validate.field) = { string: { min_len: 1, max_len: 255 } }
  ];

  // 指定 identifier 获取账号
  // 可以指定一个 id 或者 email
  // 如果都不指定，则随机获取一个账号
  oneof identifier {
    // id
    int64 id = 2 [(buf.validate.field) = { int64: { gt: 0 } }];
    // email
    string email = 3 [(buf.validate.field) = { string: { min_len: 1 } }];
  }

  // attributes
  optional Attributes attributes = 4;

  // 在借用期间是否允许共享测试账号，即不占用测试账号，建议只读场景的 test case 开启
  optional bool shared = 5;
}

// BorrowTestAccountResponse
message BorrowTestAccountResponse {
  // id
  int64 id = 1;
  // email
  string email = 2;
  // password
  string password = 3;
  // contract id
  int64 contract_id = 4;
}

// ReturnTestAccountRequest
message ReturnTestAccountRequest {
  // contract id
  int64 contract_id = 3 [(buf.validate.field) = { int64: { gt: 0 } }];
}

// ReturnTestAccountResponse
message ReturnTestAccountResponse {}

// CreateTestAccountRequest
message CreateTestAccountRequest {
  // Required. The test account to create
  TestAccount test_account = 1 [(buf.validate.field).required = true];

  // Optional. The test account ID to use for this request
  optional string test_account_id = 2;
}

// ReleaseTestAccountsRequest
message ReleaseTestAccountsRequest {
  // overdue
  bool overdue = 1;
}

// ReleaseTestAccountsResponse
message ReleaseTestAccountsResponse {}

// ListTestAccountsRequest
message ListTestAccountsRequest {
  // Optional. The maximum number of accounts to return.
  // If empty, fetch 20 accounts by default.
  optional int32 page_size = 1 [(buf.validate.field) = { int32: { gt: 0 } }];
  // Optional. A token to retrieve the next page of results. 
  // Currently use as page number.
  // If empty, fetch the first page by default.
  optional string page_token = 2 [(buf.validate.field) = { string: { min_len: 1 } }];
  // Optional. Filters to apply to the list of test accounts.
  optional ListTestAccountsFilter filter = 3;
}

// ListTestAccountsResponse
message ListTestAccountsResponse {
  // The list of test accounts.
  repeated TestAccount test_accounts = 1;
  // A token to retrieve the next page of results.
  // Currently use as page number.
  // If empty, there are no more pages.
  optional string next_page_token = 2;
  // total size
  int32 total_size = 3;
}

// ListTestAccountsFilter
message ListTestAccountsFilter {
  // Optional. Filter by owner
  optional string owner = 1;
  // Optional. Filter by occupied status
  optional bool occupied = 2;
  // Optional. Filter by attributes
  optional Attributes attributes = 3;
}
