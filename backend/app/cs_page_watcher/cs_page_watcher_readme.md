# CS Page Watcher - 项目上下文

## 概述
CS Page Watcher 是一个监控与客户支持相关的 Jira 工单并根据工单属性执行自动化操作的服务。该系统可以触发 Datadog 事件、创建 Slack 频道、将工单分配给适当的团队以及处理数据导入任务。

## 核心组件

### 1. 主服务 (`service/page_watcher.go`)
- **PageWatcherIns**: CS Page Watcher 服务的主要实现
- 实现了 `pb.CSPageWatcherServiceServer` 接口
- 核心方法：
  - `MonitorAndPage()`: 主监控功能
  - `TriggerPage()`: 从 Jira 工单触发页面
  - `CompleteIncident()`: 完成事件
  - `CompleteJira()`: 完成 Jira 工单
  - `TriggerAdminTask()`: 将工单分配给适当的负责人
  - `RunTask()`: 执行特定任务
  - `runDataImportTask()`: 处理数据导入任务
  - `dmSubTaskReminder()`: 处理 DM 项目子任务状态更新提醒

### 2. DM 子任务提醒 (`service/dm_reminder.go`)
- **dmSubTaskReminder**: 当 DM 项目子任务状态更新时发送 Slack 提醒

### 2. 配置 (`configloader/`)
- **Config**: 主配置结构
- **CSPageConfig**: Nacos 配置结构
- 配置文件从环境特定目录加载

### 3. 全局常量 (`global/`)
- Jira 自定义字段名称 (T1OrGeneral, IssuePriority 等)
- Jira 优先级层级 (TierT1, TierOther)
- Slack 表情符号常量
- 团队时间表和映射
- 联系人映射 (MoegoContacts)

### 4. 数据模型 (`repo/entity/`)
- **CsPage**: 表示 cs_page 表
- **CsDataImportTask**: 表示 cs_data_import_task 表
- 两个实体的读写接口和实现

### 5. 外部集成

#### Jira (`repo/jira/`)
- **IssueRepository**: Jira 操作接口
- **IssueRepositoryIns**: 使用 go-jira 库的实现
- **Issue**: Jira 工单数据结构
- 获取工单、更新字段、关闭工单等方法

#### Slack (`repo/slack/`)
- **Client**: Slack 操作接口
- **slackClient**: 使用 slack-go 库的实现
- 创建频道、添加成员、发送消息等方法

#### Datadog (`repo/datadog/`)
- **IncidentGateway**: Datadog 操作接口
- 触发事件、获取根本原因等方法

### 6. 逻辑组件 (`logic/`)
- **JiraIncidentEvaluator**: 评估 Jira 工单以确定是否应触发事件

### 7. 定时任务 (`service/jira_sla_reminder.go`)
- **ScheduledJiraReminder**: 处理 SLA 提醒任务
- 提醒开放和逾期的工单

## 核心数据结构

### Issue (Jira 工单)
```go
type Issue struct {
    ID                   string
    Key                  string
    Summary              string
    Status               string
    IssueDescription     string
    Description          string
    Created              time.Time
    Updated              time.Time
    Assignee             UserInfo
    Reporter             UserInfo
    Parent               *Parent  // 添加Parent字段用于子任务
    T1OrGeneral          string
    IssuePriority        string
    CustomerStage        string
    Components           []string
    JiraSquad            string
    SLABreach            string
    LogoName             string
    LocationName         string
    DevEngineer          []UserInfo
    ResolutionTimeCustom string
    CreatedByCustom      string
    FeatureDomains       string
}
```

### Parent (Jira 工单父问题)
```go
type Parent struct {
    ID  string `json:"id,omitempty" structs:"id,omitempty"`
    Key string `json:"key,omitempty" structs:"key,omitempty"`
}
```

### CsPage
```go
type CsPage struct {
    CsPageJiraTicket         string
    DatadogIncidentID        string
    IncidentSlackChannelID   string
    T1NotifyMessageTimestamp string
    CreateTime               time.Time
}
```

### CsDataImportTask
```go
type CsDataImportTask struct {
    JiraKey        string
    SlackChannelID string
    CreateTime     time.Time
}
```

## 核心方法

### PageWatcherIns 方法
1. **TriggerPage**: 为符合条件的 Jira 工单创建 Datadog 事件
2. **CompleteIncident**: 完成事件并更新 Jira 工单
3. **CompleteJira**: 完成没有关联事件的 Jira 工单
4. **TriggerAdminTask**: 将工单分配给适当的负责人
5. **RunTask**: 执行定时任务
6. **runDataImportTask**: 为数据导入任务创建 Slack 频道
7. **dmSubTaskReminder**: 当 DM 项目子任务状态更新时发送 Slack 提醒

### RunTask 支持的任务名称
- `Open Tasks: Bugs`: 提醒开放的Bug工单
- `Overdue Open Tasks: Bugs`: 提醒逾期的开放Bug工单
- `Open Tasks: AdminTasks`: 提醒开放的管理任务工单
- `Overdue Open Tasks: AdminTasks`: 提醒逾期的开放管理任务工单
- `Data Import Task`: 处理数据导入任务（需要提供JiraKey参数）
- `DM Subtask Reminder`: 处理DM子任务状态更新提醒（需要提供JiraKey参数）

### Jira Repository 方法
1. **GetIssueDetails**: 获取 Jira 工单的详细信息
2. **GetNewOrUpdatedBugTickets**: 获取符合条件的工单
3. **CloseIssue**: 关闭 Jira 工单
4. **SetAssignee**: 将工单分配给用户
5. **SetCauseAndSolution**: 设置原因和解决方案字段
6. **SetFeatureDomains**: 设置功能域字段

### Slack Client 方法
1. **CreateChannel**: 创建新的 Slack 频道
2. **AddMembersToChannel**: 向频道添加成员
3. **SendMessage**: 向频道发送消息
4. **SendMessageToThread**: 向线程发送消息
5. **SendMessageToPerson**: 发送直接消息
6. **AddEmojiToMessage**: 为消息添加表情符号反应
7. **JoinChannel**: 加入频道
8. **LookUpByEmail**: 通过邮箱查找用户 ID

## 数据库模式

### cs_page 表
```sql
CREATE TABLE cs_page (
    cs_page_jira_ticket VARCHAR(255) PRIMARY KEY UNIQUE,
    datadog_incident_id VARCHAR(255) NOT NULL DEFAULT '',
    incident_slack_channel_id VARCHAR(255) NOT NULL DEFAULT '',
    t1_notify_message_timestamp VARCHAR(255) NOT NULL DEFAULT '',
    create_time TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

### cs_data_import_task 表
```sql
CREATE TABLE cs_data_import_task (
    jira_key VARCHAR(255) PRIMARY KEY UNIQUE,
    slack_channel_id VARCHAR(255) NOT NULL DEFAULT '',
    create_time TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

## Protobuf 服务定义
该服务实现了 `cs_page_watcher.proto` 中定义的 `CSPageWatcherService`：
- `TriggerPage`: 从 Jira 工单启动 CS 页面
- `TriggerAdminTask`: 将 Jira 工单分配给适当的负责人
- `CompleteIncident`: 完成事件和相关的 Jira 工单
- `CompleteJira`: 完成 Jira 工单
- `RunTask`: 按名称执行特定任务

## 核心特性
1. **事件管理**: 与 Datadog 集成进行事件创建和管理
2. **团队分配**: 根据组件自动将工单分配给适当的团队
3. **SLA 监控**: 为开放和逾期的工单安排提醒
4. **Slack 集成**: 在 Slack 中创建频道和通知
5. **数据导入任务**: 特殊处理数据导入工单，包括频道创建
6. **Jira 自动化**: 在工作流中自动更新 Jira 工单
7. **DM 子任务提醒**: 当 DM 项目子任务状态更新时自动发送 Slack 提醒

## 配置
该服务同时使用本地配置文件和 Nacos 进行动态配置：
- 本地配置包含 Slack/Jira 凭据
- Nacos 配置包含业务规则和映射

## 测试
为关键组件提供单元测试：
- 实体读写实现
- Slack 客户端方法
- 频道名称清理
- 服务方法（主要是手动/开发测试）

## 依赖
- `github.com/andygrunwald/go-jira` 用于 Jira 集成
- `github.com/slack-go/slack` 用于 Slack 集成
- `github.com/DataDog/datadog-api-client-go` 用于 Datadog 集成
- 各种实用程序库 (samber/lo, gorm 等)