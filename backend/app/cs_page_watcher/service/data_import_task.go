package service

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"github.com/samber/lo"
	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/configloader"
	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/global"
	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/repo/entity"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

// runDataImportTask processes data import tasks for Jira tickets
func (p *PageWatcherIns) runDataImportTask(ctx context.Context, jiraKey string) error {
	log.InfoContextf(ctx, "runDataImportTask: Processing data import task for Jira key: %s", jiraKey)

	// 判断是否是DM项目的issue (project:DM)
	// jiraKey格式通常是 PROJECT-123，我们检查前缀是否是DM
	if !strings.HasPrefix(jira<PERSON><PERSON>, "DM-") {
		log.Infof("Issue %s is not a DM project issue, skipping", jira<PERSON><PERSON>)

		return nil
	}

	// Check if this task has already been processed
	taskBean := &entity.CsDataImportTask{JiraKey: jiraKey}
	existingTask, err := p.dataImportRepo.GetCsDataImportTask(taskBean)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		log.ErrorContextf(ctx, "runDataImportTask: Failed to check existing task: %v", err)

		return fmt.Errorf("failed to check existing task: %w", err)
	}

	// If task already exists, skip processing
	if existingTask != nil {
		log.InfoContextf(ctx, "runDataImportTask: Task already processed for Jira key: %s", jiraKey)

		return nil // Not an error, just already processed
	}

	// Get Jira issue details
	issue, err := p.jiraClient.GetIssueDetails(jiraKey)
	if err != nil {
		log.ErrorContextf(ctx, "runDataImportTask: Failed to get Jira issue details: %v", err)

		return fmt.Errorf("failed to get Jira issue details: %w", err)
	}

	// Check if this is a TierT1 ticket
	if issue.T1OrGeneral != global.TierT1 {
		log.InfoContextf(ctx, "runDataImportTask: Not a TierT1 ticket, skipping channel creation for Jira key: %s",
			jiraKey)

		return nil
	}

	// Create a new Slack channel for this TierT1 ticket
	// The Slack client will sanitize the channel name to comply with Slack's naming rules
	channel, err := p.slackClient.CreateChannel(
		fmt.Sprintf("%s-%s-%s-dm-discussion", issue.LogoName, issue.LocationName, jiraKey))
	if err != nil {
		log.ErrorContextf(ctx, "runDataImportTask: Failed to create Slack channel: %v", err)

		return fmt.Errorf("failed to create Slack channel: %w", err)
	}

	// Get the list of users to add to the channel
	var userEmails []string

	// Add the reporter/creator
	if createdByEmail, ok := global.MoegoContacts[issue.CreatedByCustom]; ok && createdByEmail != "" {
		userEmails = append(userEmails, createdByEmail)
	}

	// Add the assignee
	if issue.Assignee.EmailAddress != "" {
		userEmails = append(userEmails, issue.Assignee.EmailAddress)
	}

	// Add users from Nacos config
	nacosConfig := configloader.GlobalNacosCsPageConfig.Load()
	if nacosConfig != nil {
		userEmails = append(userEmails, nacosConfig.DataImportUserEmails...)
	}

	// Remove duplicates
	userEmails = lo.Uniq(userEmails)

	// Add users to the channel
	if len(userEmails) > 0 {
		_ = p.retryOperation(ctx, func() error {
			return p.slackClient.JoinChannel(channel.ID)
		}, "addMembers2IncidentChannel: JoinChannel failed: %v")

		_ = p.retryOperation(ctx, func() error {
			return p.slackClient.AddMembersToChannel(channel.ID, userEmails)
		}, "runDataImportTask: Failed to add members to channel: %v")
	}

	// Save the task with the channel ID
	task := &entity.CsDataImportTask{
		JiraKey:        jiraKey,
		SlackChannelID: channel.ID,
	}
	if err := p.dataImportRepo.CreateCsDataImportTask(task); err != nil {
		log.ErrorContextf(ctx, "runDataImportTask: Failed to create task record: %v", err)

		return fmt.Errorf("failed to create task record: %w", err)
	}

	log.InfoContextf(ctx, "runDataImportTask: Successfully processed data import task for Jira key: %s, channel ID: %s",
		jiraKey, channel.ID)

	return nil
}
