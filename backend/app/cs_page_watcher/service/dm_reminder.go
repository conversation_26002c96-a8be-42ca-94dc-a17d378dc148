package service

import (
	"fmt"
	"strings"

	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/repo/entity"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

// dmSubTaskReminder 在Jira issue状态更新时被调用
// 1. 首先获取jiraKey相关的issue，判定是否是project:DM的issue，如果不是，则结束
// 2. 查看这个jiraKey是否有parent issue，获取parent issue的key
// 3. 判定parent issue key是否在数据库CsDataImportTask中。如果不在，则结束
// 4. 从CsDataImportTask中获取slack_channel_id
// 5. 发送slack消息到这个channel，提示1中的issue最新的状态是什么
func (p *PageWatcherIns) dmSubTaskReminder(jiraKey string) error {
	// 获取Jira issue的详细信息
	issue, err := p.jiraClient.GetIssueDetails(jiraKey)
	if err != nil {
		return fmt.Errorf("failed to get Jira issue details for %s: %w", jira<PERSON><PERSON>, err)
	}

	// 判断是否是DM项目的issue (project:DM)
	// jiraKey格式通常是 PROJECT-123，我们检查前缀是否是DM
	if !strings.HasPrefix(issue.Key, "DM-") {
		log.Infof("Issue %s is not a DM project issue, skipping", jiraKey)

		return nil
	}

	// 获取parent issue的key
	// 使用Parent字段获取parent key（只有子任务才有Parent字段）
	parentKey := ""
	if issue.Parent != nil && issue.Parent.Key != "" {
		parentKey = issue.Parent.Key
	} else {
		// 如果没有Parent字段，说明这不是子任务，直接结束
		log.Infof("Issue %s has no parent, skipping", jiraKey)

		return nil
	}

	// 如果无法获取parent key，则结束
	if parentKey == "" {
		log.Infof("Issue %s has no identifiable parent, skipping", jiraKey)

		return nil
	}

	// 判定parent issue key是否在数据库CsDataImportTask中
	taskBean := &entity.CsDataImportTask{
		JiraKey: parentKey,
	}

	parentTask, err := p.dataImportRepo.GetCsDataImportTask(taskBean)
	if err != nil {
		log.Infof("Parent issue %s not found in CsDataImportTask table, skipping", parentKey)

		return nil
	}

	// 从CsDataImportTask中获取slack_channel_id
	slackChannelID := parentTask.SlackChannelID
	if slackChannelID == "" {
		log.Infof("No Slack channel ID found for parent issue %s, skipping", parentKey)

		return nil
	}

	// 发送slack消息到这个channel，提示issue最新的状态是什么
	message := fmt.Sprintf("Sub-task <%s|*%s*> status updated to: *%s*",
		fmt.Sprintf("https://moego.atlassian.net/browse/%s", jiraKey),
		jiraKey,
		issue.Status)

	_, err = p.slackClient.SendMessage(slackChannelID, message)
	if err != nil {
		return fmt.Errorf("failed to send Slack message to channel %s: %w", slackChannelID, err)
	}

	log.Infof("Successfully sent DM sub-task reminder for %s to channel %s", jiraKey, slackChannelID)

	return nil
}
