package entity

import (
	"gorm.io/gorm"
)

// CsDataImportTaskReaderWriter defines the interface for CsDataImportTask CRUD operations.
type CsDataImportTaskReaderWriter interface {
	CreateCsDataImportTask(task *CsDataImportTask) error
	GetCsDataImportTask(taskBean *CsDataImportTask) (*CsDataImportTask, error)
	UpdateCsDataImportTask(task *CsDataImportTask) error
	DeleteCsDataImportTask(jiraKey string) error
	CreateOrUpdateCsDataImportTask(task *CsDataImportTask) error
}

// csDataImportTaskReaderWriterImpl implements CsDataImportTaskReaderWriter using GORM.
type csDataImportTaskReaderWriterImpl struct {
	db *gorm.DB
}

// NewCsDataImportTaskReaderWriter creates a new CsDataImportTaskReaderWriter.
func NewCsDataImportTaskReaderWriter() CsDataImportTaskReaderWriter {
	return &csDataImportTaskReaderWriterImpl{db: GetDB()}
}

// CreateCsDataImportTask creates a new CsDataImportTask record.
func (r *csDataImportTaskReaderWriterImpl) CreateCsDataImportTask(task *CsDataImportTask) error {
	return r.db.Create(task).Error
}

// GetCsDataImportTask retrieves a CsDataImportTask record by its Jira key from the provided task bean.
func (r *csDataImportTaskReaderWriterImpl) GetCsDataImportTask(taskBean *CsDataImportTask) (*CsDataImportTask, error) {
	var task CsDataImportTask
	query := r.db.Model(&CsDataImportTask{})

	if taskBean.JiraKey != "" {
		query = query.Where("jira_key = ?", taskBean.JiraKey)
	}
	if taskBean.SlackChannelID != "" {
		query = query.Where("slack_channel_id = ?", taskBean.SlackChannelID)
	}

	result := query.First(&task)
	if result.Error != nil {
		return nil, result.Error
	}

	return &task, nil
}

// UpdateCsDataImportTask updates an existing CsDataImportTask record.
func (r *csDataImportTaskReaderWriterImpl) UpdateCsDataImportTask(task *CsDataImportTask) error {
	return r.db.Save(task).Error
}

// DeleteCsDataImportTask deletes a CsDataImportTask record by its Jira key.
func (r *csDataImportTaskReaderWriterImpl) DeleteCsDataImportTask(jiraKey string) error {
	return r.db.Where("jira_key = ?", jiraKey).Delete(&CsDataImportTask{}).Error
}

// CreateOrUpdateCsDataImportTask creates a new CsDataImportTask record or updates it if the Jira key already exists.
func (r *csDataImportTaskReaderWriterImpl) CreateOrUpdateCsDataImportTask(task *CsDataImportTask) error {
	var existing CsDataImportTask
	err := r.db.Where("jira_key = ?", task.JiraKey).First(&existing).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return r.CreateCsDataImportTask(task)
		}

		return err
	}
	// 存在则更新
	return r.UpdateCsDataImportTask(task)
}
