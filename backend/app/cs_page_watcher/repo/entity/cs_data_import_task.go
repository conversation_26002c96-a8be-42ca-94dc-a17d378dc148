package entity

import (
	"time"
)

// CsDataImportTask represents the cs_data_import_task table in the database.
type CsDataImportTask struct {
	JiraKey        string    `gorm:"primaryKey;column:jira_key"`
	SlackChannelID string    `gorm:"column:slack_channel_id;not null;default:''"`
	CreateTime     time.Time `gorm:"column:create_time;autoCreateTime"`
}

// TableName specifies the table name for GORM.
func (CsDataImportTask) TableName() string {
	return "cs_data_import_task"
}
