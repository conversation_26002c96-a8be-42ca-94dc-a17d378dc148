package entity

import (
	"sync"

	"gorm.io/gorm"

	igorm "github.com/MoeGolibrary/moego/backend/common/rpc/database/gorm"
)

// CsPageReaderWriter defines the interface for CsPage CRUD operations.
type CsPageReaderWriter interface {
	CreateCsPage(page *CsPage) error
	GetCsPage(csPageBean *CsPage) (*CsPage, error)
	UpdateCsPage(page *CsPage) error
	DeleteCsPage(jiraTicket string) error
	CreateOrUpdateCsPage(page *CsPage) error
}

// csPageReaderWriterImpl implements CsPageReaderWriter using GORM.
type csPageReaderWriterImpl struct {
	db *gorm.DB
}

// NewCsPageReaderWriter creates a new CsPageReaderWriter.
func NewCsPageReaderWriter() CsPageReaderWriter {
	return &csPageReaderWriterImpl{db: GetDB()}
}

// CreateCsPage creates a new CsPage record.
func (r *csPageReaderWriterImpl) CreateCsPage(page *CsPage) error {
	return r.db.Create(page).Error
}

// GetCsPage retrieves a CsPage record by its Jira ticket from the provided CsPage bean.
func (r *csPageReaderWriterImpl) GetCsPage(csPageBean *CsPage) (*CsPage, error) {
	var page CsPage
	query := r.db.Model(&CsPage{})

	if csPageBean.CsPageJiraTicket != "" {
		query = query.Where("cs_page_jira_ticket = ?", csPageBean.CsPageJiraTicket)
	}
	if csPageBean.DatadogIncidentID != "" {
		query = query.Where("datadog_incident_id = ?", csPageBean.DatadogIncidentID)
	}
	if csPageBean.IncidentSlackChannelID != "" {
		query = query.Where("incident_slack_channel_id = ?", csPageBean.IncidentSlackChannelID)
	}

	result := query.First(&page)
	if result.Error != nil {
		return nil, result.Error
	}

	return &page, nil
}

// UpdateCsPage updates an existing CsPage record.
func (r *csPageReaderWriterImpl) UpdateCsPage(page *CsPage) error {
	return r.db.Save(page).Error
}

// DeleteCsPage deletes a CsPage record by its Jira ticket.
func (r *csPageReaderWriterImpl) DeleteCsPage(jiraTicket string) error {
	return r.db.Where("cs_page_jira_ticket = ?", jiraTicket).Delete(&CsPage{}).Error
}

// CreateOrUpdateCsPage creates a new CsPage record or updates it if the Jira ticket already exists.
func (r *csPageReaderWriterImpl) CreateOrUpdateCsPage(page *CsPage) error {
	var existing CsPage
	err := r.db.Where("cs_page_jira_ticket = ?", page.CsPageJiraTicket).First(&existing).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return r.CreateCsPage(page)
		}

		return err
	}
	// 存在则更新
	return r.UpdateCsPage(page)
}

var globalDB *gorm.DB

var initDB sync.Once

func GetDB() *gorm.DB {
	initDB.Do(func() {
		db, err := igorm.NewClientProxy("postgres")
		if err != nil {
			panic(err)
		}
		globalDB = db
	})

	return globalDB
}
