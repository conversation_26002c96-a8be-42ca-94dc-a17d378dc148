package entity

import (
	"encoding/json"
	"errors"
	"testing"
	"time"

	"github.com/samber/lo"
	"github.com/stretchr/testify/assert"
	"gorm.io/gorm"

	_ "github.com/MoeGolibrary/moego/backend/common/rpc/codec/grpc"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/database/gorm"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/filters/debuglog"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/filters/recovery"
)

func Test_csDataImportTaskReaderWriterImpl_GetCsDataImportTask(t *testing.T) {
	t.Skip("manual test")
	setUp()
	db := NewCsDataImportTaskReaderWriter()
	jiraKey := "IFRBE-2054"
	ret, err := db.GetCsDataImportTask(&CsDataImportTask{
		JiraKey: jira<PERSON>ey,
	})
	if err != nil {
		t.Errorf("err:%v", err)
		return
	}
	t.Logf("ret:%s", lo.<PERSON>(json.<PERSON>(ret)))
}

func Test_csDataImportTaskReaderWriterImpl_CreateCsDataImportTask(t *testing.T) {
	t.Skip("manual test")
	setUp()

	db := NewCsDataImportTaskReaderWriter()
	jiraKey := "IFRBE-2054"
	err := db.CreateCsDataImportTask(&CsDataImportTask{
		JiraKey:        jiraKey,
		SlackChannelID: "C1234567890",
		CreateTime:     time.Now(),
	})
	assert.Nil(t, err)

	task, err := db.GetCsDataImportTask(&CsDataImportTask{
		JiraKey: jiraKey,
	})
	assert.Nil(t, err)
	t.Logf("task:%s", lo.Must(json.Marshal(task)))
	assert.Equal(t, jiraKey, task.JiraKey)

	err = db.DeleteCsDataImportTask(jiraKey)
	assert.Nil(t, err)

	_, err = db.GetCsDataImportTask(&CsDataImportTask{
		JiraKey: jiraKey,
	})
	if !errors.Is(err, gorm.ErrRecordNotFound) {
		t.Fail()
	}
	return
}
