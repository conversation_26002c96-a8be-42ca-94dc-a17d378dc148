CREATE TABLE cs_page (
    cs_page_jira_ticket VARCHAR(255) PRIMARY KEY UNIQUE,
    datadog_incident_id VARCHAR(255) NOT NULL DEFAULT '',
    incident_slack_channel_id VARCHAR(255) NOT NULL DEFAULT '',
    t1_notify_message_timestamp VARCHAR(255) NOT NULL DEFAULT '',
    create_time TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE cs_data_import_task (
    jira_key VARCHAR(255) PRIMARY KEY UNIQUE,
    slack_channel_id VARCHAR(255) NOT NULL DEFAULT '',
    create_time TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);