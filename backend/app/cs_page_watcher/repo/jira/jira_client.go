package jira

import (
	"fmt"
	"sync/atomic"
	"time"

	jira "github.com/andygrunwald/go-jira"

	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/configloader"
	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/global"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

// IssueRepository 定义了从 Jira 系统获取工单数据的契约。
type IssueRepository interface {
	// GetNewOrUpdatedBugTickets 获取在指定时间戳之后创建或更新的指定类型工单。
	GetNewOrUpdatedBugTickets(lastCheckedTime time.Time, issueType string, isClosed bool) ([]*Issue, error)
	// GetIssueDetails 通过Issue Key获取单个Jira工单的详细信息。
	GetIssueDetails(issueKey string) (*Issue, error)
	// AddCommentToIssue 添加评论到指定的Jira工单（如果需要将Page结果回写到Jira）
	AddCommentToIssue(issueKey string, comment string) error
	// CloseIssue 关闭Jira Issue
	CloseIssue(issueKey string) error
	SetAssignee(jiraKey string, assigneeEmail string) error
	// SetCauseAndSolution 设置Jira Issue的Cause and solution (Note)字段
	SetCauseAndSolution(issueKey string, causeAndSolution string) error
	// SetFeatureDomains 设置Jira Issue的Feature Domains字段
	SetFeatureDomains(issueKey string, featureDomains string) error
}

type IssueRepositoryIns struct {
	baseURL      string
	email        string
	apiToken     string
	jiraClient   *jira.Client
	csPageConfig *atomic.Pointer[configloader.CSPageConfig]
}

// NewIssueRepository creates a new instance of JiraIssueRepository
func NewIssueRepository(jiraEmail, jiraToken string) (*IssueRepositoryIns, error) {
	baseURL := "https://moego.atlassian.net"
	tp := jira.BasicAuthTransport{
		Username: jiraEmail,
		Password: jiraToken,
	}
	client, err := jira.NewClient(tp.Client(), baseURL)
	if err != nil {
		return nil, fmt.Errorf("failed to create Jira client: %w", err)
	}

	return &IssueRepositoryIns{
		baseURL:      baseURL,
		email:        jiraEmail,
		apiToken:     jiraToken,
		jiraClient:   client,
		csPageConfig: &configloader.GlobalNacosCsPageConfig,
	}, nil
}

func getCustomFieldsAsString(key string, customFields map[string]any) string {
	if value, ok := customFields[key]; ok {
		return fmt.Sprint(value)
	}

	return ""
}

func getComponentsAsStringList(components []*jira.Component) []string {
	var componentNames []string
	for _, comp := range components {
		componentNames = append(componentNames, comp.Name)
	}

	return componentNames
}

func (r *IssueRepositoryIns) convertJiraIssueToIssue(issue *jira.Issue) *Issue {
	customFields := r.extractCustomFields(issue.Fields.Unknowns)

	var assignee UserInfo
	if issue.Fields.Assignee != nil {
		assignee = UserInfo{
			DisplayName:  issue.Fields.Assignee.DisplayName,
			EmailAddress: issue.Fields.Assignee.EmailAddress,
		}
	}

	var reporter UserInfo
	if issue.Fields.Reporter != nil {
		reporter = UserInfo{
			DisplayName:  issue.Fields.Reporter.DisplayName,
			EmailAddress: issue.Fields.Reporter.EmailAddress,
		}
	}

	var devEngineer []UserInfo
	if val, ok := customFields[global.DevEngineer]; ok {
		devEngineer, _ = val.([]UserInfo)
	}

	components := getComponentsAsStringList(issue.Fields.Components)

	// 处理Parent字段（用于子任务）
	var parent *Parent
	if issue.Fields.Parent != nil {
		parent = &Parent{
			ID:  issue.Fields.Parent.ID,
			Key: issue.Fields.Parent.Key,
		}
	}

	return &Issue{
		ID:               issue.ID,
		Key:              issue.Key,
		Summary:          issue.Fields.Summary,
		Status:           issue.Fields.Status.Name,
		IssueDescription: getCustomFieldsAsString(global.IssueDescription, customFields),
		Description:      issue.Fields.Description,
		Created:          time.Time(issue.Fields.Created),
		Updated:          time.Time(issue.Fields.Updated),
		Assignee:         assignee,
		Reporter:         reporter,
		Parent:           parent,
		T1OrGeneral:      getCustomFieldsAsString(global.T1OrGeneral, customFields),
		IssuePriority:    getCustomFieldsAsString(global.IssuePriority, customFields),
		CustomerStage:    getCustomFieldsAsString(global.CustomerStage, customFields),
		Components:       components,
		JiraSquad: global.GetTeamFromComponents(components, issue.Key,
			r.csPageConfig.Load().ComponentsSquadsMapping),
		SLABreach:            getCustomFieldsAsString(global.SLABreach, customFields),
		LogoName:             getCustomFieldsAsString(global.LogoName, customFields),
		LocationName:         getCustomFieldsAsString(global.LocationName, customFields),
		DevEngineer:          devEngineer,
		ResolutionTimeCustom: getCustomFieldsAsString(global.ResolutionTime, customFields),
		CreatedByCustom:      getCustomFieldsAsString(global.CreatedBy, customFields),
		FeatureDomains:       getCustomFieldsAsString(global.FeatureDomains, customFields),
	}
}

func (r *IssueRepositoryIns) GetNewOrUpdatedBugTickets(
	lastCheckedTime time.Time, issueType string, isClosed bool) ([]*Issue, error) {
	var statusCondition string
	if isClosed {
		statusCondition = `status = Closed`
	} else {
		statusCondition = `status != Closed`
	}

	jql := fmt.Sprintf(
		`project = "Customer Support" AND %s AND created >= "%s" AND issuetype = "%s"`,
		statusCondition, lastCheckedTime.Format("2006-01-02 15:04"), issueType)

	var allIssues []jira.Issue
	startAt := 0
	maxResults := 50 // Jira API default max is often 50 or 100

	for {
		options := &jira.SearchOptions{
			StartAt:    startAt,
			MaxResults: maxResults,
		}

		issues, resp, err := r.jiraClient.Issue.Search(jql, options)
		if err != nil {
			return nil, fmt.Errorf("failed to search Jira issues: %w", err)
		}

		allIssues = append(allIssues, issues...)

		if resp.Total <= startAt+len(issues) { // Check if all issues have been fetched
			break
		}
		startAt += len(issues) // Increment startAt by the actual number of issues fetched
	}

	var bugTickets []*Issue
	for _, issueSummary := range allIssues { // Use allIssues here
		issue, err := r.GetIssueDetails(issueSummary.Key)
		if err != nil {
			// Log the error and continue with the next issue
			log.Errorf("failed to get details for issue %s: %v", issueSummary.Key, err)

			continue
		}
		bugTickets = append(bugTickets, issue)
	}

	return bugTickets, nil
}

func (r *IssueRepositoryIns) GetIssueDetails(issueKey string) (*Issue, error) {
	issue, _, err := r.jiraClient.Issue.Get(issueKey, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to get Jira issue %s: %w", issueKey, err)
	}
	// log.Infof("GetIssueDetails:key:%v,value: %s", issueKey, lo.Must(json.Marshal(issue)))

	return r.convertJiraIssueToIssue(issue), nil
}

// extractCustomFields 辅助函数，用于从 Jira 工单的 Unknowns 字段中提取自定义字段。
func (r *IssueRepositoryIns) extractCustomFields(unknowns map[string]interface{}) map[string]interface{} {
	customFields := make(map[string]interface{})

	// 定义要提取的自定义字段及其对应的 Jira ID
	fieldMappings := map[string]string{
		global.T1OrGeneral:      "customfield_11184",
		global.IssuePriority:    "customfield_10049",
		global.CustomerStage:    "customfield_11159",
		global.SLABreach:        "customfield_11317",
		global.LogoName:         "customfield_11160",
		global.LocationName:     "customfield_11382", // [T1] Location Name字段的准确ID
		global.DevEngineer:      "customfield_10125",
		global.IssueDescription: "customfield_10340",
		global.ResolutionTime:   "customfield_11349",
		global.CreatedBy:        "customfield_10450",
		global.FeatureDomains:   "customfield_11580",
		global.CauseAndSolution: "customfield_10084",
	}

	for fieldName, fieldID := range fieldMappings {
		if rawValue, ok := unknowns[fieldID]; ok {
			if fieldMap, isMap := rawValue.(map[string]interface{}); isMap {
				if value, hasValue := fieldMap["value"]; hasValue {
					customFields[fieldName] = value
				}
			} else if fieldArray, isArray := rawValue.([]interface{}); isArray {
				// Handle array type for components or multi-user picker
				var userInfos []UserInfo
				var stringValues []string
				isUserPicker := false
				for _, item := range fieldArray {
					if userMap, isMap := item.(map[string]interface{}); isMap {
						if _, hasAccountID := userMap["accountId"]; hasAccountID {
							isUserPicker = true
							userInfos = append(userInfos, UserInfo{
								DisplayName:  fmt.Sprint(userMap["displayName"]),
								EmailAddress: fmt.Sprint(userMap["emailAddress"]),
							})
						} else if name, hasName := userMap["value"]; hasName {
							if strName, isString := name.(string); isString {
								stringValues = append(stringValues, strName)
							}
						}
					}
				}
				if isUserPicker {
					customFields[fieldName] = userInfos
				} else {
					customFields[fieldName] = stringValues
				}
			} else if fieldArray, isString := rawValue.(string); isString {
				customFields[fieldName] = fieldArray
			}
		}
	}

	return customFields
}

// getFieldID returns the Jira custom field ID for a given field name
func (r *IssueRepositoryIns) getFieldID(fieldName string) string {
	// 定义字段名称到Jira ID的映射
	fieldMappings := map[string]string{
		global.T1OrGeneral:      "customfield_11184",
		global.IssuePriority:    "customfield_10049",
		global.CustomerStage:    "customfield_11159",
		global.SLABreach:        "customfield_11317",
		global.LogoName:         "customfield_11160",
		global.LocationName:     "customfield_11382", // [T1] Location Name字段的准确ID
		global.DevEngineer:      "customfield_10125",
		global.IssueDescription: "customfield_10340",
		global.ResolutionTime:   "customfield_11349",
		global.CreatedBy:        "customfield_10450",
		global.FeatureDomains:   "customfield_11580",
		global.CauseAndSolution: "customfield_10084",
	}

	if fieldID, exists := fieldMappings[fieldName]; exists {
		return fieldID
	}

	return ""
}

func (r *IssueRepositoryIns) AddCommentToIssue(_ string, _ string) error {
	// TODO: Implement logic to add comment to an issue
	// This would typically involve making HTTP POST request to Jira's REST API
	return nil
}

func (r *IssueRepositoryIns) CloseIssue(issueKey string) error {
	transitionOptions, _, err := r.jiraClient.Issue.GetTransitions(issueKey)
	if err != nil {
		return fmt.Errorf("failed to get transitions for Jira issue %s: %w", issueKey, err)
	}

	var closeTransitionID string
	for _, transition := range transitionOptions {
		if transition.Name == "Closed" {
			closeTransitionID = transition.ID

			break
		}
	}

	if closeTransitionID == "" {
		return fmt.Errorf("no suitable close transition found for Jira issue %s", issueKey)
	}

	_, err = r.jiraClient.Issue.DoTransition(issueKey, closeTransitionID)
	if err != nil {
		return fmt.Errorf("failed to transition Jira issue %s: %w", issueKey, err)
	}

	return nil
}

func (r *IssueRepositoryIns) SetAssignee(jiraKey string, assigneeEmail string) error {
	// Find the user by email
	users, _, err := r.jiraClient.User.Find(assigneeEmail)
	if err != nil {
		return fmt.Errorf("failed to find user with email %s: %w", assigneeEmail, err)
	}

	if len(users) == 0 {
		return fmt.Errorf("no user found with email %s", assigneeEmail)
	}

	user := users[0]

	// Update the issue with the new assignee
	updateIssue := &jira.Issue{
		Key: jiraKey,
		Fields: &jira.IssueFields{
			Assignee: &user,
		},
	}

	_, _, err = r.jiraClient.Issue.Update(updateIssue)
	if err != nil {
		return fmt.Errorf("failed to update assignee for issue %s: %w", jiraKey, err)
	}

	return nil
}

// SetCauseAndSolution sets the "Cause and solution (Note)" field for a Jira issue
func (r *IssueRepositoryIns) SetCauseAndSolution(issueKey string, causeAndSolution string) error {
	// Get the field ID for Cause and solution (Note)
	fieldID := r.getFieldID(global.CauseAndSolution)
	if fieldID == "" {
		return fmt.Errorf("failed to get field ID for Cause and solution (Note)")
	}

	// Create issue update request with the Cause and solution (Note) field
	update := &jira.Issue{
		Key: issueKey,
		Fields: &jira.IssueFields{
			Unknowns: map[string]interface{}{
				fieldID: causeAndSolution, // Cause and solution (Note) field
			},
		},
	}

	// Update the issue
	_, _, err := r.jiraClient.Issue.Update(update)
	if err != nil {
		return fmt.Errorf("failed to update cause and solution for issue %s: %w", issueKey, err)
	}

	return nil
}

// SetFeatureDomains sets the "Feature Domains" field for a Jira issue
func (r *IssueRepositoryIns) SetFeatureDomains(issueKey string, featureDomains string) error {
	// Get the field ID for Feature Domains
	fieldID := r.getFieldID(global.FeatureDomains)
	if fieldID == "" {
		return fmt.Errorf("failed to get field ID for Feature Domains")
	}

	// Create issue update request with the Feature Domains field
	// For select fields, we need to send an object with "value" field
	update := &jira.Issue{
		Key: issueKey,
		Fields: &jira.IssueFields{
			Unknowns: map[string]interface{}{
				fieldID: map[string]string{"value": featureDomains}, // Feature Domains field
			},
		},
	}

	// Update the issue
	_, _, err := r.jiraClient.Issue.Update(update)
	if err != nil {
		return fmt.Errorf("failed to update feature domains for issue %s: %w", issueKey, err)
	}

	return nil
}
