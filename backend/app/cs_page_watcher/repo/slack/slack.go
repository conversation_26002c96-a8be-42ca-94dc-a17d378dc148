package slack

import (
	"fmt"
	"regexp"
	"strings"

	"github.com/slack-go/slack"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

type Client interface {
	AddMembersToChannel(channelID string, memberEmails []string) error
	AddEmojiToMessage(channelID string, messageTS string, emoji string) error
	JoinChannel(channelID string) error
	CreateChannel(channelName string) (*slack.Channel, error)
	ArchiveChannel(channelID string) error
	SendMessage(channelID string, message string) (timestampTS string, err error)
	SendMessageToThread(channelID string, messageTS string, message string) error
	SendMessageTo<PERSON>erson(memberEmail string, message string) (err error)

	LookUpByEmail(emailList []string) (userIDList []string)
}

type slackClient struct {
	api *slack.Client
}

func NewClient(token string) Client {
	return &slackClient{
		api: slack.New(token),
	}
}

func (s *slackClient) AddMembersToChannel(channelID string, memberEmails []string) error {
	userIDList := s.LookUpByEmail(memberEmails)
	if len(userIDList) == 0 {
		return fmt.Errorf("AddMembersToChannel: no user found")
	}
	_, err := s.api.InviteUsersToConversation(channelID, userIDList...)
	if err != nil {
		return fmt.Errorf("AddMembersToChannel: failed to add members to channel %s: %w", channelID, err)
	}

	return nil
}

func (s *slackClient) SendMessageToThread(channelID string, messageTS string, message string) error {
	_, _, err := s.api.PostMessage(
		channelID,
		slack.MsgOptionText(message, false),
		slack.MsgOptionTS(messageTS),
	)
	if err != nil {
		return fmt.Errorf("failed to send message to thread %s in channel %s: %w", messageTS, channelID, err)
	}

	return nil
}

func (s *slackClient) AddEmojiToMessage(channelID string, messageTS string, emoji string) error {
	err := s.api.AddReaction(emoji, slack.ItemRef{
		Channel:   channelID,
		Timestamp: messageTS,
	})
	if err != nil {
		return fmt.Errorf("failed to add emoji %s to message %s in channel %s: %w", emoji, messageTS, channelID, err)
	}

	return nil
}

func (s *slackClient) JoinChannel(channelID string) error {
	_, _, _, err := s.api.JoinConversation(channelID)
	if err != nil {
		return fmt.Errorf("failed to join channel %s: %w", channelID, err)
	}

	return nil
}

func (s *slackClient) LookUpByEmail(emailList []string) (userIDList []string) {
	userIDList = make([]string, 0, len(emailList))
	for _, email := range emailList {
		if email == "" {
			userIDList = append(userIDList, "UNKNOWN_USER")

			continue
		}
		user, err := s.api.GetUserByEmail(email)
		if err != nil {
			log.Errorf("failed to look up user by email %s: %v", email, err)
			userIDList = append(userIDList, "UNKNOWN_USER")

			continue
		}
		userIDList = append(userIDList, user.ID)
	}

	return userIDList
}

func (s *slackClient) SendMessage(channelID string, message string) (timestampTS string, err error) {
	_, timestampTS, err = s.api.PostMessage(channelID, slack.MsgOptionText(message, false))
	if err != nil {
		return "", fmt.Errorf("failed to send message to channel %s: %w", channelID, err)
	}

	return timestampTS, nil
}

func (s *slackClient) SendMessageToPerson(memberEmail string, message string) (err error) {
	userIDList := s.LookUpByEmail([]string{memberEmail})
	if len(userIDList) == 0 {
		return fmt.Errorf("SendMessageToPerson: no user found for email %s", memberEmail)
	}

	// Open a direct message channel with the user
	channel, _, _, err := s.api.OpenConversation(&slack.OpenConversationParameters{
		Users: []string{userIDList[0]},
	})
	if err != nil {
		return fmt.Errorf("SendMessageToPerson: failed to open IM channel with user %s: %w", memberEmail, err)
	}

	_, _, err = s.api.PostMessage(channel.ID, slack.MsgOptionText(message, false))
	if err != nil {
		return fmt.Errorf("failed to send message to person %s: %w", memberEmail, err)
	}

	return nil
}

// CreateChannel creates a new Slack channel with the given name
func (s *slackClient) CreateChannel(channelName string) (*slack.Channel, error) {
	// Sanitize the channel name to comply with Slack's naming rules
	sanitizedName := s.sanitizeChannelName(channelName)

	channel, err := s.api.CreateConversation(slack.CreateConversationParams{
		ChannelName: sanitizedName,
		IsPrivate:   false,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create channel %s: %w", sanitizedName, err)
	}

	return channel, nil
}

// ArchiveChannel archives a Slack channel with the given ID
func (s *slackClient) ArchiveChannel(channelID string) error {
	if err := s.api.ArchiveConversation(channelID); err != nil {
		return fmt.Errorf("failed to archive channel %s: %w", channelID, err)
	}

	return nil
}

// sanitizeChannelName cleans a string to make it suitable for use as a Slack channel name
// Slack channel names must:
// - Be lowercase
// - Contain only letters, numbers, hyphens, and underscores
// - Be no longer than 80 characters
// - Not start or end with a hyphen or underscore
// - Not have consecutive hyphens or underscores
func (s *slackClient) sanitizeChannelName(name string) string {
	const unknownChannelName = "unknown"

	if name == "" {
		return unknownChannelName
	}

	// Convert to lowercase
	name = strings.ToLower(name)

	// Replace spaces with hyphens
	name = strings.ReplaceAll(name, " ", "-")

	// Remove any characters that are not letters, numbers, hyphens, or underscores
	reg := regexp.MustCompile("[^a-z0-9_-]+")
	name = reg.ReplaceAllString(name, "")

	// Replace multiple consecutive hyphens or underscores with a single one
	reg = regexp.MustCompile("[-_]{2,}")
	name = reg.ReplaceAllString(name, "-")

	// Remove leading/trailing hyphens or underscores
	name = strings.Trim(name, "-_")

	// Limit to 80 characters
	if len(name) > 80 {
		name = name[:80]
		// Make sure we don't end with a hyphen or underscore after truncation
		name = strings.Trim(name, "-_")
	}

	// If the result is empty, return a default value
	if name == "" {
		return unknownChannelName
	}

	return name
}
