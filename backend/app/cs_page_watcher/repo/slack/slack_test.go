package slack

import (
	"testing"

	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/configloader"
	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/global"
)

func TestSanitizeChannelName(t *testing.T) {
	client := &slackClient{}

	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "Normal name",
			input:    "Test Channel",
			expected: "test-channel",
		},
		{
			name:     "Name with brackets",
			input:    "[Test] Channel",
			expected: "test-channel",
		},
		{
			name:     "Name with special characters",
			input:    "Test@Channel#1!",
			expected: "testchannel1",
		},
		{
			name:     "Empty string",
			input:    "",
			expected: "unknown",
		},
		{
			name:     "Only special characters",
			input:    "@#$%^&*()",
			expected: "unknown",
		},
		{
			name:     "Name with underscores and hyphens",
			input:    "test_channel-name",
			expected: "test_channel-name",
		},
		{
			name:     "Long name",
			input:    "This is a very long channel name that exceeds the 80 character limit and should be truncated",
			expected: "this-is-a-very-long-channel-name-that-exceeds-the-80-character-limit-and-should",
		},
		{
			name:     "Name starting with hyphen",
			input:    "-test-channel",
			expected: "test-channel",
		},
		{
			name:     "Name ending with underscore",
			input:    "test-channel_",
			expected: "test-channel",
		},
		{
			name:     "Name with multiple spaces",
			input:    "test   channel",
			expected: "test-channel",
		},
		{
			name:     "Name with uppercase letters",
			input:    "Furryland",
			expected: "furryland",
		},
		{
			name:     "Real case from DM-19",
			input:    "Furryland-xx-DM-19-dm-discussion",
			expected: "furryland-xx-dm-19-dm-discussion",
		},
		{
			name:     "Multiple consecutive hyphens",
			input:    "test--channel",
			expected: "test-channel",
		},
		{
			name:     "Multiple consecutive underscores",
			input:    "test__channel",
			expected: "test-channel",
		},
		{
			name:     "Mixed consecutive separators",
			input:    "test-_-channel",
			expected: "test-channel",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := client.sanitizeChannelName(tt.input)
			if result != tt.expected {
				t.Errorf("sanitizeChannelName(%q) = %q, want %q", tt.input, result, tt.expected)
			}
		})
	}
}

func TestCreateChannel(t *testing.T) {
	t.Skip("manual test - requires proper Slack app scopes")
	// Note: This test requires the Slack app to have the following OAuth scopes:
	// - channels:write - to create channels
	// - groups:write - to create private channels (if needed)
	// - chat:write - to post messages in channels
	// - users:read - to look up users by email

	// Initialize the Slack client with your token
	cfg := configloader.Init("/Users/<USER>/workspace/moego/backend/app/cs_page_watcher/config")
	t.Logf("token:%v", cfg.CsPageWatcher.SlackToken)
	client := NewClient(cfg.CsPageWatcher.SlackToken)

	// Create a new channel
	channel, err := client.CreateChannel("test-channel-for-cs-page-watcher")
	if err != nil {
		t.Fatalf("TestCreateChannel error: %v", err)
	}

	t.Logf("Successfully created channel: ID=%s, Name=%s", channel.ID, channel.Name)
}

func TestArchiveChannel(t *testing.T) {
	t.Skip("manual test - requires a valid channel ID and proper Slack app scopes")
	// Note: This test requires the Slack app to have the channels:write scope

	// Initialize the Slack client with your token
	cfg := configloader.Init("/Users/<USER>/workspace/moego/backend/app/cs_page_watcher/config")
	t.Logf("token:%v", cfg.CsPageWatcher.SlackToken)
	client := NewClient(cfg.CsPageWatcher.SlackToken)

	// Archive an existing channel (replace with a valid channel ID)
	channelID := "C012AB3CD" // Replace with actual channel ID
	err := client.ArchiveChannel(channelID)
	if err != nil {
		t.Fatalf("TestArchiveChannel error: %v", err)
	}

	t.Logf("Successfully archived channel: %s", channelID)
}

func TestSendMessageToPerson(t *testing.T) {
	t.Skip("manual test")
	// Initialize the Slack client with your token
	cfg := configloader.Init("/Users/<USER>/workspace/moego/backend/app/cs_page_watcher/config")
	t.Logf("token:%v", cfg.CsPageWatcher.SlackToken)
	client := NewClient(cfg.CsPageWatcher.SlackToken)

	// Add the emoji to a message
	err := client.SendMessageToPerson("<EMAIL>", "hello world")
	if err != nil {
		t.Fatalf("TestSendMessageToPerson error: %v", err)
	}
}

func TestAddEmojiToMessage(t *testing.T) {
	t.Skip("manual test")
	// Initialize the Slack client with your token
	cfg := configloader.Init("/Users/<USER>/workspace/moego/backend/app/cs_page_watcher/config")
	t.Logf("token:%v", cfg.CsPageWatcher.SlackToken)
	client := NewClient(cfg.CsPageWatcher.SlackToken)

	// Add the emoji to a message
	err := client.AddEmojiToMessage("C092AS8EL1Y", "1750410965.975779", global.EmojiDoing)
	if err != nil {
		t.Fatalf("TestAddEmojiToMessage error: %v", err)
	}
}

func TestSendMessage(t *testing.T) {
	t.Skip("manual test")
	cfg := configloader.Init("/Users/<USER>/workspace/moego/backend/app/cs_page_watcher/config")
	t.Logf("token:%v", cfg.CsPageWatcher.SlackToken)
	client := NewClient(cfg.CsPageWatcher.SlackToken)
	ts, err := client.SendMessage("C092AS8EL1Y", "Hello, world!")
	if err != nil {
		t.Fatalf("TestSendMessage error: %v", err)
	}
	t.Logf("message ts:%v", ts)
}

func TestAddMembersToChannel(t *testing.T) {
	t.Skip("manual test")
	cfg := configloader.Init("/Users/<USER>/workspace/moego/backend/app/cs_page_watcher/config")
	t.Logf("token:%v", cfg.CsPageWatcher.SlackToken)
	client := NewClient(cfg.CsPageWatcher.SlackToken)

	channelID := "C06HHN24LGM"
	// Ensure the bot is in the channel before adding members
	err := client.JoinChannel(channelID)
	if err != nil {
		t.Logf("Bot might already be in channel or failed to join: %v", err)
	} else {
		t.Logf("Bot successfully joined channel")
	}

	err = client.AddMembersToChannel(channelID, []string{
		"<EMAIL>",
	})
	if err != nil {
		t.Fatalf("TestAddMembersToChannel error: %v", err)
	}
}
