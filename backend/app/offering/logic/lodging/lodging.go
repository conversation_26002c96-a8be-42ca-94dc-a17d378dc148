package lodging

import (
	"context"

	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/lodging"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

func New() *Logic {
	return &Logic{
		lodgingRepo: lodging.NewRepository(),
	}
}

type Logic struct {
	lodgingRepo lodging.Repository
}

func (l *Logic) GetLodgingTypeListByCompanyID(ctx context.Context,
	req *offeringpb.GetLodgingTypeListByCompanyIDRequest) (
	*offeringpb.GetLodgingTypeListByCompanyIDResponse, error) {
	// 校验请求
	if err := verifyGetLodgingTypeListRequest(req); err != nil {
		return nil, err
	}

	// 构建过滤器
	filter := &lodging.ListLodgingTypeFilter{
		CompanyID: req.GetCompanyId(),
	}

	// 查询数据库
	lodgingTypes, err := l.lodgingRepo.ListLodgingTypes(ctx, filter)
	if err != nil {
		return nil, err
	}

	// 转换为proto响应
	var pbLodgingTypes []*offeringpb.LodgingType
	for _, lt := range lodgingTypes {
		pbLodgingType, err := l.convertToLodgingTypeInfo(lt)
		if err != nil {
			return nil, err
		}
		pbLodgingTypes = append(pbLodgingTypes, pbLodgingType)
	}

	return &offeringpb.GetLodgingTypeListByCompanyIDResponse{
		LodgingTypes: pbLodgingTypes,
	}, nil
}

func (l *Logic) ListLodgingUnit(ctx context.Context, req *offeringpb.ListLodgingUnitRequest) (
	*offeringpb.ListLodgingUnitResponse, error) {
	// 校验请求
	if err := verifyListLodgingUnitRequest(req); err != nil {
		return nil, err
	}

	// 构建过滤器
	filter := &lodging.ListLodgingUnitFilter{
		BusinessID: req.GetBusinessId(),
	}

	// 应用过滤条件
	if req.GetFilter() != nil && req.GetFilter().GetLodgingTypeId() > 0 {
		filter.LodgingTypeID = req.GetFilter().GetLodgingTypeId()
	}

	// 查询数据库
	lodgingUnits, err := l.lodgingRepo.ListLodgingUnits(ctx, filter)
	if err != nil {
		return nil, err
	}

	// 转换为proto响应
	var pbLodgingUnits []*offeringpb.LodgingUnit
	for _, unit := range lodgingUnits {
		pbLodgingUnit := l.convertToLodgingUnitInfo(unit)
		pbLodgingUnits = append(pbLodgingUnits, pbLodgingUnit)
	}

	return &offeringpb.ListLodgingUnitResponse{
		LodgingUnits: pbLodgingUnits,
	}, nil
}

func verifyGetLodgingTypeListRequest(_ *offeringpb.GetLodgingTypeListByCompanyIDRequest) error {
	return nil
}

func verifyListLodgingUnitRequest(_ *offeringpb.ListLodgingUnitRequest) error {
	return nil
}

func (l *Logic) convertToLodgingTypeInfo(lt *model.LodgingType) (*offeringpb.LodgingType, error) {
	var lodgingUnitType offeringpb.LodgingUnitType
	if lt.Type != nil {
		lodgingUnitType = offeringpb.LodgingUnitType(*lt.Type)
	}

	return &offeringpb.LodgingType{
		Id:                lt.ID,
		Name:              lt.Name,
		Description:       lt.Description,
		PhotoList:         lt.Photo,
		MaxPetNum:         lt.MaxPetNum,
		MaxPetTotalWeight: lt.MaxPetTotalWeight,
		PetSizeIds:        lt.AllowedPetSizeList,
		LodgingUnitType:   lodgingUnitType,
		PetSizeFilter:     lt.PetSizeFilter,
		AllPetSizes:       lt.PetSizeFilter,
		Sort:              lt.Sort,
		Source:            offeringpb.OfferingSource(lt.Source),
	}, nil
}

func (l *Logic) convertToLodgingUnitInfo(unit *model.LodgingUnit) *offeringpb.LodgingUnit {
	return &offeringpb.LodgingUnit{
		Id:            unit.ID,
		Name:          unit.Name,
		BusinessId:    unit.BusinessID,
		LodgingTypeId: unit.LodgingTypeID,
		CameraId:      unit.CameraID,
		Sort:          unit.Sort,
	}
}
