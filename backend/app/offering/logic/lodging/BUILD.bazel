load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "lodging",
    srcs = ["lodging.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/offering/logic/lodging",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/offering/repo/db/lodging",
        "//backend/app/offering/repo/db/model",
        "//backend/proto/offering/v1:offering",
    ],
)
