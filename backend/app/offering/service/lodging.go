package service

import (
	"context"

	"github.com/MoeGolibrary/moego/backend/app/offering/logic/lodging"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/offering/v1"
)

type LodgingService struct {
	lodgingLogic *lodging.Logic
	offeringpb.UnimplementedLodgingServiceServer
}

func NewLodgingService() *LodgingService {
	return &LodgingService{
		lodgingLogic: lodging.New(),
	}
}

func (s *LodgingService) GetLodgingTypeListByCompanyID(ctx context.Context,
	req *offeringpb.GetLodgingTypeListByCompanyIDRequest) (
	*offeringpb.GetLodgingTypeListByCompanyIDResponse, error) {
	return s.lodgingLogic.GetLodgingTypeListByCompanyID(ctx, req)
}

func (s *LodgingService) ListLodgingUnit(ctx context.Context,
	req *offeringpb.ListLodgingUnitRequest) (*offeringpb.ListLodgingUnitResponse, error) {
	return s.lodgingLogic.ListLodgingUnit(ctx, req)
}
