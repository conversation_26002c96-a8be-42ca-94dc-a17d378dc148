package lodging

import (
	"context"

	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/query"
)

//go:generate mockgen -package=mock -destination=mocks/mock_lodging_repo.go . Repository
type Repository interface {
	WithQuery(q *query.Query) Repository

	// LodgingType operations
	ListLodgingTypes(ctx context.Context, filter *ListLodgingTypeFilter) ([]*model.LodgingType, error)

	// LodgingUnit operations
	ListLodgingUnits(ctx context.Context, filter *ListLodgingUnitFilter) ([]*model.LodgingUnit, error)
}

// repository implements the data access logic for lodging types and units.
type repository struct {
	query *query.Query
}

// NewRepository creates a new repository.
func NewRepository() Repository {
	return &repository{query: query.Use(db.GetDB())}
}

// WithQuery sets the query for the repository, useful for multi operation in one transaction.
func (r *repository) WithQuery(q *query.Query) Repository {
	return &repository{query: q}
}

// ListLodgingTypes lists lodging types by filter.
func (r *repository) ListLodgingTypes(ctx context.Context,
	filter *ListLodgingTypeFilter) ([]*model.LodgingType, error) {
	if filter == nil {
		return []*model.LodgingType{}, nil
	}

	return r.query.LodgingType.WithContext(ctx).
		Where(r.query.LodgingType.CompanyID.Eq(filter.CompanyID)).
		Order(r.query.LodgingType.Sort).
		Find()
}

// ListLodgingUnits lists lodging units by filter.
func (r *repository) ListLodgingUnits(ctx context.Context,
	filter *ListLodgingUnitFilter) ([]*model.LodgingUnit, error) {
	if filter == nil {
		return []*model.LodgingUnit{}, nil
	}
	query := r.query.LodgingUnit.WithContext(ctx).
		Where(r.query.LodgingUnit.BusinessID.Eq(filter.BusinessID)).
		Order(r.query.LodgingUnit.Sort)
	// Apply optional filter
	if filter.LodgingTypeID > 0 {
		query = query.Where(r.query.LodgingUnit.LodgingTypeID.Eq(filter.LodgingTypeID))
	}

	return query.Find()
}
