load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "model",
    srcs = [
        "care_type.gen.go",
        "care_type_attribute.gen.go",
        "lodging_type.gen.go",
        "lodging_unit.gen.go",
        "service.gen.go",
        "service_association.gen.go",
        "service_attribute.gen.go",
        "service_auto_rollover.gen.go",
        "service_business_scope.gen.go",
        "service_category.gen.go",
        "service_lodging_scope.gen.go",
        "service_ob_setting.gen.go",
        "service_ob_staff_binding.gen.go",
        "service_staff_scope.gen.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/proto/offering/v1:offering",
        "//backend/proto/organization/v1:organization",
        "@com_github_lib_pq//:pq",
        "@io_gorm_gorm//:gorm",
    ],
)
