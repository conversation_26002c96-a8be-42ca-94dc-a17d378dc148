// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"

	"gorm.io/gorm"
)

const TableNameLodgingUnit = "lodging_unit"

// LodgingUnit mapped from table <lodging_unit>
type LodgingUnit struct {
	ID            int64          `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true" json:"id"`
	CompanyID     int64          `gorm:"column:company_id;type:bigint;not null" json:"company_id"`
	BusinessID    int64          `gorm:"column:business_id;type:bigint;not null;uniqueIndex:idx_lodging_unit_business_type_name,priority:1" json:"business_id"`
	LodgingTypeID int64          `gorm:"column:lodging_type_id;type:bigint;not null;uniqueIndex:idx_lodging_unit_business_type_name,priority:2;comment:lodging template type" json:"lodging_type_id"` // lodging template type
	Name          string         `gorm:"column:name;type:character varying(500);not null;uniqueIndex:idx_lodging_unit_business_type_name,priority:3;comment:lodging name" json:"name"`                // lodging name
	CreatedAt     *time.Time     `gorm:"column:created_at;type:timestamp with time zone;not null;default:CURRENT_TIMESTAMP" json:"created_at"`
	CreatedBy     int64          `gorm:"column:created_by;type:bigint;not null" json:"created_by"`
	UpdatedAt     *time.Time     `gorm:"column:updated_at;type:timestamp with time zone;not null;default:CURRENT_TIMESTAMP" json:"updated_at"`
	UpdatedBy     int64          `gorm:"column:updated_by;type:bigint;not null" json:"updated_by"`
	DeletedAt     gorm.DeletedAt `gorm:"column:deleted_at;type:timestamp with time zone" json:"deleted_at"`
	DeletedBy     int64          `gorm:"column:deleted_by;type:bigint;not null" json:"deleted_by"`
	CameraID      int64          `gorm:"column:camera_id;type:bigint;not null" json:"camera_id"`
	Sort          int32          `gorm:"column:sort;type:integer;not null" json:"sort"`
}

// TableName LodgingUnit's table name
func (*LodgingUnit) TableName() string {
	return TableNameLodgingUnit
}
