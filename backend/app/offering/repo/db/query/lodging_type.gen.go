// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
)

func newLodgingType(db *gorm.DB, opts ...gen.DOOption) lodgingType {
	_lodgingType := lodgingType{}

	_lodgingType.lodgingTypeDo.UseDB(db, opts...)
	_lodgingType.lodgingTypeDo.UseModel(&model.LodgingType{})

	tableName := _lodgingType.lodgingTypeDo.TableName()
	_lodgingType.ALL = field.NewAsterisk(tableName)
	_lodgingType.ID = field.NewInt64(tableName, "id")
	_lodgingType.CompanyID = field.NewInt64(tableName, "company_id")
	_lodgingType.Description = field.NewString(tableName, "description")
	_lodgingType.Name = field.NewString(tableName, "name")
	_lodgingType.Photo = field.NewField(tableName, "photo")
	_lodgingType.MaxPetNum = field.NewInt32(tableName, "max_pet_num")
	_lodgingType.MaxPetTotalWeight = field.NewInt32(tableName, "max_pet_total_weight")
	_lodgingType.CreatedAt = field.NewTime(tableName, "created_at")
	_lodgingType.CreatedBy = field.NewInt64(tableName, "created_by")
	_lodgingType.UpdatedAt = field.NewTime(tableName, "updated_at")
	_lodgingType.UpdatedBy = field.NewInt64(tableName, "updated_by")
	_lodgingType.DeletedAt = field.NewField(tableName, "deleted_at")
	_lodgingType.DeletedBy = field.NewInt64(tableName, "deleted_by")
	_lodgingType.PetSizeFilter = field.NewBool(tableName, "pet_size_filter")
	_lodgingType.AllowedPetSizeList = field.NewField(tableName, "allowed_pet_size_list")
	_lodgingType.Type = field.NewInt16(tableName, "type")
	_lodgingType.Sort = field.NewInt32(tableName, "sort")
	_lodgingType.Source = field.NewField(tableName, "source")

	_lodgingType.fillFieldMap()

	return _lodgingType
}

type lodgingType struct {
	lodgingTypeDo lodgingTypeDo

	ALL                field.Asterisk
	ID                 field.Int64
	CompanyID          field.Int64
	Description        field.String
	Name               field.String
	Photo              field.Field // photo list
	MaxPetNum          field.Int32 // maximum number of pets the lodging can accommodate
	MaxPetTotalWeight  field.Int32 // maximum weight of pets the lodging can accommodate
	CreatedAt          field.Time
	CreatedBy          field.Int64
	UpdatedAt          field.Time
	UpdatedBy          field.Int64
	DeletedAt          field.Field
	DeletedBy          field.Int64
	PetSizeFilter      field.Bool  // if need pet size filter
	AllowedPetSizeList field.Field // allowed pet size list, only when service_filter is true
	Type               field.Int16 // lodging 类型：1-Room/kennel type；2-Area type
	Sort               field.Int32
	Source             field.Field // 1-MoeGo Platform 2-Enterprise Hub

	fieldMap map[string]field.Expr
}

func (l lodgingType) Table(newTableName string) *lodgingType {
	l.lodgingTypeDo.UseTable(newTableName)
	return l.updateTableName(newTableName)
}

func (l lodgingType) As(alias string) *lodgingType {
	l.lodgingTypeDo.DO = *(l.lodgingTypeDo.As(alias).(*gen.DO))
	return l.updateTableName(alias)
}

func (l *lodgingType) updateTableName(table string) *lodgingType {
	l.ALL = field.NewAsterisk(table)
	l.ID = field.NewInt64(table, "id")
	l.CompanyID = field.NewInt64(table, "company_id")
	l.Description = field.NewString(table, "description")
	l.Name = field.NewString(table, "name")
	l.Photo = field.NewField(table, "photo")
	l.MaxPetNum = field.NewInt32(table, "max_pet_num")
	l.MaxPetTotalWeight = field.NewInt32(table, "max_pet_total_weight")
	l.CreatedAt = field.NewTime(table, "created_at")
	l.CreatedBy = field.NewInt64(table, "created_by")
	l.UpdatedAt = field.NewTime(table, "updated_at")
	l.UpdatedBy = field.NewInt64(table, "updated_by")
	l.DeletedAt = field.NewField(table, "deleted_at")
	l.DeletedBy = field.NewInt64(table, "deleted_by")
	l.PetSizeFilter = field.NewBool(table, "pet_size_filter")
	l.AllowedPetSizeList = field.NewField(table, "allowed_pet_size_list")
	l.Type = field.NewInt16(table, "type")
	l.Sort = field.NewInt32(table, "sort")
	l.Source = field.NewField(table, "source")

	l.fillFieldMap()

	return l
}

func (l *lodgingType) WithContext(ctx context.Context) *lodgingTypeDo {
	return l.lodgingTypeDo.WithContext(ctx)
}

func (l lodgingType) TableName() string { return l.lodgingTypeDo.TableName() }

func (l lodgingType) Alias() string { return l.lodgingTypeDo.Alias() }

func (l lodgingType) Columns(cols ...field.Expr) gen.Columns { return l.lodgingTypeDo.Columns(cols...) }

func (l *lodgingType) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := l.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (l *lodgingType) fillFieldMap() {
	l.fieldMap = make(map[string]field.Expr, 18)
	l.fieldMap["id"] = l.ID
	l.fieldMap["company_id"] = l.CompanyID
	l.fieldMap["description"] = l.Description
	l.fieldMap["name"] = l.Name
	l.fieldMap["photo"] = l.Photo
	l.fieldMap["max_pet_num"] = l.MaxPetNum
	l.fieldMap["max_pet_total_weight"] = l.MaxPetTotalWeight
	l.fieldMap["created_at"] = l.CreatedAt
	l.fieldMap["created_by"] = l.CreatedBy
	l.fieldMap["updated_at"] = l.UpdatedAt
	l.fieldMap["updated_by"] = l.UpdatedBy
	l.fieldMap["deleted_at"] = l.DeletedAt
	l.fieldMap["deleted_by"] = l.DeletedBy
	l.fieldMap["pet_size_filter"] = l.PetSizeFilter
	l.fieldMap["allowed_pet_size_list"] = l.AllowedPetSizeList
	l.fieldMap["type"] = l.Type
	l.fieldMap["sort"] = l.Sort
	l.fieldMap["source"] = l.Source
}

func (l lodgingType) clone(db *gorm.DB) lodgingType {
	l.lodgingTypeDo.ReplaceConnPool(db.Statement.ConnPool)
	return l
}

func (l lodgingType) replaceDB(db *gorm.DB) lodgingType {
	l.lodgingTypeDo.ReplaceDB(db)
	return l
}

type lodgingTypeDo struct{ gen.DO }

func (l lodgingTypeDo) Debug() *lodgingTypeDo {
	return l.withDO(l.DO.Debug())
}

func (l lodgingTypeDo) WithContext(ctx context.Context) *lodgingTypeDo {
	return l.withDO(l.DO.WithContext(ctx))
}

func (l lodgingTypeDo) ReadDB() *lodgingTypeDo {
	return l.Clauses(dbresolver.Read)
}

func (l lodgingTypeDo) WriteDB() *lodgingTypeDo {
	return l.Clauses(dbresolver.Write)
}

func (l lodgingTypeDo) Session(config *gorm.Session) *lodgingTypeDo {
	return l.withDO(l.DO.Session(config))
}

func (l lodgingTypeDo) Clauses(conds ...clause.Expression) *lodgingTypeDo {
	return l.withDO(l.DO.Clauses(conds...))
}

func (l lodgingTypeDo) Returning(value interface{}, columns ...string) *lodgingTypeDo {
	return l.withDO(l.DO.Returning(value, columns...))
}

func (l lodgingTypeDo) Not(conds ...gen.Condition) *lodgingTypeDo {
	return l.withDO(l.DO.Not(conds...))
}

func (l lodgingTypeDo) Or(conds ...gen.Condition) *lodgingTypeDo {
	return l.withDO(l.DO.Or(conds...))
}

func (l lodgingTypeDo) Select(conds ...field.Expr) *lodgingTypeDo {
	return l.withDO(l.DO.Select(conds...))
}

func (l lodgingTypeDo) Where(conds ...gen.Condition) *lodgingTypeDo {
	return l.withDO(l.DO.Where(conds...))
}

func (l lodgingTypeDo) Order(conds ...field.Expr) *lodgingTypeDo {
	return l.withDO(l.DO.Order(conds...))
}

func (l lodgingTypeDo) Distinct(cols ...field.Expr) *lodgingTypeDo {
	return l.withDO(l.DO.Distinct(cols...))
}

func (l lodgingTypeDo) Omit(cols ...field.Expr) *lodgingTypeDo {
	return l.withDO(l.DO.Omit(cols...))
}

func (l lodgingTypeDo) Join(table schema.Tabler, on ...field.Expr) *lodgingTypeDo {
	return l.withDO(l.DO.Join(table, on...))
}

func (l lodgingTypeDo) LeftJoin(table schema.Tabler, on ...field.Expr) *lodgingTypeDo {
	return l.withDO(l.DO.LeftJoin(table, on...))
}

func (l lodgingTypeDo) RightJoin(table schema.Tabler, on ...field.Expr) *lodgingTypeDo {
	return l.withDO(l.DO.RightJoin(table, on...))
}

func (l lodgingTypeDo) Group(cols ...field.Expr) *lodgingTypeDo {
	return l.withDO(l.DO.Group(cols...))
}

func (l lodgingTypeDo) Having(conds ...gen.Condition) *lodgingTypeDo {
	return l.withDO(l.DO.Having(conds...))
}

func (l lodgingTypeDo) Limit(limit int) *lodgingTypeDo {
	return l.withDO(l.DO.Limit(limit))
}

func (l lodgingTypeDo) Offset(offset int) *lodgingTypeDo {
	return l.withDO(l.DO.Offset(offset))
}

func (l lodgingTypeDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *lodgingTypeDo {
	return l.withDO(l.DO.Scopes(funcs...))
}

func (l lodgingTypeDo) Unscoped() *lodgingTypeDo {
	return l.withDO(l.DO.Unscoped())
}

func (l lodgingTypeDo) Create(values ...*model.LodgingType) error {
	if len(values) == 0 {
		return nil
	}
	return l.DO.Create(values)
}

func (l lodgingTypeDo) CreateInBatches(values []*model.LodgingType, batchSize int) error {
	return l.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (l lodgingTypeDo) Save(values ...*model.LodgingType) error {
	if len(values) == 0 {
		return nil
	}
	return l.DO.Save(values)
}

func (l lodgingTypeDo) First() (*model.LodgingType, error) {
	if result, err := l.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.LodgingType), nil
	}
}

func (l lodgingTypeDo) Take() (*model.LodgingType, error) {
	if result, err := l.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.LodgingType), nil
	}
}

func (l lodgingTypeDo) Last() (*model.LodgingType, error) {
	if result, err := l.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.LodgingType), nil
	}
}

func (l lodgingTypeDo) Find() ([]*model.LodgingType, error) {
	result, err := l.DO.Find()
	return result.([]*model.LodgingType), err
}

func (l lodgingTypeDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.LodgingType, err error) {
	buf := make([]*model.LodgingType, 0, batchSize)
	err = l.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (l lodgingTypeDo) FindInBatches(result *[]*model.LodgingType, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return l.DO.FindInBatches(result, batchSize, fc)
}

func (l lodgingTypeDo) Attrs(attrs ...field.AssignExpr) *lodgingTypeDo {
	return l.withDO(l.DO.Attrs(attrs...))
}

func (l lodgingTypeDo) Assign(attrs ...field.AssignExpr) *lodgingTypeDo {
	return l.withDO(l.DO.Assign(attrs...))
}

func (l lodgingTypeDo) Joins(fields ...field.RelationField) *lodgingTypeDo {
	for _, _f := range fields {
		l = *l.withDO(l.DO.Joins(_f))
	}
	return &l
}

func (l lodgingTypeDo) Preload(fields ...field.RelationField) *lodgingTypeDo {
	for _, _f := range fields {
		l = *l.withDO(l.DO.Preload(_f))
	}
	return &l
}

func (l lodgingTypeDo) FirstOrInit() (*model.LodgingType, error) {
	if result, err := l.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.LodgingType), nil
	}
}

func (l lodgingTypeDo) FirstOrCreate() (*model.LodgingType, error) {
	if result, err := l.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.LodgingType), nil
	}
}

func (l lodgingTypeDo) FindByPage(offset int, limit int) (result []*model.LodgingType, count int64, err error) {
	result, err = l.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = l.Offset(-1).Limit(-1).Count()
	return
}

func (l lodgingTypeDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = l.Count()
	if err != nil {
		return
	}

	err = l.Offset(offset).Limit(limit).Scan(result)
	return
}

func (l lodgingTypeDo) Scan(result interface{}) (err error) {
	return l.DO.Scan(result)
}

func (l lodgingTypeDo) Delete(models ...*model.LodgingType) (result gen.ResultInfo, err error) {
	return l.DO.Delete(models)
}

func (l *lodgingTypeDo) withDO(do gen.Dao) *lodgingTypeDo {
	l.DO = *do.(*gen.DO)
	return l
}
