// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/MoeGolibrary/moego/backend/app/offering/repo/db/model"
)

func newLodgingUnit(db *gorm.DB, opts ...gen.DOOption) lodgingUnit {
	_lodgingUnit := lodgingUnit{}

	_lodgingUnit.lodgingUnitDo.UseDB(db, opts...)
	_lodgingUnit.lodgingUnitDo.UseModel(&model.LodgingUnit{})

	tableName := _lodgingUnit.lodgingUnitDo.TableName()
	_lodgingUnit.ALL = field.NewAsterisk(tableName)
	_lodgingUnit.ID = field.NewInt64(tableName, "id")
	_lodgingUnit.CompanyID = field.NewInt64(tableName, "company_id")
	_lodgingUnit.BusinessID = field.NewInt64(tableName, "business_id")
	_lodgingUnit.LodgingTypeID = field.NewInt64(tableName, "lodging_type_id")
	_lodgingUnit.Name = field.NewString(tableName, "name")
	_lodgingUnit.CreatedAt = field.NewTime(tableName, "created_at")
	_lodgingUnit.CreatedBy = field.NewInt64(tableName, "created_by")
	_lodgingUnit.UpdatedAt = field.NewTime(tableName, "updated_at")
	_lodgingUnit.UpdatedBy = field.NewInt64(tableName, "updated_by")
	_lodgingUnit.DeletedAt = field.NewField(tableName, "deleted_at")
	_lodgingUnit.DeletedBy = field.NewInt64(tableName, "deleted_by")
	_lodgingUnit.CameraID = field.NewInt64(tableName, "camera_id")
	_lodgingUnit.Sort = field.NewInt32(tableName, "sort")

	_lodgingUnit.fillFieldMap()

	return _lodgingUnit
}

type lodgingUnit struct {
	lodgingUnitDo lodgingUnitDo

	ALL           field.Asterisk
	ID            field.Int64
	CompanyID     field.Int64
	BusinessID    field.Int64
	LodgingTypeID field.Int64  // lodging template type
	Name          field.String // lodging name
	CreatedAt     field.Time
	CreatedBy     field.Int64
	UpdatedAt     field.Time
	UpdatedBy     field.Int64
	DeletedAt     field.Field
	DeletedBy     field.Int64
	CameraID      field.Int64
	Sort          field.Int32

	fieldMap map[string]field.Expr
}

func (l lodgingUnit) Table(newTableName string) *lodgingUnit {
	l.lodgingUnitDo.UseTable(newTableName)
	return l.updateTableName(newTableName)
}

func (l lodgingUnit) As(alias string) *lodgingUnit {
	l.lodgingUnitDo.DO = *(l.lodgingUnitDo.As(alias).(*gen.DO))
	return l.updateTableName(alias)
}

func (l *lodgingUnit) updateTableName(table string) *lodgingUnit {
	l.ALL = field.NewAsterisk(table)
	l.ID = field.NewInt64(table, "id")
	l.CompanyID = field.NewInt64(table, "company_id")
	l.BusinessID = field.NewInt64(table, "business_id")
	l.LodgingTypeID = field.NewInt64(table, "lodging_type_id")
	l.Name = field.NewString(table, "name")
	l.CreatedAt = field.NewTime(table, "created_at")
	l.CreatedBy = field.NewInt64(table, "created_by")
	l.UpdatedAt = field.NewTime(table, "updated_at")
	l.UpdatedBy = field.NewInt64(table, "updated_by")
	l.DeletedAt = field.NewField(table, "deleted_at")
	l.DeletedBy = field.NewInt64(table, "deleted_by")
	l.CameraID = field.NewInt64(table, "camera_id")
	l.Sort = field.NewInt32(table, "sort")

	l.fillFieldMap()

	return l
}

func (l *lodgingUnit) WithContext(ctx context.Context) *lodgingUnitDo {
	return l.lodgingUnitDo.WithContext(ctx)
}

func (l lodgingUnit) TableName() string { return l.lodgingUnitDo.TableName() }

func (l lodgingUnit) Alias() string { return l.lodgingUnitDo.Alias() }

func (l lodgingUnit) Columns(cols ...field.Expr) gen.Columns { return l.lodgingUnitDo.Columns(cols...) }

func (l *lodgingUnit) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := l.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (l *lodgingUnit) fillFieldMap() {
	l.fieldMap = make(map[string]field.Expr, 13)
	l.fieldMap["id"] = l.ID
	l.fieldMap["company_id"] = l.CompanyID
	l.fieldMap["business_id"] = l.BusinessID
	l.fieldMap["lodging_type_id"] = l.LodgingTypeID
	l.fieldMap["name"] = l.Name
	l.fieldMap["created_at"] = l.CreatedAt
	l.fieldMap["created_by"] = l.CreatedBy
	l.fieldMap["updated_at"] = l.UpdatedAt
	l.fieldMap["updated_by"] = l.UpdatedBy
	l.fieldMap["deleted_at"] = l.DeletedAt
	l.fieldMap["deleted_by"] = l.DeletedBy
	l.fieldMap["camera_id"] = l.CameraID
	l.fieldMap["sort"] = l.Sort
}

func (l lodgingUnit) clone(db *gorm.DB) lodgingUnit {
	l.lodgingUnitDo.ReplaceConnPool(db.Statement.ConnPool)
	return l
}

func (l lodgingUnit) replaceDB(db *gorm.DB) lodgingUnit {
	l.lodgingUnitDo.ReplaceDB(db)
	return l
}

type lodgingUnitDo struct{ gen.DO }

func (l lodgingUnitDo) Debug() *lodgingUnitDo {
	return l.withDO(l.DO.Debug())
}

func (l lodgingUnitDo) WithContext(ctx context.Context) *lodgingUnitDo {
	return l.withDO(l.DO.WithContext(ctx))
}

func (l lodgingUnitDo) ReadDB() *lodgingUnitDo {
	return l.Clauses(dbresolver.Read)
}

func (l lodgingUnitDo) WriteDB() *lodgingUnitDo {
	return l.Clauses(dbresolver.Write)
}

func (l lodgingUnitDo) Session(config *gorm.Session) *lodgingUnitDo {
	return l.withDO(l.DO.Session(config))
}

func (l lodgingUnitDo) Clauses(conds ...clause.Expression) *lodgingUnitDo {
	return l.withDO(l.DO.Clauses(conds...))
}

func (l lodgingUnitDo) Returning(value interface{}, columns ...string) *lodgingUnitDo {
	return l.withDO(l.DO.Returning(value, columns...))
}

func (l lodgingUnitDo) Not(conds ...gen.Condition) *lodgingUnitDo {
	return l.withDO(l.DO.Not(conds...))
}

func (l lodgingUnitDo) Or(conds ...gen.Condition) *lodgingUnitDo {
	return l.withDO(l.DO.Or(conds...))
}

func (l lodgingUnitDo) Select(conds ...field.Expr) *lodgingUnitDo {
	return l.withDO(l.DO.Select(conds...))
}

func (l lodgingUnitDo) Where(conds ...gen.Condition) *lodgingUnitDo {
	return l.withDO(l.DO.Where(conds...))
}

func (l lodgingUnitDo) Order(conds ...field.Expr) *lodgingUnitDo {
	return l.withDO(l.DO.Order(conds...))
}

func (l lodgingUnitDo) Distinct(cols ...field.Expr) *lodgingUnitDo {
	return l.withDO(l.DO.Distinct(cols...))
}

func (l lodgingUnitDo) Omit(cols ...field.Expr) *lodgingUnitDo {
	return l.withDO(l.DO.Omit(cols...))
}

func (l lodgingUnitDo) Join(table schema.Tabler, on ...field.Expr) *lodgingUnitDo {
	return l.withDO(l.DO.Join(table, on...))
}

func (l lodgingUnitDo) LeftJoin(table schema.Tabler, on ...field.Expr) *lodgingUnitDo {
	return l.withDO(l.DO.LeftJoin(table, on...))
}

func (l lodgingUnitDo) RightJoin(table schema.Tabler, on ...field.Expr) *lodgingUnitDo {
	return l.withDO(l.DO.RightJoin(table, on...))
}

func (l lodgingUnitDo) Group(cols ...field.Expr) *lodgingUnitDo {
	return l.withDO(l.DO.Group(cols...))
}

func (l lodgingUnitDo) Having(conds ...gen.Condition) *lodgingUnitDo {
	return l.withDO(l.DO.Having(conds...))
}

func (l lodgingUnitDo) Limit(limit int) *lodgingUnitDo {
	return l.withDO(l.DO.Limit(limit))
}

func (l lodgingUnitDo) Offset(offset int) *lodgingUnitDo {
	return l.withDO(l.DO.Offset(offset))
}

func (l lodgingUnitDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *lodgingUnitDo {
	return l.withDO(l.DO.Scopes(funcs...))
}

func (l lodgingUnitDo) Unscoped() *lodgingUnitDo {
	return l.withDO(l.DO.Unscoped())
}

func (l lodgingUnitDo) Create(values ...*model.LodgingUnit) error {
	if len(values) == 0 {
		return nil
	}
	return l.DO.Create(values)
}

func (l lodgingUnitDo) CreateInBatches(values []*model.LodgingUnit, batchSize int) error {
	return l.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (l lodgingUnitDo) Save(values ...*model.LodgingUnit) error {
	if len(values) == 0 {
		return nil
	}
	return l.DO.Save(values)
}

func (l lodgingUnitDo) First() (*model.LodgingUnit, error) {
	if result, err := l.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.LodgingUnit), nil
	}
}

func (l lodgingUnitDo) Take() (*model.LodgingUnit, error) {
	if result, err := l.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.LodgingUnit), nil
	}
}

func (l lodgingUnitDo) Last() (*model.LodgingUnit, error) {
	if result, err := l.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.LodgingUnit), nil
	}
}

func (l lodgingUnitDo) Find() ([]*model.LodgingUnit, error) {
	result, err := l.DO.Find()
	return result.([]*model.LodgingUnit), err
}

func (l lodgingUnitDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.LodgingUnit, err error) {
	buf := make([]*model.LodgingUnit, 0, batchSize)
	err = l.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (l lodgingUnitDo) FindInBatches(result *[]*model.LodgingUnit, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return l.DO.FindInBatches(result, batchSize, fc)
}

func (l lodgingUnitDo) Attrs(attrs ...field.AssignExpr) *lodgingUnitDo {
	return l.withDO(l.DO.Attrs(attrs...))
}

func (l lodgingUnitDo) Assign(attrs ...field.AssignExpr) *lodgingUnitDo {
	return l.withDO(l.DO.Assign(attrs...))
}

func (l lodgingUnitDo) Joins(fields ...field.RelationField) *lodgingUnitDo {
	for _, _f := range fields {
		l = *l.withDO(l.DO.Joins(_f))
	}
	return &l
}

func (l lodgingUnitDo) Preload(fields ...field.RelationField) *lodgingUnitDo {
	for _, _f := range fields {
		l = *l.withDO(l.DO.Preload(_f))
	}
	return &l
}

func (l lodgingUnitDo) FirstOrInit() (*model.LodgingUnit, error) {
	if result, err := l.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.LodgingUnit), nil
	}
}

func (l lodgingUnitDo) FirstOrCreate() (*model.LodgingUnit, error) {
	if result, err := l.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.LodgingUnit), nil
	}
}

func (l lodgingUnitDo) FindByPage(offset int, limit int) (result []*model.LodgingUnit, count int64, err error) {
	result, err = l.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = l.Offset(-1).Limit(-1).Count()
	return
}

func (l lodgingUnitDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = l.Count()
	if err != nil {
		return
	}

	err = l.Offset(offset).Limit(limit).Scan(result)
	return
}

func (l lodgingUnitDo) Scan(result interface{}) (err error) {
	return l.DO.Scan(result)
}

func (l lodgingUnitDo) Delete(models ...*model.LodgingUnit) (result gen.ResultInfo, err error) {
	return l.DO.Delete(models)
}

func (l *lodgingUnitDo) withDO(do gen.Dao) *lodgingUnitDo {
	l.DO = *do.(*gen.DO)
	return l
}
