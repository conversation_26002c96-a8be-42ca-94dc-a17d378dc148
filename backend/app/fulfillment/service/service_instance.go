package service

import (
	"context"

	service "github.com/MoeGolibrary/moego/backend/app/fulfillment/logic/service"
	offeringpb "github.com/MoeGolibrary/moego/backend/proto/fulfillment/v1"
)

type InstanceService struct {
	instance *service.Logic
	offeringpb.UnimplementedInstanceServiceServer
}

func NewInstanceService() *InstanceService {
	return &InstanceService{
		instance: service.New(),
	}
}

func (i *InstanceService) ListServiceInstance(ctx context.Context,
	req *offeringpb.ListServiceInstanceRequest) (*offeringpb.ListServiceInstanceResponse, error) {
	return i.instance.ListServiceInstance(ctx, req)
}

func (i *InstanceService) GetServiceInstanceByIDs(ctx context.Context,
	req *offeringpb.GetServiceInstanceByIDsRequest) (*offeringpb.GetServiceInstanceByIDsResponse, error) {
	return i.instance.GetServiceInstanceByIDs(ctx, req)
}

func (i *InstanceService) GetOverlappingServiceInstances(_ context.Context,
	_ *offeringpb.GetOverlappingServiceInstancesRequest) (*offeringpb.GetOverlappingServiceInstancesResponse, error) {
	return &offeringpb.GetOverlappingServiceInstancesResponse{}, nil
}
