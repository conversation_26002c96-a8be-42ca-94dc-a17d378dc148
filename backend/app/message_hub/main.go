package main

import (
	"github.com/MoeGolibrary/moego/backend/app/message_hub/config"
	"github.com/MoeGolibrary/moego/backend/app/message_hub/service"
	"github.com/MoeGolibrary/moego/backend/common/rpc/codec/grpc"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/config/nacos"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/database/gorm"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/filters/debuglog"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/filters/opentelemetry"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/filters/recovery"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/filters/validation"
	rpc "github.com/MoeGolibrary/moego/backend/common/rpc/framework"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	messagehubpb "github.com/MoeGolibrary/moego/backend/proto/message_hub/v1"
)

func main() {
	s := rpc.NewServer()
	config.Init("./config")

	// 这里需要注册grpc服务
	grpc.Register(s, &messagehubpb.MessageHubService_ServiceDesc, service.NewMessageHub())
	grpc.Register(s, &messagehubpb.TwilioCallbackService_ServiceDesc, service.NewTwilioCallbackService())

	if err := s.Serve(); err != nil {
		log.Fatalf("failed to serve: %v", err)
	}
}
