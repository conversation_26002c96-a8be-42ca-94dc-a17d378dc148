load("@io_bazel_rules_go//go:def.bzl", "go_binary", "go_library")

go_library(
    name = "message_hub_lib",
    srcs = ["main.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/message_hub",
    visibility = ["//visibility:private"],
    deps = [
        "//backend/app/message_hub/config",
        "//backend/app/message_hub/service",
        "//backend/common/rpc/codec/grpc",
        "//backend/common/rpc/config/nacos",
        "//backend/common/rpc/database/gorm",
        "//backend/common/rpc/filters/debuglog",
        "//backend/common/rpc/filters/opentelemetry",
        "//backend/common/rpc/filters/recovery",
        "//backend/common/rpc/filters/validation",
        "//backend/common/rpc/framework",
        "//backend/common/rpc/framework/log",
        "//backend/proto/message_hub/v1:message_hub",
    ],
)

go_binary(
    name = "message_hub",
    embed = [":message_hub_lib"],
    visibility = ["//visibility:public"],
)
