load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "service",
    srcs = [
        "message_hub_service.go",
        "twilio_callback_service.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/message_hub/service",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/message_hub/logic",
        "//backend/common/rpc/framework/log",
        "//backend/proto/message_hub/v1:message_hub",
    ],
)
