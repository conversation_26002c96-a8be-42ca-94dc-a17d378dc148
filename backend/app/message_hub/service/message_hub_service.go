package service

import (
	"context"
	"errors"

	"github.com/MoeGolibrary/moego/backend/app/message_hub/logic"
	messagehubpb "github.com/MoeGolibrary/moego/backend/proto/message_hub/v1"
)

type MessageHub struct {
	messagehubpb.UnimplementedMessageHubServiceServer
	sms  *logic.SmsLogic
	call *logic.CallLogic
}

func NewMessageHub() *MessageHub {
	return &MessageHub{
		sms:  logic.NewSmsLogic(),
		call: logic.NewCallLogic(),
	}
}

func (g MessageHub) SendMessage(ctx context.Context, req *messagehubpb.SendMessageRequest) (
	*messagehubpb.SendMessageResponse, error) {
	switch req.Payload.(type) {
	case *messagehubpb.SendMessageRequest_Sms:
		g.sms.Send(ctx, req.GetSms())
	case *messagehubpb.SendMessageRequest_Call:
		g.call.Send(ctx, req.GetCall())
	default:
		return nil, errors.New("invalid payload type")
	}

	// 返回成功响应
	return &messagehubpb.SendMessageResponse{}, nil
}
