package config

import (
	"fmt"
	"os"
	"sync"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/config"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

var cfg *MessageHubConfig

var initCfg sync.Once

type MessageHubConfig struct {
	Twilio *Twilio `yaml:"twilio"`
}

type Twilio struct {
	AccountSid string `yaml:"account_sid"`
	AuthToken  string `yaml:"auth_token"`
}

func Init(dir string) {
	initCfg.Do(func() {
		env := os.Getenv("MOEGO_ENVIRONMENT")
		if len(env) == 0 {
			env = "local"
			log.Warnf("MOEGO_ENVIRONMENT is not set, using default value: %s", env)
		}

		c, err := config.DefaultConfigLoader.Load(fmt.Sprintf("%s/%s/messagehub.yaml", dir, env))
		if err != nil {
			panic(err)
		}

		cfg = &MessageHubConfig{}
		err = c.Unmarshal(cfg)
		if err != nil {
			panic(err)
		}
	})
}

func GetCfg() *MessageHubConfig {
	return cfg
}
