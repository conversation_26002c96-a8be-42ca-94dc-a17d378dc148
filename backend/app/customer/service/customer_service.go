package service

import (
	"context"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/emptypb"

	actionstate "github.com/MoeGolibrary/moego/backend/app/customer/logic/action_state"
	"github.com/MoeGolibrary/moego/backend/app/customer/logic/customer"
	customersearch "github.com/MoeGolibrary/moego/backend/app/customer/logic/customer_search"
	customerview "github.com/MoeGolibrary/moego/backend/app/customer/logic/customer_view"
	historylog "github.com/MoeGolibrary/moego/backend/app/customer/logic/history_log"
	lifecycle "github.com/MoeGolibrary/moego/backend/app/customer/logic/life_cycle"
	"github.com/MoeGolibrary/moego/backend/app/customer/logic/task"
	customerproducer "github.com/MoeGolibrary/moego/backend/app/customer/repo/producer/customer_producer"
	customerutils "github.com/MoeGolibrary/moego/backend/app/customer/utils"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	"github.com/MoeGolibrary/moego/backend/common/utils/pointer"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v1"
	searchpb "github.com/MoeGolibrary/moego/backend/proto/search/v1"
)

type CustomerService struct {
	cl  *customer.Logic
	tl  *task.Logic
	csl *customersearch.Logic
	lcl *lifecycle.Logic
	asl *actionstate.Logic
	cvl *customerview.Logic
	cp  customerproducer.API
	hll *historylog.Logic
	customerpb.UnimplementedCustomerServiceServer
}

func NewCustomerService() *CustomerService {
	return &CustomerService{
		cl:  customer.New(),
		tl:  task.New(),
		csl: customersearch.New(),
		lcl: lifecycle.New(),
		asl: actionstate.New(),
		cvl: customerview.New(),
		cp:  customerproducer.NewCustomerProducer(),
		hll: historylog.New(),
	}
}

func validateCustomer(req *customerpb.Customer) error {
	if req.PhoneNumber == "" {
		return status.Errorf(codes.InvalidArgument, "phone number is required")
	}
	if req.CompanyId == 0 {
		return status.Errorf(codes.InvalidArgument, "company id is required")
	}
	if req.Type == customerpb.Customer_TYPE_UNSPECIFIED {
		return status.Errorf(codes.InvalidArgument, "customer type is required")
	}

	return nil
}

func convertCustomerToLogic(reqCustomer *customerpb.Customer) *customer.Customer {
	createCustomer := &customer.Customer{
		PreferredBusinessID: reqCustomer.GetPreferredBusinessId(),
		PhoneNumber:         reqCustomer.GetPhoneNumber(),
		GivenName:           reqCustomer.GetGivenName(),
		FamilyName:          reqCustomer.GetFamilyName(),
		Email:               customerutils.ToPointer(reqCustomer.GetEmail()),
		CompanyID:           reqCustomer.GetCompanyId(),
		Source:              reqCustomer.GetSource(),
		Type:                reqCustomer.GetType(),
		LifeCycle:           reqCustomer.GetLifeCycle(),
		ActionState:         reqCustomer.GetActionState(),
		AvatarPath:          reqCustomer.GetAvatarPath(),
		AllocateStaffID:     reqCustomer.GetAllocateStaffId(),
		Contact: &customer.Contact{
			GivenName:   reqCustomer.GetGivenName(),
			FamilyName:  reqCustomer.GetFamilyName(),
			Email:       reqCustomer.GetEmail(),
			PhoneNumber: reqCustomer.GetPhoneNumber(),
			CompanyID:   reqCustomer.GetCompanyId(),
			State:       customerpb.CustomerContact_NORMAL,
			Type:        customerpb.CustomerContact_MAIN,
			IsPrimary:   customerpb.CustomerContact_PRIMARY,
		},
		CustomizeLifeCycleID:   &reqCustomer.CustomizeLifeCycleId,
		CustomizeActionStateID: &reqCustomer.CustomizeActionStateId,
		ClientColor:            reqCustomer.ClientColor,
	}

	reqCustomer.GetCustomizeActionStateId()

	if birthTime := reqCustomer.GetBirthTime(); birthTime != nil {
		birthTime := birthTime.AsTime()
		createCustomer.BirthTime = &birthTime
	}

	if additionalInfo := reqCustomer.GetAdditionalInfo(); additionalInfo != nil {
		createCustomer.ReferralSourceID = additionalInfo.GetReferralSourceId()
		createCustomer.ReferralSourceDesc = additionalInfo.GetReferralSourceDesc()
		createCustomer.PreferredGroomerID = additionalInfo.GetPreferredGroomerId()
		createCustomer.PreferredFrequencyDay = additionalInfo.GetPreferredFrequencyDay()
		createCustomer.PreferredFrequencyType = additionalInfo.GetPreferredFrequencyType()
		createCustomer.PreferredDay = additionalInfo.GetPreferredDay()
		createCustomer.PreferredTime = additionalInfo.GetPreferredTime()
	}

	return createCustomer
}

func (c *CustomerService) CreateCustomer(ctx context.Context,
	request *customerpb.CreateCustomerRequest) (*customerpb.CreateCustomerResponse, error) {
	reqCustomer := request.GetCustomer()
	if reqCustomer == nil {
		return nil, status.Errorf(codes.InvalidArgument, "customer is nil")
	}
	if err := validateCustomer(reqCustomer); err != nil {
		return nil, err
	}

	createCustomer := convertCustomerToLogic(reqCustomer)

	if address := reqCustomer.GetAddress(); address != nil {
		createCustomer.Address = &customer.Address{
			Address1:  address.GetAddress1(),
			Address2:  address.GetAddress2(),
			City:      address.GetCity(),
			State:     address.GetState(),
			Zipcode:   address.GetZipcode(),
			Country:   address.GetRegionCode(),
			CompanyID: reqCustomer.GetCompanyId(),
			Lat:       address.GetLat(),
			Lng:       address.GetLng(),
			Status:    customerpb.Address_NORMAL,
			IsPrimary: pointer.Get(customerpb.Address_PRIMARY),
		}
	}

	customer, err := c.cl.Create(ctx, createCustomer)
	if err != nil {
		if status.Code(err) == codes.AlreadyExists {
			return &customerpb.CreateCustomerResponse{
				Customer: customer.ToPB(),
				IsExist:  true,
			}, nil
		}

		return nil, err
	}

	// collect history log
	if _, err := c.hll.Create(ctx, &historylog.CreateHistoryLogDatum{
		CustomerID:          customer.ID,
		CustomerName:        customerutils.ConvCustomerName(customer.GivenName, customer.FamilyName),
		CustomerPhoneNumber: customer.PhoneNumber,
		BusinessID:          customer.PreferredBusinessID,
		CompanyID:           customer.CompanyID,
		Action: &customerpb.HistoryLog_Action{
			Action: &customerpb.HistoryLog_Action_Create{
				Create: &customerpb.HistoryLog_Create{},
			},
		},
		StaffID:    request.GetStaffId(),
		Source:     convCustomerSourceType(customer.Source).Enum(),
		SourceID:   request.StaffId,
		SourceName: request.StaffName,
	}); err != nil {
		log.ErrorContextf(ctx, "Create Log err, customerID:%d, err:%+v", customer.ID, err)
	}
	if err := c.csl.SyncES(ctx, customer.ID, searchpb.OperationType_CREATE); err != nil {
		log.ErrorContextf(ctx, "SyncES err, customerID:%d, err:%+v", customer.ID, err)
	}
	if err := c.cp.SendCustomerCreateMessage(ctx, &customerproducer.CustomerCreateMessageDatum{
		CustomerID: customer.ID,
		CompanyID:  customer.CompanyID,
		BusinessID: customer.PreferredBusinessID,
	}); err != nil {
		log.ErrorContextf(ctx, "SendCustomerCreateMessage err, customerID:%d, err:%+v", customer.ID, err)
	}

	return &customerpb.CreateCustomerResponse{
		Customer: customer.ToPB(),
		IsExist:  false,
	}, nil
}

func convCustomerSourceType(source string) customerpb.HistoryLog_Source {
	switch source {
	case "manual":
		return customerpb.HistoryLog_STAFF
	default:
		return customerpb.HistoryLog_SOURCE_UNSPECIFIED
	}
}

// todo update

func (c *CustomerService) UpdateCustomer(ctx context.Context,
	request *customerpb.UpdateCustomerRequest) (*customerpb.UpdateCustomerResponse, error) {
	reqCustomer := request.GetCustomer()
	if reqCustomer == nil {
		return nil, status.Errorf(codes.InvalidArgument, "customer is nil")
	}

	updateCustomer := &customer.Customer{
		ID:                     reqCustomer.GetId(),
		PhoneNumber:            reqCustomer.GetPhoneNumber(),
		PreferredBusinessID:    reqCustomer.GetPreferredBusinessId(),
		GivenName:              reqCustomer.GetGivenName(),
		FamilyName:             reqCustomer.GetFamilyName(),
		Email:                  reqCustomer.Email,
		Source:                 reqCustomer.GetSource(),
		AvatarPath:             reqCustomer.GetAvatarPath(),
		LifeCycle:              reqCustomer.GetLifeCycle(),
		ActionState:            reqCustomer.GetActionState(),
		AllocateStaffID:        reqCustomer.GetAllocateStaffId(),
		CustomizeLifeCycleID:   reqCustomer.CustomizeLifeCycleId,
		CustomizeActionStateID: reqCustomer.CustomizeActionStateId,
		ClientColor:            customerutils.ToValue(reqCustomer.ClientColor),
	}

	if additionalInfo := reqCustomer.GetAdditionalInfo(); additionalInfo != nil {
		updateCustomer.ReferralSourceID = additionalInfo.GetReferralSourceId()
		updateCustomer.ReferralSourceDesc = additionalInfo.GetReferralSourceDesc()
		updateCustomer.PreferredGroomerID = additionalInfo.GetPreferredGroomerId()
		updateCustomer.PreferredFrequencyDay = additionalInfo.GetPreferredFrequencyDay()
		updateCustomer.PreferredFrequencyType = additionalInfo.GetPreferredFrequencyType()
		updateCustomer.PreferredDay = additionalInfo.GetPreferredDay()
		updateCustomer.PreferredTime = additionalInfo.GetPreferredTime()
	}

	if address := reqCustomer.GetAddress(); address != nil {
		updateCustomer.Address = &customer.Address{
			ID:        int(address.GetId()),
			Address1:  address.GetAddress1(),
			Address2:  address.GetAddress2(),
			City:      address.GetCity(),
			State:     address.GetState(),
			Zipcode:   address.GetZipcode(),
			Country:   address.GetRegionCode(),
			Lat:       address.GetLat(),
			Lng:       address.GetLng(),
			IsPrimary: address.IsPrimary,
		}
	}

	if contact := reqCustomer.GetContact(); contact != nil {
		updateCustomer.Contact = &customer.Contact{
			ID:          int(contact.GetId()),
			GivenName:   contact.GetGivenName(),
			FamilyName:  contact.GetFamilyName(),
			Email:       contact.GetEmail(),
			PhoneNumber: contact.GetPhoneNumber(),
			IsPrimary:   contact.GetIsPrimary(),
		}
	}
	// 特殊处理的字段
	if birthTime := reqCustomer.GetBirthTime(); birthTime != nil {
		bt := birthTime.AsTime()
		updateCustomer.BirthTime = &bt
	}

	err := c.cl.Update(ctx, updateCustomer)
	if err != nil {
		return nil, err
	}
	if err := c.csl.SyncES(ctx, reqCustomer.GetId(), searchpb.OperationType_INDEX); err != nil {
		log.ErrorContextf(ctx, "SyncES err, customerID:%d, err:%+v", reqCustomer.GetId(), err)
	}

	return &customerpb.UpdateCustomerResponse{}, nil
}

func (c *CustomerService) ListCustomers(ctx context.Context,
	request *customerpb.ListCustomersRequest) (*customerpb.ListCustomersResponse, error) {

	query := &customer.ListCustomersParams{
		PageSize:       int(request.GetPageSize()),
		PageNum:        int(request.GetPageNum()),
		OrderField:     request.GetOrderField(),
		OrderDirection: request.GetOrderDirection(),
		CompanyID:      request.GetCompanyId(),
	}

	if filter := request.GetFilter(); filter != nil {
		if len(filter.GetCustomerIds()) != 0 {
			query.CustomerIDs = filter.GetCustomerIds()
		}
		if filter.GetKeyword() != "" {
			ids, err := c.csl.SearchLead(ctx, request.GetCompanyId(), request.GetFilter().GetKeyword())
			if err != nil {
				return nil, err
			}
			if len(ids) == 0 {
				return &customerpb.ListCustomersResponse{
					Customers: []*customerpb.Customer{},
					Total:     0,
				}, nil
			}
			query.CustomerIDs = append(query.CustomerIDs, ids...)
		}
		if actionState := filter.ActionState; actionState != nil {
			query.ActionState = actionState.Enum()
		}
		if t := filter.Type; t != nil {
			query.Type = t.Enum()
		}
		query.LifeCycle = filter.LifeCycle
		query.CustomizeLifeCycleID = filter.CustomizeLifeCycleId
		query.CustomizeActionStateID = filter.CustomizeActionStateId
		query.MainPhoneNumber = filter.MainPhoneNumber
	}
	customers, err := c.cl.List(ctx, query)
	if err != nil {
		return nil, err
	}
	result := make([]*customerpb.Customer, 0, len(customers.Items))
	for _, customer := range customers.Items {
		result = append(result, customer.ToPB())
	}

	return &customerpb.ListCustomersResponse{
		Customers: result,
		Total:     int32(customers.Total),
	}, nil
}

func (c *CustomerService) GetCustomer(ctx context.Context,
	request *customerpb.GetCustomerRequest) (*customerpb.Customer, error) {
	customer, err := c.cl.Get(ctx, &customer.GetCustomerParams{
		ID: request.GetCustomerId(),
	})
	if err != nil {
		return nil, err
	}

	return customer.ToPB(), nil
}

func (c *CustomerService) DeleteCustomer(ctx context.Context,
	request *customerpb.DeleteCustomerRequest) (*emptypb.Empty, error) {
	err := c.cl.Delete(ctx, request.GetCustomerId())
	if err != nil {
		return nil, err
	}
	if err := c.csl.SyncES(ctx, request.GetCustomerId(), searchpb.OperationType_INDEX); err != nil {
		log.ErrorContextf(ctx, "SyncES err, customerID:%d, err:%+v", request.GetCustomerId(), err)
	}

	return &emptypb.Empty{}, nil
}

func (c *CustomerService) ConvertCustomer(ctx context.Context,
	request *customerpb.ConvertCustomerRequest) (*customerpb.ConvertCustomerResponse, error) {
	if err := c.cl.ConvertCustomer(ctx, request.GetCustomerId()); err != nil {
		log.ErrorContextf(ctx, "ConvertCustomer err, customerID:%d, err:%+v", request.GetCustomerId(), err)

		return nil, err
	}

	return &customerpb.ConvertCustomerResponse{}, nil
}

func (c *CustomerService) ConvertCustomersAttribute(ctx context.Context,
	request *customerpb.ConvertCustomersAttributeRequest) (*customerpb.ConvertCustomersAttributeResponse, error) {
	if err := c.cl.ConvertCustomersAttribute(ctx, &customer.ConvertCustomersAttributeDatum{
		CustomerIDs:            request.CustomerIds,
		CustomizeLifeCycleID:   request.CustomizeLifeCycleId,
		CustomizeActionStateID: request.CustomizeActionStateId,
	}); err != nil {
		log.ErrorContextf(ctx, "ConvertCustomersAttribute err, err:%+v", err)

		return nil, err
	}

	return &customerpb.ConvertCustomersAttributeResponse{}, nil
}

func (c *CustomerService) SyncCustomerSearch(ctx context.Context,
	request *customerpb.SyncCustomerSearchRequest) (*customerpb.SyncCustomerSearchResponse, error) {
	if err := c.csl.SyncES(ctx, request.GetCustomerId(), searchpb.OperationType_INDEX); err != nil {
		return nil, err
	}

	return &customerpb.SyncCustomerSearchResponse{}, nil
}

func (c *CustomerService) CreateCustomerHistoryLog(ctx context.Context,
	request *customerpb.CreateCustomerHistoryLogRequest) (
	*customerpb.CreateCustomerHistoryLogResponse, error) {
	logID, err := c.hll.Create(ctx, &historylog.CreateHistoryLogDatum{
		CustomerID:          request.GetCustomerId(),
		CustomerName:        request.GetCustomerName(),
		CustomerPhoneNumber: request.GetCustomerPhoneNumber(),
		BusinessID:          request.GetBusinessId(),
		CompanyID:           request.GetCompanyId(),
		StaffID:             request.GetStaffId(),
		Action:              request.GetAction(),
		Source:              request.Source,
		SourceID:            request.SourceId,
		SourceName:          request.SourceName,
	})
	if err != nil {
		return nil, err
	}

	return &customerpb.CreateCustomerHistoryLogResponse{
		LogId: logID,
	}, nil
}

func (c *CustomerService) UpdateCustomerHistoryLog(ctx context.Context,
	request *customerpb.UpdateCustomerHistoryLogRequest) (*customerpb.UpdateCustomerHistoryLogResponse, error) {
	if err := c.hll.Update(ctx, &historylog.UpdateHistoryLogDatum{
		LogID:   request.GetLogId(),
		StaffID: request.GetStaffId(),
		Action:  request.GetAction(),
	}); err != nil {
		return nil, err
	}

	return &customerpb.UpdateCustomerHistoryLogResponse{}, nil
}

func (c *CustomerService) ListCustomerHistoryLogs(ctx context.Context,
	request *customerpb.ListCustomerHistoryLogsRequest) (
	*customerpb.ListCustomerHistoryLogsResponse, error) {
	// conv list datum
	listDatum := &historylog.ListHistoryLogsDatum{
		CustomerID: request.CustomerId,
		PageSize:   int(request.GetPageSize()),
		PageNum:    int(request.GetPageNum()),
	}
	if request.Filter != nil {
		listDatum.HistoryLogType = request.Filter.Type
		listDatum.CompanyID = request.Filter.CompanyId
	}

	// select
	historyLogs, total, err := c.hll.List(ctx, listDatum)
	if err != nil {
		return nil, err
	}

	return &customerpb.ListCustomerHistoryLogsResponse{
		HistoryLogs: historyLogs,
		Total:       total,
	}, nil
}

func (c *CustomerService) CreateCustomerTask(ctx context.Context,
	request *customerpb.CreateCustomerTaskRequest) (*customerpb.CreateCustomerTaskResponse, error) {
	taskID, err := c.tl.Create(ctx, &task.CreateTaskDatum{
		CustomerID:      request.GetCustomerId(),
		BusinessID:      request.GetBusinessId(),
		CompanyID:       request.GetCompanyId(),
		StaffID:         request.GetStaffId(),
		Name:            request.GetName(),
		AllocateStaffID: request.AllocateStaffId,
		CompleteTime:    request.CompleteTime,
		State:           customerpb.Task_NEW,
	})
	if err != nil {
		return nil, err
	}

	return &customerpb.CreateCustomerTaskResponse{
		TaskId: taskID,
	}, nil
}

func (c *CustomerService) UpdateCustomerTask(ctx context.Context,
	request *customerpb.UpdateCustomerTaskRequest) (*customerpb.UpdateCustomerTaskResponse, error) {
	if err := c.tl.Update(ctx, &task.UpdateTaskDatum{
		TaskID:          request.GetTaskId(),
		StaffID:         request.GetStaffId(),
		Name:            request.Name,
		AllocateStaffID: request.AllocateStaffId,
		CompleteTime:    request.CompleteTime,
		State:           request.State,
	}); err != nil {
		return nil, err
	}

	return &customerpb.UpdateCustomerTaskResponse{}, nil
}

func (c *CustomerService) ListCustomerTasks(ctx context.Context,
	request *customerpb.ListCustomerTasksRequest) (*customerpb.ListCustomerTasksResponse, error) {
	tasks, err := c.tl.List(ctx, &task.ListTasksDatum{
		CustomerID: request.GetCustomerId(),
	})
	if err != nil {
		return nil, err
	}

	return &customerpb.ListCustomerTasksResponse{
		Tasks: tasks,
	}, nil
}

func (c *CustomerService) DeleteCustomerTask(ctx context.Context,
	request *customerpb.DeleteCustomerTaskRequest) (*emptypb.Empty, error) {
	err := c.tl.Delete(ctx, &task.DeleteTasksDatum{
		TaskID:  request.GetTaskId(),
		StaffID: request.GetStaffId(),
	})
	if err != nil {
		return nil, err
	}

	return &emptypb.Empty{}, nil
}

func (c *CustomerService) CreateAddress(ctx context.Context,
	request *customerpb.CreateAddressRequest) (*customerpb.CreateAddressResponse, error) {
	address := request.GetAddress()
	addressID, err := c.cl.CreateAddress(ctx, &customer.Address{
		CustomerID: request.GetCustomerId(),
		Address1:   address.GetAddress1(),
		Address2:   address.GetAddress2(),
		City:       address.GetCity(),
		State:      address.GetState(),
		Zipcode:    address.GetZipcode(),
		Country:    address.GetRegionCode(),
		Lat:        address.GetLat(),
		Lng:        address.GetLng(),
		CompanyID:  address.GetCompanyId(),
		IsPrimary:  &address.IsPrimary,
	})
	if err != nil {
		return nil, err
	}

	return &customerpb.CreateAddressResponse{
		AddressId: addressID,
	}, nil
}

func (c *CustomerService) UpdateAddress(ctx context.Context,
	request *customerpb.UpdateAddressRequest) (*customerpb.UpdateAddressResponse, error) {
	address := request.GetAddress()
	err := c.cl.UpdateAddress(ctx, &customer.Address{
		ID:        int(address.GetAddressId()),
		Address1:  address.GetAddress1(),
		Address2:  address.GetAddress2(),
		City:      address.GetCity(),
		State:     address.GetState(),
		Zipcode:   address.GetZipcode(),
		Country:   address.GetRegionCode(),
		Lat:       address.GetLat(),
		Lng:       address.GetLng(),
		IsPrimary: address.IsPrimary,
	})
	if err != nil {
		return nil, err
	}

	return &customerpb.UpdateAddressResponse{}, nil
}

func (c *CustomerService) DeleteAddress(ctx context.Context,
	request *customerpb.DeleteAddressRequest) (*emptypb.Empty, error) {
	err := c.cl.DeleteAddress(ctx, request.GetAddressId())
	if err != nil {
		return nil, err
	}

	return &emptypb.Empty{}, nil
}

func (c *CustomerService) ListAddresses(ctx context.Context,
	request *customerpb.ListAddressesRequest) (*customerpb.ListAddressesResponse, error) {
	addresses, err := c.cl.ListAddresses(ctx, &customer.ListAddressesParams{
		CustomerID:     request.GetParent(),
		PageSize:       int(request.GetPageSize()),
		PageNum:        int(request.GetPageNum()),
		OrderField:     request.GetOrderField(),
		OrderDirection: request.GetOrderDirection(),
	})
	if err != nil {
		return nil, err
	}
	result := make([]*customerpb.Address, 0, len(addresses))
	for _, address := range addresses {
		result = append(result, address.ToPB())
	}

	return &customerpb.ListAddressesResponse{
		Addresses: result,
	}, nil
}

func (c *CustomerService) CreateLifeCycle(ctx context.Context,
	request *customerpb.CreateLifeCycleRequest) (*customerpb.CreateLifeCycleResponse, error) {
	lifeCycle, err := c.lcl.Create(ctx, &lifecycle.CreateLifeCycleDatum{
		BusinessID: request.GetBusinessId(),
		CompanyID:  request.GetCompanyId(),
		StaffID:    request.GetStaffId(),
		Name:       request.GetName(),
		Sort:       request.GetSort(),
	})
	if err != nil {
		return nil, err
	}

	return &customerpb.CreateLifeCycleResponse{
		Id: lifeCycle.GetId(),
	}, nil
}

func (c *CustomerService) UpdateLifeCycles(ctx context.Context, request *customerpb.UpdateLifeCyclesRequest) (
	*customerpb.UpdateLifeCyclesResponse, error) {
	datumList := make([]*lifecycle.UpdateLifeCycleDatum, 0, len(request.GetUpdates()))
	for _, update := range request.GetUpdates() {
		if update == nil {
			continue
		}
		datumList = append(datumList, &lifecycle.UpdateLifeCycleDatum{
			ID:   update.GetId(),
			Name: update.Name,
			Sort: update.Sort,
		})
	}
	if err := c.lcl.Update(ctx, datumList, request.GetStaffId(), request.GetCompanyId()); err != nil {
		return nil, err
	}

	return &customerpb.UpdateLifeCyclesResponse{}, nil
}

func (c *CustomerService) ListLifeCycles(ctx context.Context, request *customerpb.ListLifeCyclesRequest) (
	*customerpb.ListLifeCyclesResponse, error) {
	lifeCycles, err := c.lcl.List(ctx, &lifecycle.ListLifeCyclesDatum{
		CompanyIDs: []int64{request.GetCompanyId()},
	})
	if err != nil {
		return nil, err
	}

	return &customerpb.ListLifeCyclesResponse{
		LifeCycles: lifeCycles,
	}, nil
}

func (c *CustomerService) DeleteLifeCycle(ctx context.Context, request *customerpb.DeleteLifeCycleRequest) (
	*emptypb.Empty, error) {
	if err := c.lcl.Delete(ctx, &lifecycle.DeleteLifeCycleDatum{
		ID:      request.GetId(),
		StaffID: request.GetStaffId(),
	}); err != nil {
		return nil, err
	}

	return &emptypb.Empty{}, nil
}

func (c *CustomerService) CreateActionState(ctx context.Context, request *customerpb.CreateActionStateRequest) (
	*customerpb.CreateActionStateResponse, error) {
	actionState, err := c.asl.Create(ctx, &actionstate.CreateActionStateDatum{
		BusinessID: request.GetBusinessId(),
		CompanyID:  request.GetCompanyId(),
		StaffID:    request.GetStaffId(),
		Name:       request.GetName(),
		Sort:       request.GetSort(),
		Color:      request.GetColor(),
	})
	if err != nil {
		return nil, err
	}

	return &customerpb.CreateActionStateResponse{
		Id: actionState.GetId(),
	}, nil
}

func (c *CustomerService) UpdateActionStates(ctx context.Context, request *customerpb.UpdateActionStatesRequest) (
	*customerpb.UpdateActionsStatesResponse, error) {
	datumList := make([]*actionstate.UpdateActionStateDatum, 0, len(request.GetUpdates()))
	for _, update := range request.GetUpdates() {
		if update == nil {
			continue
		}
		datumList = append(datumList, &actionstate.UpdateActionStateDatum{
			ID:    update.GetId(),
			Name:  update.Name,
			Sort:  update.Sort,
			Color: update.Color,
		})
	}
	if err := c.asl.Update(ctx, datumList, request.GetStaffId(), request.GetCompanyId()); err != nil {
		return nil, err
	}

	return &customerpb.UpdateActionsStatesResponse{}, nil
}

func (c *CustomerService) ListActionStates(ctx context.Context, request *customerpb.ListActionStatesRequest) (
	*customerpb.ListActionStatesResponse, error) {
	actionStates, err := c.asl.List(ctx, &actionstate.ListActionStatesDatum{
		CompanyIDs: []int64{request.GetCompanyId()},
	})
	if err != nil {
		return nil, err
	}

	return &customerpb.ListActionStatesResponse{
		ActionStates: actionStates,
	}, nil
}

func (c *CustomerService) DeleteActionState(ctx context.Context, request *customerpb.DeleteActionStateRequest) (
	*emptypb.Empty, error) {
	if err := c.asl.Delete(ctx, &actionstate.DeleteActionStateDatum{
		ID:      request.GetId(),
		StaffID: request.GetStaffId(),
	}); err != nil {
		return nil, err
	}

	return &emptypb.Empty{}, nil
}

func (c *CustomerService) CreateView(ctx context.Context, request *customerpb.CreateViewRequest) (
	*customerpb.CreateViewResponse, error) {
	id, err := c.cvl.Create(ctx, &customerview.CreateViewDatum{
		Title:     request.Title,
		Fields:    request.Fields,
		OrderBy:   request.OrderBy,
		Filter:    request.Filter,
		StaffID:   request.StaffId,
		CompanyID: request.CompanyId,
		Type:      request.Type,
	})
	if err != nil {
		return nil, err
	}

	return &customerpb.CreateViewResponse{
		Id: id,
	}, nil
}

func (c *CustomerService) UpdateView(ctx context.Context, request *customerpb.UpdateViewRequest) (
	*customerpb.UpdateViewResponse, error) {
	if err := c.cvl.Update(ctx, &customerview.UpdateViewDatum{
		ID:        request.Id,
		Title:     request.Title,
		Fields:    request.Fields,
		OrderBy:   request.OrderBy,
		Filter:    request.Filter,
		StaffID:   request.StaffId,
		CompanyID: request.CompanyId,
	}); err != nil {
		return nil, err
	}

	return &customerpb.UpdateViewResponse{}, nil
}

func (c *CustomerService) ListViews(ctx context.Context, request *customerpb.ListViewsRequest) (
	*customerpb.ListViewsResponse, error) {
	views, err := c.cvl.List(ctx, &customerview.ListViewsDatum{
		CompanyID: request.CompanyId,
		StaffID:   request.StaffId,
		Type:      request.Type,
	})
	if err != nil {
		return nil, err
	}

	return &customerpb.ListViewsResponse{
		Views: views,
	}, nil
}

func (c *CustomerService) DeleteView(ctx context.Context, request *customerpb.DeleteViewRequest) (
	*emptypb.Empty, error) {
	if err := c.cvl.Delete(ctx, &customerview.DeleteViewDatum{
		ID:        request.GetId(),
		CompanyID: request.CompanyId,
		StaffID:   request.GetStaffId(),
	}); err != nil {
		return nil, err
	}

	return &emptypb.Empty{}, nil
}
