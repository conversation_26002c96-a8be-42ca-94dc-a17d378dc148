load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "service",
    srcs = [
        "activity_service.go",
        "appointment_consumer.go",
        "customer_merge_consumer.go",
        "customer_query_service.go",
        "customer_service.go",
        "engagement_consumer.go",
        "message_consumer.go",
        "metadata_service.go",
        "online_booking_consumer.go",
        "order_consumer.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/customer/service",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/customer/logic/action_state",
        "//backend/app/customer/logic/activity_log",
        "//backend/app/customer/logic/activity_rel",
        "//backend/app/customer/logic/address",
        "//backend/app/customer/logic/contact",
        "//backend/app/customer/logic/contact_tag",
        "//backend/app/customer/logic/convert",
        "//backend/app/customer/logic/creationsetting:customer_creation_setting",
        "//backend/app/customer/logic/customer",
        "//backend/app/customer/logic/customer_query",
        "//backend/app/customer/logic/customer_related_data",
        "//backend/app/customer/logic/customer_search",
        "//backend/app/customer/logic/customer_view",
        "//backend/app/customer/logic/customerv2",
        "//backend/app/customer/logic/customfield",
        "//backend/app/customer/logic/history_log",
        "//backend/app/customer/logic/life_cycle",
        "//backend/app/customer/logic/metadata",
        "//backend/app/customer/logic/note",
        "//backend/app/customer/logic/source",
        "//backend/app/customer/logic/tag",
        "//backend/app/customer/logic/task",
        "//backend/app/customer/logic/task_v2",
        "//backend/app/customer/repo/appointment",
        "//backend/app/customer/repo/db",
        "//backend/app/customer/repo/grooming",
        "//backend/app/customer/repo/growthbook",
        "//backend/app/customer/repo/membership",
        "//backend/app/customer/repo/order",
        "//backend/app/customer/repo/payment",
        "//backend/app/customer/repo/postgres/customer",
        "//backend/app/customer/repo/producer/customer_merge",
        "//backend/app/customer/repo/producer/customer_producer",
        "//backend/app/customer/repo/staff",
        "//backend/app/customer/utils",
        "//backend/common/rpc/framework/errs",
        "//backend/common/rpc/framework/log",
        "//backend/common/utils/pointer",
        "//backend/common/utils/random",
        "//backend/proto/customer/v1:customer",
        "//backend/proto/customer/v2:customer",
        "//backend/proto/search/v1:search",
        "@com_github_bytedance_sonic//:sonic",
        "@com_github_ibm_sarama//:sarama",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/models/engagement/v1:engagement",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/models/event_bus/v1:event_bus",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/models/offering/v1:offering",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/models/order/v1:order",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/models/organization/v1:organization",
        "@io_gorm_gorm//:gorm",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//status",
        "@org_golang_google_protobuf//encoding/protojson",
        "@org_golang_google_protobuf//proto",
        "@org_golang_google_protobuf//types/known/emptypb",
        "@org_golang_x_sync//errgroup",
    ],
)
