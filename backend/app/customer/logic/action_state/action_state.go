package actionstate

import (
	"context"
	"fmt"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	actionstate "github.com/MoeGolibrary/moego/backend/app/customer/repo/db/customer_action_state"
	"github.com/MoeGolibrary/moego/backend/app/customer/repo/redis"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/errs"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v1"
)

type Logic struct {
	actionStateRepo actionstate.ReadWriter
	redisRepo       redis.API
}

func New() *Logic {
	return &Logic{
		actionStateRepo: actionstate.New(),
		redisRepo:       redis.New(),
	}
}

func (l *Logic) Create(ctx context.Context, datum *CreateActionStateDatum) (*customerpb.CustomizeActionState, error) {
	// check
	if datum == nil || datum.CompanyID == 0 || datum.Name == "" || datum.Sort == 0 || datum.Color == "" {
		log.InfoContextf(ctx, "Create ActionState params is invalid, datum: %+v", datum)

		return nil, status.Errorf(codes.InvalidArgument, "params is invalid")
	}

	// check name
	existNameList, err := l.actionStateRepo.List(ctx, &actionstate.ListActionStatesDatum{
		CompanyIDs: []int64{datum.CompanyID},
		Names:      []string{datum.Name},
	})
	if err != nil {
		log.ErrorContextf(ctx, "Create ActionState List same name err, err: %+v", err)

		return nil, err
	}
	if len(existNameList) > 0 {
		log.InfoContextf(ctx, "Create ActionState name is exist, datum: %+v", datum)

		return nil, errs.New(customerpb.ErrCode_ERR_CODE_ACTION_STATE_NAME_EXIST)
	}

	// save to db
	db := convActionStateDB(datum)
	if err := l.actionStateRepo.Create(ctx, db); err != nil {
		return nil, err
	}

	return convActionStatePB(db), nil
}

func convActionStateDB(datum *CreateActionStateDatum) *actionstate.CustomerActionState {
	return &actionstate.CustomerActionState{
		CompanyID:  datum.CompanyID,
		BusinessID: datum.BusinessID,
		CreatedBy:  datum.StaffID,
		UpdatedBy:  datum.StaffID,
		Name:       datum.Name,
		Sort:       datum.Sort,
		Color:      datum.Color,
	}
}

func (l *Logic) Update(ctx context.Context, datumList []*UpdateActionStateDatum, staffID, companyID int64) error {
	// check
	if staffID <= 0 || companyID <= 0 {
		log.InfoContextf(ctx, "Update ActionState staffID or companyID is invalid, "+
			"staffID: %d, companyID: %d", staffID, companyID)

		return status.Errorf(codes.InvalidArgument, "params is invalid")
	}

	// datum check && collect name && conv to db entity
	names := make([]string, 0, len(datumList))
	idChangeNameMap := make(map[int64]*UpdateActionStateDatum, len(datumList))
	updates := make([]*actionstate.CustomerActionState, 0, len(datumList))
	for _, datum := range datumList {
		if datum == nil || datum.ID <= 0 ||
			(datum.Name == nil && datum.Sort == nil && datum.Color == nil) ||
			(datum.Name != nil && *datum.Name == "") ||
			(datum.Sort != nil && *datum.Sort <= 0) ||
			(datum.Color != nil && *datum.Color == "") {
			log.InfoContextf(ctx, "Update ActionState params is invalid, datum: %+v", datum)

			return status.Errorf(codes.InvalidArgument, "params is invalid")
		}
		update := &actionstate.CustomerActionState{
			ID:        datum.ID,
			UpdatedBy: staffID,
		}
		if datum.Name != nil {
			update.Name = *datum.Name
			names = append(names, *datum.Name)
			idChangeNameMap[datum.ID] = datum
		}
		if datum.Sort != nil {
			update.Sort = *datum.Sort
		}
		if datum.Color != nil {
			update.Color = *datum.Color
		}
		updates = append(updates, update)
	}

	// check names
	if len(names) > 0 {
		existNameList, err := l.actionStateRepo.List(ctx, &actionstate.ListActionStatesDatum{
			CompanyIDs: []int64{companyID},
			Names:      names,
		})
		if err != nil {
			log.ErrorContextf(ctx, "Update ActionState List same name err, err: %+v", err)

			return err
		}
		for _, existName := range existNameList {
			// existName state is the same update id and name, skip
			if idChangeNameMap[existName.ID] != nil && *idChangeNameMap[existName.ID].Name == existName.Name {
				continue
			}
			log.InfoContextf(ctx, "Update ActionState name is exist, names: %+v", names)

			return errs.New(customerpb.ErrCode_ERR_CODE_ACTION_STATE_NAME_EXIST)
		}
	}

	// save to db
	if err := l.actionStateRepo.WithTransaction(ctx, func(api actionstate.ReadWriter) error {
		for _, update := range updates {
			if err := api.Update(ctx, update); err != nil {
				return err
			}
		}

		return nil
	}); err != nil {
		log.ErrorContextf(ctx, "Update ActionState WithTransaction err, err:%+v", err)

		return err
	}

	return nil
}

func (l *Logic) List(ctx context.Context, datum *ListActionStatesDatum) ([]*customerpb.CustomizeActionState, error) {
	// check
	if datum == nil || (len(datum.CompanyIDs) == 0 && len(datum.IDs) == 0) {
		log.InfoContextf(ctx, "List ActionState params is invalid, datum: %+v", datum)

		return nil, status.Errorf(codes.InvalidArgument, "params is invalid")
	}

	// list db
	states, err := l.actionStateRepo.List(ctx, &actionstate.ListActionStatesDatum{
		CompanyIDs: datum.CompanyIDs,
		IDs:        datum.IDs},
	)
	if err != nil {
		return nil, err
	}

	// if empty and not init, init default action state
	if len(states) == 0 {
		for _, companyID := range datum.CompanyIDs {
			initStates, err := l.InitActionState(ctx, companyID)
			if err != nil {
				return nil, err
			}
			states = initStates
		}
	}

	return convActionStatesPB(states), nil
}

func (l *Logic) InitActionState(ctx context.Context, companyID int64) (
	[]*actionstate.CustomerActionState, error) {
	// check init before
	initKey := fmt.Sprintf(redis.CompanyInitActionStateKey, companyID)
	value, err := l.redisRepo.Get(ctx, initKey)
	if err != nil {
		return nil, err
	}
	if value != "" {
		return []*actionstate.CustomerActionState{}, nil
	}

	// add lock
	lock := fmt.Sprintf(redis.CompanyInitActionStateLockKey, companyID)
	if err := l.redisRepo.Lock(ctx, lock, redis.CompanyInitActionStateLockTTL); err != nil {
		return nil, err
	}
	defer func() {
		if err := l.redisRepo.Unlock(ctx, lock); err != nil {
			log.ErrorContextf(ctx, "InitActionState Failed to release lock, lock:%s", lock)
		}
	}()

	// init
	if err := l.actionStateRepo.CreateBatch(ctx, buildDefaultLifeCycles(companyID)); err != nil {
		return nil, err
	}

	// set key
	if err := l.redisRepo.Set(ctx, initKey, "1"); err != nil {
		return nil, err
	}

	// get data
	states, err := l.actionStateRepo.List(ctx,
		&actionstate.ListActionStatesDatum{CompanyIDs: []int64{companyID}})
	if err != nil {
		return nil, err
	}

	log.InfoContextf(ctx, "InitActionState success, companyID:%d, states:%+v", companyID, states)

	return states, nil

}

var (
	defaultActionStateNames = []string{"To follow up", "Attempted to Contact", "Connected", "Bad Timing"}
)

func buildDefaultLifeCycles(companyID int64) []*actionstate.CustomerActionState {
	res := make([]*actionstate.CustomerActionState, 0, len(defaultActionStateNames))
	for i, name := range defaultActionStateNames {
		res = append(res, &actionstate.CustomerActionState{
			CompanyID:  companyID,
			BusinessID: 0,
			CreatedBy:  0,
			UpdatedBy:  0,
			Name:       name,
			Sort:       int32(i + 1),
			Color:      "#660033", // TODO 提供对应的color值
		})
	}

	return res
}

func convActionStatesPB(states []*actionstate.CustomerActionState) []*customerpb.CustomizeActionState {
	res := make([]*customerpb.CustomizeActionState, 0, len(states))
	for _, state := range states {
		if state == nil {
			continue
		}
		res = append(res, convActionStatePB(state))
	}

	return res
}

func convActionStatePB(state *actionstate.CustomerActionState) *customerpb.CustomizeActionState {
	return &customerpb.CustomizeActionState{
		Id:    state.ID,
		Name:  state.Name,
		Sort:  state.Sort,
		Color: state.Color,
	}
}

func (l *Logic) Delete(ctx context.Context, datum *DeleteActionStateDatum) error {
	// check
	if datum == nil || datum.ID <= 0 {
		log.InfoContextf(ctx, "Delete ActionState params is invalid, datum: %+v", datum)

		return status.Errorf(codes.InvalidArgument, "params is invalid")
	}

	// delete db
	if err := l.actionStateRepo.Delete(ctx, datum.ID, datum.StaffID); err != nil {
		return err
	}

	return nil
}
