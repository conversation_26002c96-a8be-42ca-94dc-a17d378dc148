package contacttag

import (
	"context"
	"errors"
	"time"

	"google.golang.org/grpc/codes"
	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres"
	contacttagrepo "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/contact_tag"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/errs"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

type Logic struct {
	contactTagRepo contacttagrepo.Repository
}

func New() *Logic {
	return &Logic{
		contactTagRepo: contacttagrepo.New(),
	}
}

func NewByParams(
	contactTagRepo contacttagrepo.Repository,
) *Logic {
	return &Logic{
		contactTagRepo: contactTagRepo,
	}
}

func (l *Logic) Create(ctx context.Context, contactTag *ContactTag) (*ContactTag, error) {
	now := time.Now().UTC()
	contactTag.State = customerpb.ContactTag_ACTIVE
	contactTag.CreatedTime = now
	contactTag.UpdatedTime = now
	dbContactTag := contactTag.ToDB()

	dbContactTag, err := l.contactTagRepo.Create(ctx, dbContactTag)
	if err != nil {
		return nil, err
	}

	return convertToContactTag(dbContactTag), nil
}

func (l *Logic) Get(ctx context.Context, id int64) (*ContactTag, error) {
	dbContactTag, err := l.contactTagRepo.Get(ctx, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errs.New(customerpb.ErrCode_ERR_CODE_CONTACT_TAG_NOT_FOUND)
		}

		return nil, err
	}

	return convertToContactTag(dbContactTag), nil
}

func (l *Logic) List(ctx context.Context, req *ListContactTagsRequest) (*ListContactTagsResponse, error) {
	if req.Filter == nil {
		return nil, errs.Newm(codes.InvalidArgument, "filter is required")
	}
	if req.OrderBy == nil {
		req.OrderBy = &ListContactTagsOrderBy{
			Field:     customerpb.ListContactTagsRequest_Sorting_CREATED_TIME,
			Direction: customerpb.Direction_DESC,
		}
	}

	// 默认的Tags
	defaultTags, err := l.contactTagRepo.ListByCursor(ctx, &contacttagrepo.ListFilter{
		States:           []customerpb.ContactTag_State{customerpb.ContactTag_ACTIVE},
		OrganizationType: customerpb.OrganizationRef_SYSTEM,
		OrganizationID:   0,
	}, &contacttagrepo.Pagination{
		PageSize: 1000,
	}, &contacttagrepo.OrderBy{
		Field:     customerpb.ListContactTagsRequest_Sorting_CREATED_TIME,
		Direction: customerpb.Direction_DESC,
	})
	if err != nil {
		return nil, err
	}

	dbContactTags, err := l.contactTagRepo.ListByCursor(ctx, &contacttagrepo.ListFilter{
		IDs:              req.Filter.IDs,
		States:           req.Filter.States,
		Types:            req.Filter.Types,
		Name:             req.Filter.Name,
		OrganizationType: req.Filter.OrganizationType,
		OrganizationID:   req.Filter.OrganizationID,
	}, &contacttagrepo.Pagination{
		PageSize:        req.Pagination.PageSize,
		Cursor:          req.Pagination.DecodeCursor(),
		ReturnTotalSize: req.Pagination.ReturnTotalSize,
	}, &contacttagrepo.OrderBy{
		Field:     req.OrderBy.Field,
		Direction: req.OrderBy.Direction,
	})
	if err != nil {
		return nil, err
	}

	dbContactTags.Data = append(defaultTags.Data, dbContactTags.Data...)
	result := &ListContactTagsResponse{
		ContactTags: convertToContactTags(dbContactTags.Data),
		HasNext:     dbContactTags.HasNext,
	}

	if dbContactTags.TotalCount != nil {
		count := *dbContactTags.TotalCount + *defaultTags.TotalCount
		result.TotalSize = &count
	}

	if dbContactTags.HasNext && len(dbContactTags.Data) > 0 {
		lastContactTag := dbContactTags.Data[len(dbContactTags.Data)-1]
		cursor := postgres.Cursor{
			ID:        lastContactTag.ID,
			CreatedAt: lastContactTag.CreatedTime,
		}
		result.NextToken = cursor.EncodeCursor()
	}

	return result, nil
}

func (l *Logic) Update(ctx context.Context, updateRef *UpdateContactTagRequest) (*ContactTag, error) {
	dbContactTag, err := l.Get(ctx, updateRef.ID)
	if err != nil {
		return nil, err
	}

	now := time.Now().UTC()
	dbContactTag.Name = updateRef.Name
	dbContactTag.Color = updateRef.Color
	dbContactTag.SortOrder = updateRef.SortOrder
	dbContactTag.Description = updateRef.Description
	dbContactTag.UpdatedTime = now
	dbContactTag.State = updateRef.State

	// update contact tag
	updatedContactTag, err := l.contactTagRepo.Update(ctx, dbContactTag.ToDB())
	if err != nil {
		return nil, err
	}

	return convertToContactTag(updatedContactTag), nil
}

func (l *Logic) Delete(ctx context.Context, id int64) error {
	_, err := l.Get(ctx, id)
	if err != nil {
		return err
	}

	err = l.contactTagRepo.Delete(ctx, id)
	if err != nil {
		return err
	}

	return nil
}

func convertToContactTags(dbContactTags []*contacttagrepo.ContactTag) []*ContactTag {
	contactTags := make([]*ContactTag, 0, len(dbContactTags))
	for _, dbContactTag := range dbContactTags {
		contactTags = append(contactTags, convertToContactTag(dbContactTag))
	}

	return contactTags
}

func convertToContactTag(dbContactTag *contacttagrepo.ContactTag) *ContactTag {
	return &ContactTag{
		ID:               dbContactTag.ID,
		OrganizationType: dbContactTag.OrganizationType,
		OrganizationID:   dbContactTag.OrganizationID,
		Name:             dbContactTag.Name,
		Color:            dbContactTag.Color,
		SortOrder:        dbContactTag.SortOrder,
		Description:      dbContactTag.Description,
		State:            dbContactTag.State,
		Type:             dbContactTag.Type,
		CreatedTime:      dbContactTag.CreatedTime,
		UpdatedTime:      dbContactTag.UpdatedTime,
		DeletedTime:      dbContactTag.DeletedTime,
	}
}
