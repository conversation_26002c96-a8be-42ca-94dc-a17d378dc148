load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "customer_creation_setting",
    srcs = [
        "entity.go",
        "logic.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/customer/logic/creationsetting",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/customer/repo/postgres/creationsetting",
        "//backend/proto/customer/v2:customer",
    ],
)
