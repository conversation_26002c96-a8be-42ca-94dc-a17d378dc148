package creationsetting

import (
	"time"

	repoentity "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/creationsetting"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

// CustomerCreationSetting logic层实体
type CustomerCreationSetting struct {
	ID                     int64
	CompanyID              int64
	EnableCreationFromSMS  bool
	EnableCreationFromCall bool
	CreatedTime            time.Time
	UpdatedTime            time.Time
}

// ToRepo 转换为repo层实体
func (c *CustomerCreationSetting) ToRepo() *repoentity.CustomerCreationSetting {
	return &repoentity.CustomerCreationSetting{
		ID:                     c.ID,
		CompanyID:              c.CompanyID,
		EnableCreationFromSMS:  c.EnableCreationFromSMS,
		EnableCreationFromCall: c.EnableCreationFromCall,
		CreatedTime:            c.CreatedTime,
		UpdatedTime:            c.UpdatedTime,
	}
}

// FromRepo 从repo层实体转换
func (c *CustomerCreationSetting) FromRepo(repo *repoentity.CustomerCreationSetting) {
	if repo == nil {
		return
	}
	c.ID = repo.ID
	c.CompanyID = repo.CompanyID
	c.EnableCreationFromSMS = repo.EnableCreationFromSMS
	c.EnableCreationFromCall = repo.EnableCreationFromCall
	c.CreatedTime = repo.CreatedTime
	c.UpdatedTime = repo.UpdatedTime
}

// ToPB 转换为protobuf实体
func (c *CustomerCreationSetting) ToPB() *customerpb.CustomerCreationSetting {
	return &customerpb.CustomerCreationSetting{
		EnableCreationViaSms:  c.EnableCreationFromSMS,
		EnableCreationViaCall: c.EnableCreationFromCall,
	}
}

// FromPB 从protobuf实体转换
func (c *CustomerCreationSetting) FromPB(pb *customerpb.CustomerCreationSetting) {
	if pb == nil {
		return
	}
	c.EnableCreationFromSMS = pb.EnableCreationViaSms
	c.EnableCreationFromCall = pb.EnableCreationViaCall
}
