package creationsetting

import (
	"context"

	repo "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/creationsetting"
)

type Logic struct {
	repo repo.Repository
}

func New() *Logic {
	return &Logic{
		repo: repo.New(),
	}
}

// GetByCompanyID 根据公司ID获取客户创建设置
func (l *Logic) GetByCompanyID(ctx context.Context, companyID int64) (*CustomerCreationSetting, error) {
	repoSetting, err := l.repo.GetByCompanyID(ctx, companyID)
	if err != nil {
		return nil, err
	}

	setting := &CustomerCreationSetting{}
	setting.FromRepo(repoSetting)

	return setting, nil
}

// Update 更新客户创建设置
func (l *Logic) Update(ctx context.Context, setting *CustomerCreationSetting) (*CustomerCreationSetting, error) {
	repoSetting := setting.ToRepo()
	updatedRepoSetting, err := l.repo.Update(ctx, repoSetting)
	if err != nil {
		return nil, err
	}

	updatedSetting := &CustomerCreationSetting{}
	updatedSetting.FromRepo(updatedRepoSetting)

	return updatedSetting, nil
}

// Create 创建客户创建设置
func (l *Logic) Create(ctx context.Context, setting *CustomerCreationSetting) (*CustomerCreationSetting, error) {
	repoSetting := setting.ToRepo()
	createdRepoSetting, err := l.repo.Create(ctx, repoSetting)
	if err != nil {
		return nil, err
	}

	createdSetting := &CustomerCreationSetting{}
	createdSetting.FromRepo(createdRepoSetting)

	return createdSetting, nil
}
