load("@io_bazel_rules_go//go:def.bzl", "go_binary", "go_library")

go_library(
    name = "generateerrorcode_lib",
    srcs = ["main.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/tools/cmd/generateerrorcode",
    visibility = ["//visibility:private"],
    deps = [
        "//backend/tools/cmd/findsameerrorcode",
        "@in_gopkg_yaml_v3//:yaml_v3",
    ],
)

go_binary(
    name = "generateerrorcode",
    embed = [":generateerrorcode_lib"],
    visibility = ["//visibility:public"],
)
