package findsameerrorcode

import (
	"bufio"
	"fmt"
	"os"
	"strings"
)

// Duplicate 在文件夹中查找是否存在包含指定错误码的文件
func Duplicate(pathName, errCode string) (bool, error) {
	fis, err := os.ReadDir(pathName)
	if err != nil {
		return false, fmt.Errorf("读取文件目录失败，pathname=%v, err=%v", pathName, err)
	}
	for _, fi := range fis {
		fullname := pathName + "/" + fi.Name()
		if fi.IsDir() {
			exits, err := Duplicate(fullname, errCode)
			if err != nil {
				return false, err
			}
			if exits {
				return true, nil
			}
			continue
		}
		if strings.Contains(fi.Name(), ".proto") {
			exits, err := isErrCodeExits(fullname, errCode)
			if err != nil {
				return false, err
			}
			if exits {
				return true, nil
			}
		}
	}
	return false, nil
}

func isErrCodeExits(fullname, errCode string) (bool, error) {
	f, err := os.Open(fullname)
	if err != nil {
		return false, fmt.Errorf("读取文件失败,fullname=%v, err=%v", fullname, err)
	}
	defer f.Close()

	scanner := bufio.NewScanner(f)
	for scanner.Scan() {
		if strings.Contains(scanner.Text(), errCode) {
			return true, nil
		}
	}
	return false, nil
}
