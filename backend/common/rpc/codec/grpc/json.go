package grpc

// for calling grpc with json
// curl example:
// echo -en '\x00\x00\x00\x00\x0c{"msg": "1"}' | curl  --http2-prior-knowledge --output -  \
// -H "Content-Type: application/grpc+json" \
// -H "TE:trailers" \
// --data-binary @- \
// http://127.0.0.1:9090/backend.proto.helloworld.v1.Greeter/Hello

import (
	"google.golang.org/grpc/encoding"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/codec"
)

func init() {
	encoding.RegisterCodec(&codec.ProtoJSONSerialization{})
}
