package grpc

import "github.com/MoeGolibrary/moego/backend/common/rpc/framework/codec"

var (
	// DefaultServerCodec Default codec instance
	DefaultServerCodec = &ServerCodec{}
	// DefaultClientCodec Default client codec
	DefaultClientCodec = &ClientCodec{}
)

// init Register grpc codec and grpc server transport
func init() {
	codec.Register("grpc", DefaultServerCodec, DefaultClientCodec)
}

// ServerCodec Server codec
type ServerCodec struct {
}

// Decode ServerCodec.Decode for decoding
func (s *ServerCodec) Decode(_ codec.Msg, reqbuf []byte) (reqbody []byte, err error) {
	return reqbuf, nil
}

// Encode ServerCodec.Encode for coding
func (s *ServerCodec) Encode(_ codec.Msg, reqbuf []byte) (reqbody []byte, err error) {
	return reqbuf, nil
}

// ClientCodec is the codec for the grpc client, does nothing
type ClientCodec struct{}

// Encode is the encoder for the grpc client and does nothing
func (c *ClientCodec) Encode(_ codec.Msg, rspbody []byte) (buffer []byte, err error) {
	return rspbody, nil
}

// Decode is a decoder for the grpc client, does nothing
func (c *ClientCodec) Decode(_ codec.Msg, buffer []byte) (rspbody []byte, err error) {
	return buffer, nil
}
