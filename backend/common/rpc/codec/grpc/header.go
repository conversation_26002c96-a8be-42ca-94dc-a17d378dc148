package grpc

import (
	"context"
)

type contextHeader<PERSON>ey struct{}

type Header struct {
	Req         interface{}
	Rsp         interface{}
	InMetaData  map[string][]string
	OutMetaData map[string][]string
}

func WithHeader(ctx context.Context, header *Header) context.Context {
	return context.WithValue(ctx, contextHeader<PERSON>ey{}, header)
}

func GetHeader(ctx context.Context) *Header {
	header := ctx.Value(contextHeaderKey{}).(*Header) // nolint
	return header
}
