package grpc

import (
	"context"
	"strings"

	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/metadata"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/client"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/codec"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/errs"
)

func NewClient[T any](calleeName string,
	builder func(cc grpc.ClientConnInterface) T) T {
	return builder(&Client{
		calleeName: calleeName,
		c:          client.DefaultClient,
	})
}

type Client struct {
	calleeName               string
	c                        client.Client
	grpc.ClientConnInterface // TODO
}

func (c *Client) Invoke(ctx context.Context, method string, args any, reply any, _ ...grpc.CallOption) error {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)

	msg.WithCalleeServiceName(c.calleeName)
	msg.WithClientRPCName(method)

	index := strings.LastIndex(method, "/")
	if index < 0 {
		return errs.NewFrameError(codes.DataLoss, "client method name error")
	}
	methodName := method[index+1:]

	msg.WithCalleeMethod(methodName)

	header := &Header{
		Req:         args,
		Rsp:         reply,
		InMetaData:  metadata.MD{},
		OutMetaData: metadata.MD{}, // TODO: metadata in msg
	}
	ctx = WithHeader(ctx, header)

	return c.c.Invoke(ctx, args, reply) // TODO: grpc.CallOption
}
