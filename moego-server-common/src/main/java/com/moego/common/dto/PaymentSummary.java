package com.moego.common.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

@Data
public class PaymentSummary {

    private String module;
    private Integer invoiceId;
    private Integer customerId;
    private BigDecimal paidAmount = BigDecimal.ZERO;
    private BigDecimal refundedAmount = BigDecimal.ZERO;
    private BigDecimal processingFee = BigDecimal.ZERO;
    private List<PaymentDto> payments;
    private List<PaymentRefundDto> refunds;

    @Data
    public static class PaymentDto {

        private Integer id;
        private String method;
        private BigDecimal amount;

        private String paidBy;
        private String description;
        private String status;
        private Byte stripePaymentMethod;
        private String stripeIntentId;
        private String stripeClientSecret;
        private String stripeCustomerId;
        private String stripeAccountId;
        private Boolean isOnline;
        private Byte isDeposit;
        private Long createTime;
        private Long updateTime;
        private BigDecimal processingFee;

        @Schema(description = "1 - card, 2 - card on file, 3- terminal, 4 - reader sdk")
        private Byte squarePaymentMethod;

        @Schema(description = "values: Stripe, Square or empty string")
        private String vendor;

        private String merchant;

        @JsonInclude(JsonInclude.Include.NON_NULL)
        private String cardFunding;

        @JsonInclude(JsonInclude.Include.NON_NULL)
        private String checkNumber;

        @JsonInclude(JsonInclude.Include.NON_NULL)
        private String cardType;

        @JsonInclude(JsonInclude.Include.NON_NULL)
        private String cardNumber;

        @JsonInclude(JsonInclude.Include.NON_NULL)
        private String expMonth;

        @JsonInclude(JsonInclude.Include.NON_NULL)
        private String expYear;

        @JsonInclude(JsonInclude.Include.NON_NULL)
        private String signature;

        // create payment时，调用stripe接口返回status字段，用于前端是否需要3ds的判断
        @JsonInclude(JsonInclude.Include.NON_NULL)
        private String stripeStatus;

        // 是否与OB deposit记录关联，表示来自prepay
        private Boolean isPrepay;

        // order payment id
        private Long orderPaymentId;

        // 关联的 transaction id
        private Long transactionId;
    }

    @Data
    public static class PaymentRefundDto {

        private Integer id;
        private Integer refundId;
        private String module;
        private String method;
        private BigDecimal amount;
        private String status;
        private Integer methodId;
        private Integer originPaymentId;
        private String stripeRefundId;
        private String reason;
        private String error;

        private Long createTime;
        private Long updateTime;
        private Long refundOrderPaymentId;
    }
}
